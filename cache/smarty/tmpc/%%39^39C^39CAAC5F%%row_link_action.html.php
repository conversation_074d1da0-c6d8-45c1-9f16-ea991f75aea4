<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:37
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/row_link_action.html */ ?>
<?php echo ''; ?><?php $this->assign('object_onclick_handler', ''); ?><?php echo ''; ?><?php $this->assign('object_row_link_action', $this->_tpl_vars['object']->getTypeRowLinkAction()); ?><?php echo ''; ?><?php if ($this->_tpl_vars['object_row_link_action']): ?><?php echo ''; ?><?php echo ''; ?><?php if (preg_match ( '#\(.*\)#' , $this->_tpl_vars['object_row_link_action'] )): ?><?php echo ''; ?><?php echo ''; ?><?php $this->assign('object_onclick_handler', $this->_tpl_vars['object_row_link_action']); ?><?php echo ''; ?><?php elseif (preg_match ( '#&#' , $this->_tpl_vars['object_row_link_action'] )): ?><?php echo ''; ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'openHref(\''; ?><?php echo $this->_tpl_vars['object_row_link_action']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['link_target']; ?><?php echo '\', event);'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('object_onclick_handler', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['object_onclick_handler']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['object_row_link_action']): ?><?php echo ''; ?><?php $this->assign('object_row_link_action', $this->_tpl_vars['row_link_action']); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['object']->checkPermissions('view')): ?><?php echo ''; ?><?php $this->assign('default_row_link_action', 'view'); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('default_row_link_action', ''); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['object']->isDeleted()): ?><?php echo ''; ?><?php $this->assign('object_row_link_action', ''); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['object']->isTranslated()): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['object']->checkPermissions($this->_tpl_vars['row_link_action'])): ?><?php echo ''; ?><?php $this->assign('object_row_link_action', $this->_tpl_vars['default_row_link_action']); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php if ($this->_tpl_vars['object']->checkPermissions('translate')): ?><?php echo ''; ?><?php $this->assign('object_row_link_action', 'translate'); ?><?php echo ''; ?><?php ob_start(); ?><?php echo '&amp;model_lang='; ?><?php echo $this->_tpl_vars['object']->get('model_lang'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('object_row_link_model_lang_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('object_row_link_action', ''); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['object_row_link_action']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'openHref(\''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_string']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['object_row_link_action']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['object_row_link_action']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['object']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['object']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?><?php echo $this->_tpl_vars['object_row_link_model_lang_string']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['link_target']; ?><?php echo '\', event);'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('object_onclick_handler', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php if ($this->_tpl_vars['object_onclick_handler']): ?> 
  onclick="<?php echo $this->_tpl_vars['object_onclick_handler']; ?>
"
<?php endif; ?>