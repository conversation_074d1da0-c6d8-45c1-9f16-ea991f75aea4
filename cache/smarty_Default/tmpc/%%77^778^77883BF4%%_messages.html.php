<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_messages.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_messages.html', 4, false),)), $this); ?>
<div id="dashlet_messages_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
<?php echo ((is_array($_tmp=@$this->_tpl_vars['msg_suffix'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
" class="collapsible" style="position: absolute; z-index: 100001; width: <?php echo $this->_tpl_vars['width']; ?>
px; margin: <?php echo ((is_array($_tmp=@$this->_tpl_vars['margin_top'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
px <?php echo ((is_array($_tmp=@$this->_tpl_vars['margin_right'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
px 0 <?php echo ((is_array($_tmp=@$this->_tpl_vars['margin_left'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
px; padding: 0 5px 0 0; background: url('<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
close.png') no-repeat 100% 0 #ffffe0; border: 1px solid #cccccc; border-radius: 1em;<?php if (! $this->_tpl_vars['errors']): ?> display: none;<?php endif; ?>" onclick="plannedTime.updateMessagesPanel('', ('<?php echo $this->_tpl_vars['msg_suffix']; ?>
' ? 1 : 0));">
  <?php if ($this->_tpl_vars['errors']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."message.html", 'smarty_include_vars' => array('items' => $this->_tpl_vars['errors'],'display' => 'error')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <script type="text/javascript" defer="defer">
      setTimeout(function() { Effect.Fade($('dashlet_messages_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
<?php echo ((is_array($_tmp=@$this->_tpl_vars['msg_suffix'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
')); }, 5000);
    </script>
  <?php endif; ?>
</div>