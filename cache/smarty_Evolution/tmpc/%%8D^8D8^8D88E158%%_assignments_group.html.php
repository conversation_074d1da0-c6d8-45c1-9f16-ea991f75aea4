<?php /* Smarty version 2.6.33, created on 2025-05-27 12:01:49
         compiled from /var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assignments_group.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assignments_group.html', 5, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assignments_group.html', 5, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assignments_group.html', 10, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assignments_group.html', 14, false),)), $this); ?>
<table id="assignments_group_<?php echo $this->_tpl_vars['type']; ?>
" style="position: absolute; display: none; min-width: 250px; border: 1px solid #cccccc" cellspacing="0">
  <tr>
    <td class="t_caption3 drag" id="assignments_group_<?php echo $this->_tpl_vars['type']; ?>
_title">
      <?php ob_start(); ?>assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('labelPart', ob_get_contents());ob_end_clean(); ?>
      <div class="t_caption3_title floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/move.png" alt="" width="10" height="10" title="<?php echo $this->_config[0]['vars']['draggable']; ?>
" />&nbsp;<?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_assign_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['labelPart']])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
close_window.png" class="vtop pointer floatr" width="12" height="12" onclick="$('assignments_group_<?php echo $this->_tpl_vars['type']; ?>
').style.display = 'none';" alt="<?php echo $this->_config[0]['vars']['close']; ?>
" title="<?php echo $this->_config[0]['vars']['close']; ?>
" />
    </td>
  </tr>
  <tr>
    <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['users'] ) && is_array ( $this->_tpl_vars['users'] )): ?><?php echo count($this->_tpl_vars['users']); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('options_count', ob_get_contents());ob_end_clean(); ?>
      <?php if ($this->_tpl_vars['options_count'] < 3): ?>
        <?php $this->assign('height', '60'); ?>
      <?php elseif ($this->_tpl_vars['options_count'] < 10): ?>
        <?php echo smarty_function_math(array('assign' => 'height','equation' => 'x*20','x' => $this->_tpl_vars['options_count']), $this);?>

      <?php else: ?>
        <?php $this->assign('height', ''); ?>
      <?php endif; ?>
    <td style="padding: 0px;">
      <div class="scroll_box" id="container_<?php echo $this->_tpl_vars['type']; ?>
" style="background: #dddddd; border: none; width: 100%;<?php if ($this->_tpl_vars['height']): ?> height: <?php echo $this->_tpl_vars['height']; ?>
px;<?php endif; ?>">
      <?php $_from = $this->_tpl_vars['users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['u']):
?>
        <div id="container_<?php echo $this->_tpl_vars['type']; ?>
_<?php echo $this->_tpl_vars['u']['option_value']; ?>
" name="<?php echo $this->_tpl_vars['u']['label']; ?>
" portal="<?php echo $this->_tpl_vars['u']['is_portal']; ?>
">
          <input type="checkbox" value="<?php echo $this->_tpl_vars['u']['option_value']; ?>
" name="assignments_group_<?php echo $this->_tpl_vars['type']; ?>
[]" id="assignments_group_<?php echo $this->_tpl_vars['type']; ?>
_<?php echo $this->_tpl_vars['u']['option_value']; ?>
"/>
          <img style="margin-right: 5px;" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
user<?php if ($this->_tpl_vars['u']['inactive']): ?>s_inactive<?php elseif ($this->_tpl_vars['u']['is_portal']): ?>_portal<?php endif; ?>.png" alt="" />
          <label for="assignments_group_<?php echo $this->_tpl_vars['type']; ?>
_<?php echo $this->_tpl_vars['u']['option_value']; ?>
"><?php echo $this->_tpl_vars['u']['label']; ?>
</label>
        </div>
      <?php endforeach; endif; unset($_from); ?>
      </div>
    </td>
  </tr>
  <tr>
    <td style="border-top: 1px solid #cccccc; background: #dddddd">
      <button class="button" type="button" onclick="assignMultipleUsers('assignments_group_<?php echo $this->_tpl_vars['type']; ?>
');"><?php echo $this->_config[0]['vars']['assign']; ?>
</button>
    </td>
  </tr>
</table>
<script type="text/javascript">
  new Draggable('assignments_group_<?php echo $this->_tpl_vars['type']; ?>
', {handle: 'assignments_group_<?php echo $this->_tpl_vars['type']; ?>
_title'});
</script>