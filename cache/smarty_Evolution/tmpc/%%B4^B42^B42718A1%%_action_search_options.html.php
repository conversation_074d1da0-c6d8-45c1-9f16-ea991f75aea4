<?php /* Smarty version 2.6.33, created on 2025-05-21 16:49:37
         compiled from _action_search_options.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_action_search_options.html', 1, false),array('modifier', 'escape', '_action_search_options.html', 23, false),array('function', 'counter', '_action_search_options.html', 56, false),)), $this); ?>
<?php if (( ! $_COOKIE['search_type'] || $_COOKIE['search_type'] == 'simple' ) && ! ((is_array($_tmp=@$this->_tpl_vars['inner_search'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0))): ?>
  <?php $this->assign('search_type', 'simple'); ?>
<?php else: ?>
  <?php $this->assign('search_type', 'advanced'); ?>
<?php endif; ?>
<div class="nz-action_search_options<?php if ($this->_tpl_vars['search_type'] == 'advanced'): ?> nz--advanced<?php endif; ?>">
<?php if ($this->_tpl_vars['params']['real_module']): ?>
  <?php $this->assign('params_module', $this->_tpl_vars['module']); ?>
  <?php $this->assign('params_controller', $this->_tpl_vars['controller']); ?>
  <?php $this->assign('params_container_prefix', $this->_tpl_vars['params']['real_module']); ?>
  <?php $this->assign('module', $this->_tpl_vars['params']['real_module']); ?>
  <?php $this->assign('controller', $this->_tpl_vars['params']['real_controller']); ?>
<?php elseif ($this->_tpl_vars['params']['module']): ?>
  <?php $this->assign('params_module', $this->_tpl_vars['params']['module']); ?>
  <?php $this->assign('params_controller', $this->_tpl_vars['params']['controller']); ?>
  <?php $this->assign('params_container_prefix', $this->_tpl_vars['module']); ?>
<?php endif; ?>

<?php if (! $this->_tpl_vars['inner_search']): ?>
  <table border="0" cellpadding="3" cellspacing="3">
    <tr>
      <td colspan="3" width="450">
        <span class="strong" id="search_simple_switch"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['search_simple'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <span class="legend pointer" onclick="toggleSearchType(this.parentNode);<?php if ($this->_tpl_vars['available_action']['name'] == 'filter'): ?> scalePopup();<?php endif; ?>">(<?php echo ((is_array($_tmp=$this->_config[0]['vars']['search_show_advanced'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)</span></span>
        <span class="strong" id="search_advanced_switch"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['search_advanced'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <span class="legend pointer" onclick="toggleSearchType(this.parentNode);<?php if ($this->_tpl_vars['available_action']['name'] == 'filter'): ?> scalePopup();<?php endif; ?>">(<?php echo ((is_array($_tmp=$this->_config[0]['vars']['search_show_simple'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)</span></span>
      </td>
    </tr>
  </table>
<?php else: ?>
  <?php $this->assign('search_type', 'advanced'); ?>
<?php endif; ?>

<?php if ($this->_tpl_vars['search_type'] == 'simple' || $this->_tpl_vars['view_mode']): ?>
  <?php $this->assign('disabled', 1); ?>
<?php else: ?>
  <?php $this->assign('disabled', 0); ?>
<?php endif; ?>

<!-- JS arrays for manipulation of the search options -->
<script type="text/javascript">
    search_additional_vars_switch = '<?php echo $this->_tpl_vars['switch_additional']; ?>
';
    advanced_search_obj = <?php echo $this->_tpl_vars['advanced_search_options']; ?>
;
    additional_search_obj = <?php echo $this->_tpl_vars['additional_search_options']; ?>
;
</script>

<!-- ADVANCED SEARCH -->
<div id="search_advanced_container" class="search_container">
  <input type="hidden" value="" id="filters_action" name="filters_action" <?php if ($this->_tpl_vars['disabled']): ?>disabled="disabled" <?php endif; ?>/>
  <div class="t_caption3_title t_caption3" style="padding: 4px;">
    <?php echo $this->_config[0]['vars']['search_filters']; ?>

  </div>
  <div class="search_advanced_filters">
    <table border="0" cellpadding="5" cellspacing="0" id="search_container" class="t_borderless">
      <tr id="search_headers" style="display: none;">
        <td></td>
      </tr>
      <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false,'assign' => 'current_item'), $this);?>

      <?php $_from = $this->_tpl_vars['session_filters']['search_fields']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['filter']):
        $this->_foreach['i']['iteration']++;
?>
            <?php $this->assign('present', 0); ?>
      <?php $_from = $this->_tpl_vars['search_fields']['basic_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
         <?php if ($this->_tpl_vars['opt']['option_value'] == $this->_tpl_vars['filter']): ?><?php $this->assign('present', 1); ?><?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
      <?php if (! $this->_tpl_vars['present']): ?>
        <?php $_from = $this->_tpl_vars['search_fields']['additional_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
          <?php if ($this->_tpl_vars['opt']['option_value'] == $this->_tpl_vars['filter']): ?><?php $this->assign('present', 1); ?><?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['present']): ?>
        <?php echo smarty_function_counter(array('name' => 'item_counter','print' => false), $this);?>

        <?php if (! preg_match ( '#a__#' , $this->_tpl_vars['filter'] )): ?>
          <?php $this->assign('search_defs', $this->_tpl_vars['search_fields']['basic_vars']); ?>
        <?php else: ?>
          <?php $this->assign('search_defs', $this->_tpl_vars['search_fields']['additional_vars']); ?>
        <?php endif; ?>
        <tr id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
" class="nz-advancedSearch-filter">
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_0">
            <?php if ($this->_tpl_vars['view_mode']): ?>
              <?php echo $this->_tpl_vars['current_item']; ?>

            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="processSearchDef('hide', '<?php echo $this->_tpl_vars['current_item']; ?>
');" />
              <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '<?php echo $this->_tpl_vars['current_item']; ?>
');" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['current_item']; ?>
</a>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_1" style="width: 150px;">
          <?php if ($this->_tpl_vars['additional_search_options'] == 'false' || $this->_tpl_vars['additional_search_options'] == '[]'): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_id' => 'search_fields','index' => $this->_tpl_vars['current_item'],'sequences' => 'setSearchDef(this);','value' => $this->_tpl_vars['filter'],'width' => '150','optgroup_label_source' => 'config','options' => $this->_tpl_vars['search_fields']['basic_vars'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php else: ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_id' => 'search_fields','index' => $this->_tpl_vars['current_item'],'sequences' => 'setSearchDef(this);','value' => $this->_tpl_vars['filter'],'width' => '150','optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['search_fields'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('name' => 'search_fields_prev','standalone' => true,'custom_id' => 'search_fields_prev','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['filter'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_2" style="width: 150px;">
            <?php $this->assign('selected_compare', $this->_tpl_vars['session_filters']['compare_options'][$this->_tpl_vars['key']]); ?>
            <?php echo smarty_function_counter(array('assign' => 'options_count','start' => 0,'print' => false), $this);?>

            <?php $_from = $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt_group'] => $this->_tpl_vars['opt_groups']):
?>
              <?php $_from = $this->_tpl_vars['opt_groups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
                <?php echo smarty_function_counter(array('print' => false), $this);?>

                <?php if ($this->_tpl_vars['selected_compare'] == $this->_tpl_vars['option']['option_value']): ?>
                  <?php $this->assign('values_operator_group', $this->_tpl_vars['opt_group']); ?>
                  <?php $this->assign('values_operator', $this->_tpl_vars['idx']); ?>
                  <?php $this->assign('next_input', $this->_tpl_vars['option']['itype']); ?>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endforeach; endif; unset($_from); ?>
            <?php if ($this->_tpl_vars['options_count'] == 1 || preg_match ( '/\.((search_)?archive|deleted)$/' , $this->_tpl_vars['filter'] ) || preg_match ( '/^(fp\.annulled|(fir|fer)\.active)$/' , $this->_tpl_vars['filter'] )): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => 'compare_options','custom_id' => 'compare_options','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['selected_compare'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'compare_options','custom_id' => 'compare_options','index' => $this->_tpl_vars['current_item'],'sequences' => 'setSearchDef(this);','value' => $this->_tpl_vars['selected_compare'],'width' => '150','optgroups' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_3" style="white-space: nowrap!important; width: 205px;">
            <?php if ($this->_tpl_vars['next_input'] == 'autocompleter'): ?>
              <?php $this->assign('restrict', ''); ?>
            <?php elseif ($this->_tpl_vars['next_input'] == 'date'): ?>
              <?php $this->assign('searchable', 1); ?>
              <?php $this->assign('restrict', ''); ?>
            <?php elseif ($this->_tpl_vars['next_input'] == 'number'): ?>
              <?php $this->assign('searchable', 0); ?>
              <?php $this->assign('next_input', 'text'); ?>
              <?php $this->assign('restrict', 'insertOnlyReals'); ?>
            <?php else: ?>
              <?php $this->assign('restrict', ''); ?>
              <?php $this->assign('searchable', 0); ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['filter'] == $this->_tpl_vars['switch_additional'] || preg_match ( '#\.type$#' , $this->_tpl_vars['filter'] )): ?>
              <?php $this->assign('sequences', "setSearchDef(this);"); ?>
            <?php else: ?>
              <?php $this->assign('sequences', ''); ?>
            <?php endif; ?>
            <?php if (isset ( $this->_tpl_vars['session_filters']['values_code'][$this->_tpl_vars['key']] ) || isset ( $this->_tpl_vars['session_filters']['values_autocomplete'][$this->_tpl_vars['key']] )): ?>
              <?php $this->assign('autocomplete_var_type', 'basic'); ?>
            <?php else: ?>
              <?php $this->assign('autocomplete_var_type', 'searchable'); ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['next_input']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['next_input']).".html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'searchable' => $this->_tpl_vars['searchable'],'disabled' => $this->_tpl_vars['disabled'],'show_calendar_icon' => true,'index' => $this->_tpl_vars['current_item'],'name' => 'values','custom_id' => 'values','width' => '200','sequences' => $this->_tpl_vars['sequences'],'hide_calendar_icon' => 1,'restrict' => $this->_tpl_vars['restrict'],'do_not_escape_labels' => true,'value' => $this->_tpl_vars['session_filters']['values'][$this->_tpl_vars['key']],'value_autocomplete' => $this->_tpl_vars['session_filters']['values_autocomplete'][$this->_tpl_vars['key']],'value_code' => $this->_tpl_vars['session_filters']['values_code'][$this->_tpl_vars['key']],'value_name' => $this->_tpl_vars['session_filters']['values_name'][$this->_tpl_vars['key']],'value_date_period' => ((is_array($_tmp=$this->_tpl_vars['session_filters']['date_period'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'autocomplete_var_type' => $this->_tpl_vars['autocomplete_var_type'],'autocomplete' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['options'],'options' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['options'],'optgroups' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['opt_groups'],'show_inactive_options' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_4" style="width: 30px;">
          <?php if ($this->_foreach['i']['iteration'] == ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?>
            &nbsp;
          <?php else: ?>
            <select id="logical_operator_<?php echo $this->_tpl_vars['current_item']; ?>
" name="logical_operator[<?php echo $this->_tpl_vars['current_item']-1; ?>
]"
                    class="selbox short" onfocus="highlight(this)" onblur="unhighlight(this)"
                    <?php if ($this->_tpl_vars['disabled']): ?>disabled="disabled"<?php endif; ?>>
              <option value="AND" <?php if ($this->_tpl_vars['session_filters']['logical_operator'][$this->_tpl_vars['key']] == 'AND'): ?>selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['and']; ?>
</option>
              <option value="OR" <?php if ($this->_tpl_vars['session_filters']['logical_operator'][$this->_tpl_vars['key']] == 'OR'): ?>selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['or']; ?>
</option>
            </select>
          <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_5">
            <?php if ($this->_tpl_vars['current_item'] == 1 && ! $this->_tpl_vars['view_mode']): ?>
            <div class="t_buttons">
              <div id="search_container_plusButton" onclick="processSearchDef('add');" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_filter']; ?>
"><div class="t_plus"></div></div>
              <div id="search_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="processSearchDef('remove');" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_filter']; ?>
"><div class="t_minus"></div></div>
            </div>
            <?php endif; ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
      <tr id="search_container_1" class="nz-advancedSearch-filter">
        <td id="search_container_1_0">
          <?php if ($this->_tpl_vars['view_mode']): ?>
            1
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" style="visibility: hidden;" onclick="processSearchDef('hide', '1');" />
            <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '1');" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">1</a>
          <?php endif; ?>
        </td>
        <td id="search_container_1_1" style="width: 150px;">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_id' => 'search_fields','index' => 1,'sequences' => 'setSearchDef(this);','value' => '','options' => $this->_tpl_vars['search_fields']['basic_vars'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'search_fields_prev','custom_id' => 'search_fields_prev','index' => 1,'value' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
        <td id="search_container_1_2" style="width: 150px;">
        </td>
        <td id="search_container_1_3" style="white-space: nowrap!important; width: 205px;">
        </td>
        <td id="search_container_1_4" style="width: 30px;">
        </td>
        <td id="search_container_1_5">
          <?php if (! $this->_tpl_vars['view_mode']): ?>
          <div class="t_buttons">
            <div id="search_container_plusButton" onclick="processSearchDef('add');" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_filter']; ?>
"><div class="t_plus"></div></div>
            <div id="search_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="processSearchDef('remove');" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_filter']; ?>
"><div class="t_minus"></div></div>
          </div>
          <?php endif; ?>
        </td>
      </tr>
      <?php endif; unset($_from); ?>
    </table>
  </div>

  <!-- SORT, SAVED FILTERS, RPP -->
  <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
    <tr>
      <td style="min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px;">
          <?php echo $this->_config[0]['vars']['sort']; ?>

          <?php if (! $this->_tpl_vars['view_mode']): ?>
          <div class="t_buttons">
            <div id="search_sort_container_plusButton" onclick="addSortCondition('search_sort_container',1)" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_sort']; ?>
"><div class="t_plus"></div></div>
            <div id="search_sort_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['sort'] ) || count ( $this->_tpl_vars['session_filters']['sort'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeSortCondition('search_sort_container')" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_sort']; ?>
"><div class="t_minus"></div></div>
          </div>
          <?php endif; ?>
        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_sort_container">
          <tr>
            <?php $_from = $this->_tpl_vars['session_filters']['sort']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ii'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ii']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['sort']):
        $this->_foreach['ii']['iteration']++;
?>
            <td style="width: 100px;<?php if ($this->_foreach['ii']['iteration'] != ($this->_foreach['ii']['iteration'] <= 1)): ?> border-left: solid 1px #AAAAAA;<?php endif; ?>">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'sort','custom_id' => 'sort','index' => $this->_foreach['ii']['iteration'],'sequences' => '','value' => $this->_tpl_vars['sort'],'optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['system_fields']['sort'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <?php endforeach; else: ?>
            <td style="width: 100px;">
            <?php ob_start(); ?><?php echo $this->_tpl_vars['alias']; ?>
.added<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_sort', ob_get_contents());ob_end_clean(); ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'sort','custom_id' => 'sort','index' => 1,'sequences' => '','value' => $this->_tpl_vars['default_sort'],'optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['system_fields']['sort'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <?php endif; unset($_from); ?>
          </tr>
          <tr>
            <?php $_from = $this->_tpl_vars['session_filters']['order']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ii'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ii']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['order']):
        $this->_foreach['ii']['iteration']++;
?>
            <td style="width: 100px;<?php if ($this->_foreach['ii']['iteration'] != ($this->_foreach['ii']['iteration'] <= 1)): ?> border-left: solid 1px #AAAAAA;<?php endif; ?>">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'order','custom_id' => 'order','index' => $this->_foreach['ii']['iteration'],'sequences' => '','value' => $this->_tpl_vars['order'],'options' => $this->_tpl_vars['system_fields']['order'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <?php endforeach; else: ?>
            <td style="width: 100px;">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'order','custom_id' => 'order','index' => 1,'sequences' => '','value' => '','options' => $this->_tpl_vars['system_fields']['order'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <?php endif; unset($_from); ?>
          </tr>
        </table>
      </td>
      <?php if (! $this->_tpl_vars['view_mode']): ?>
      <td style="border-left: 1px solid #AAAAAA; min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px; white-space: nowrap;">
          <?php echo $this->_config[0]['vars']['save_load_filters']; ?>

        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_save_container">
          <tr>
            <td nowrap="nowrap"><label for="save_filter_as"><?php echo $this->_config[0]['vars']['save_search']; ?>
:</label></td>
            <td style="width: 200px;">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_combobox.html", 'smarty_include_vars' => array('disabled' => $this->_tpl_vars['disabled'],'standalone' => true,'name' => 'save_filter_as','custom_id' => 'save_filter_as','value' => '','sequences' => '','width' => '200','options' => $this->_tpl_vars['saved_filters'],'label' => $this->_config[0]['vars']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <td class="nowrap">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('disabled' => $this->_tpl_vars['disabled'],'standalone' => true,'name' => 'save_as_action','custom_id' => 'save_as_action','option_value' => '1','label' => $this->_config[0]['vars']['save_as_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <td>
              <a href="javascript: void(0);" onclick="filterActions($('save_filter_as').form, 'savefilter');">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/download.png" id="save_filter" name="save_filter" class="pointer" width="14" height="14" alt="<?php echo $this->_config[0]['vars']['save']; ?>
" title="<?php echo $this->_config[0]['vars']['save']; ?>
" />
              </a>
            </td>
          </tr>
          <tr>
            <td nowrap="nowrap"><label for="filter_name"><?php echo $this->_config[0]['vars']['load_search']; ?>
:</label></td>
            <td style="width: 200px;">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => 'filter_name','custom_id' => 'filter_name','value' => '','sequences' => '','width' => '200','options' => $this->_tpl_vars['saved_filters'],'label' => $this->_config[0]['vars']['filter'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
            <td nowrap="nowrap" colspan="2">
              <a href="javascript: void(0);"
              <?php if (! $this->_tpl_vars['inner_search']): ?>
                onclick="filterActions($('filter_name').form, 'loadfilter', 'filter_name');"
              <?php else: ?>
                onclick="getActionOptions('<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_container_prefix'])) ? $this->_run_mod_handler('default', true, $_tmp, 'td') : smarty_modifier_default($_tmp, 'td')); ?>
_search_options', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_module'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['module']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['module'])); ?>
', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_controller'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['controller']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['controller'])); ?>
', 'filters_ajax_load', 0, {real_module: '<?php echo $this->_tpl_vars['module']; ?>
', real_controller: '<?php echo $this->_tpl_vars['controller']; ?>
'});"
              <?php endif; ?>
              >
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/reload.png" id="load_filter" name="load_filter" class="pointer" width="12" height="12" alt="<?php echo $this->_config[0]['vars']['config_load']; ?>
" title="<?php echo $this->_config[0]['vars']['config_load']; ?>
" />
              </a>
              <a href="javascript: void(0);" onclick="filterActions($('filter_name').form, 'deletefilter', 'filter_name');">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" id="delete_filter" name="delete_filter" class="pointer" width="12" height="12" alt="<?php echo $this->_config[0]['vars']['delete']; ?>
" title="<?php echo $this->_config[0]['vars']['delete']; ?>
" />
              </a>
            </td>
          </tr>
        </table>
      </td>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['module'] != 'dashlets' || ! $this->_tpl_vars['params_module']): ?>
      <td style="border-left: 1px solid #AAAAAA; min-width: 33%; vertical-align: top;">
        <div class="t_caption3_title t_caption3" style="padding: 4px; white-space: nowrap;">
          <?php echo $this->_config[0]['vars']['display_rpp']; ?>

        </div>
        <table border="0" cellpadding="5" cellspacing="0" id="search_display_container">
          <tr>
            <td><?php echo $this->_config[0]['vars']['display']; ?>
:</td>
            <td>
              <?php ob_start(); ?>list_<?php if ($this->_tpl_vars['module'] != $this->_tpl_vars['controller']): ?><?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['module']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_name', ob_get_contents());ob_end_clean(); ?>
              <?php $this->assign('default_rpp', $this->_tpl_vars['currentUser']->getPersonalSettings('interface',$this->_tpl_vars['var_name'])); ?>
              <?php if (empty ( $this->_tpl_vars['default_rpp'] )): ?><?php $this->assign('default_rpp', ''); ?><?php endif; ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'display','custom_id' => 'display','value' => ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['session_filters']['display'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_rpp']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_rpp'])))) ? $this->_run_mod_handler('default', true, $_tmp, '10') : smarty_modifier_default($_tmp, '10')),'sequences' => '','options' => $this->_tpl_vars['system_fields']['display'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
          </tr>
          <tr>
            <td style="height: 26px;" colspan="2">&nbsp;</td>
          </tr>
        </table>
      </td>
      <?php endif; ?>
    </tr>
  </table>
  <div style="height: 10px; border-top: 1px solid #AAAAAA;"></div>
</div>
<!--  END ADVANCED SEARCH -->
<?php if (! $this->_tpl_vars['inner_search']): ?>
<!--  SIMPLE SEARCH -->
<div id="search_simple_container" class="search_container">
<?php ob_start(); ?><?php if ($this->_tpl_vars['search_type'] == 'advanced'): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('disabled', ob_get_contents());ob_end_clean(); ?>
<!-- Hidden search filters (if any) -->
<?php $_from = $this->_tpl_vars['additional_hidden_filters']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['hidden_filter']):
?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => $this->_tpl_vars['hidden_filter']['name'],'custom_id' => $this->_tpl_vars['hidden_filter']['name'],'value' => $this->_tpl_vars['hidden_filter']['value'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endforeach; endif; unset($_from); ?>
<table border="0" cellpadding="3" cellspacing="3">
  <tr>
    <td><?php echo $this->_config[0]['vars']['key']; ?>
:</td>
    <td style="width: 200px;">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_text.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'show_calendar_icon' => false,'name' => 'key','custom_id' => 'key','value' => $this->_tpl_vars['session_filters']['key'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <td><?php echo $this->_config[0]['vars']['field']; ?>
:</td>
    <td style="width: 200px;">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'show_calendar_icon' => false,'name' => 'field','custom_id' => 'field','value' => $this->_tpl_vars['session_filters']['field'],'options' => $this->_tpl_vars['simple_search_defs'],'sequences' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td><?php echo $this->_config[0]['vars']['load_search']; ?>
:</td>
    <td style="width: 200px;">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => 'filter_name','custom_id' => 'filter_name_simple','value' => '','sequences' => '','width' => '200','options' => $this->_tpl_vars['saved_filters'],'label' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <td>
      <a href="javascript: void(0);"
      <?php if (! $this->_tpl_vars['inner_search']): ?>
        onclick="filterActions($('filter_name_simple').form, 'loadfilter', 'filter_name_simple');" alt="<?php echo $this->_config[0]['vars']['config_load']; ?>
" title="<?php echo $this->_config[0]['vars']['config_load']; ?>
"
      <?php else: ?>
        onclick="getActionOptions('<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_container_prefix'])) ? $this->_run_mod_handler('default', true, $_tmp, 'td') : smarty_modifier_default($_tmp, 'td')); ?>
_search_options', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_module'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['module']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['module'])); ?>
', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['params_controller'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['controller']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['controller'])); ?>
', 'filters_ajax_load', 0, {real_module: '<?php echo $this->_tpl_vars['module']; ?>
', real_controller: '<?php echo $this->_tpl_vars['controller']; ?>
'});"
      <?php endif; ?>
      >
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/reload.png" id="load_filter" name="load_filter" class="pointer" width="12" height="12" />
      </a>
      <a href="javascript: void(0);" onclick="filterActions($('filter_name_simple').form, 'deletefilter', 'filter_name_simple');">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" id="delete_filter" name="delete_filter" class="pointer" width="12" height="12" alt="<?php echo $this->_config[0]['vars']['delete']; ?>
" title="<?php echo $this->_config[0]['vars']['delete']; ?>
" />
      </a>
    </td>
    <td>&nbsp;</td>
  </tr>
</table>
</div>
<!-- END SIMPLE SEARCH -->

<table border="0" cellpadding="3" cellspacing="3">
  <tr>
    <td colspan="3">
      <?php echo '<button type="submit" class="button" name="'; ?><?php echo $this->_tpl_vars['available_action']['name']; ?><?php echo 'Go" id="'; ?><?php echo $this->_tpl_vars['available_action']['name']; ?><?php echo 'Go1" title="'; ?><?php echo $this->_tpl_vars['available_action']['label']; ?><?php echo '"'; ?><?php if ($this->_tpl_vars['available_action']['confirm']): ?><?php echo ' onclick="return confirmAction(\''; ?><?php echo $this->_tpl_vars['available_action']['name']; ?><?php echo '\', submitForm, this);"'; ?><?php endif; ?><?php echo '>'; ?><?php echo $this->_tpl_vars['available_action']['label']; ?><?php echo '</button>'; ?><?php if ($this->_tpl_vars['available_action']['name'] == 'search' || $this->_tpl_vars['available_action']['name'] == 'filter'): ?><?php echo '<button type="button" class="button" onclick="if (\''; ?><?php echo $this->_tpl_vars['available_action']['ajax_no']; ?><?php echo '\' != \'1\') '; ?>{<?php echo 'getActionOptions(\'td_'; ?><?php echo $this->_tpl_vars['available_action']['name']; ?><?php echo '_options\', \''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['available_action']['name']; ?><?php echo '\', '; ?><?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?><?php echo '\''; ?><?php echo $this->_tpl_vars['model']->get('id'); ?><?php echo '\''; ?><?php else: ?><?php echo '0'; ?><?php endif; ?><?php echo ', '; ?>{<?php echo 'clear_flag: 1'; ?>}<?php echo ');'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['clear_filters'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?>

    </td>
  </tr>
</table>
<?php endif; ?>
</div>