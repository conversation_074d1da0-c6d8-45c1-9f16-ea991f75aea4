<?php /* Smarty version 2.6.33, created on 2025-05-27 12:23:23
         compiled from _timesheets_info.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_timesheets_info.html', 4, false),array('modifier', 'date_format', '_timesheets_info.html', 12, false),array('modifier', 'strip_tags', '_timesheets_info.html', 14, false),array('modifier', 'mb_truncate', '_timesheets_info.html', 14, false),array('modifier', 'nl2br', '_timesheets_info.html', 14, false),array('modifier', 'url2href', '_timesheets_info.html', 14, false),array('function', 'cycle', '_timesheets_info.html', 10, false),)), $this); ?>
<table cellpadding="0" cellspacing="0" border="0" class="attachments t_grouping_table t_table hleft" style="border: 1px solid #999999; z-index: 10000; width: 300px; margin: 0px;">
  <tr>
    <th class="nowrap">
      <a title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $_REQUEST['real_module']; ?>
<?php if ($_REQUEST['real_controller'] != $_REQUEST['real_module']): ?>&amp;controller=<?php echo $_REQUEST['real_controller']; ?>
<?php endif; ?>&amp;<?php echo $_REQUEST['real_controller']; ?>
=timesheets&amp;timesheets=<?php echo $_REQUEST['model_id']; ?>
<?php if ($_REQUEST['archive']): ?>&amp;archive=1<?php endif; ?>"><span class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_timesheets_last_records_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span></a>
    </th>
  </tr>

<?php $_from = $this->_tpl_vars['timesheets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['timesheet']):
        $this->_foreach['ti']['iteration']++;
?>
  <?php if (($this->_foreach['ti']['iteration'] <= 1)): ?><?php ob_start(); ?>last_records_<?php echo $this->_tpl_vars['timesheet']->modelName; ?>
_<?php echo $this->_tpl_vars['timesheet']->get('task_id'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('cycle_name', ob_get_contents());ob_end_clean(); ?><?php endif; ?>
  <tr class="<?php echo smarty_function_cycle(array('name' => $this->_tpl_vars['cycle_name'],'values' => 't_odd,t_even'), $this);?>
">
    <td class="t_border t_bottom_border" style="color: #000000;">
      <?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('user_id_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 (<?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('startperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
<?php if (((is_array($_tmp=$this->_tpl_vars['timesheet']->get('startperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short'])) != ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('endperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?>-<?php echo ((is_array($_tmp=$this->_tpl_vars['timesheet']->get('endperiod'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
<?php endif; ?>, <?php echo $this->_tpl_vars['timesheet']->get('duration'); ?>
 <?php if ($this->_tpl_vars['timesheet']->get('duration') == 1): ?><?php echo $this->_config[0]['vars']['minute']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['minutes']; ?>
<?php endif; ?>)<br />
      <?php if ($this->_tpl_vars['timesheet']->get('subject')): ?><span class="strong"><?php echo $this->_tpl_vars['timesheet']->get('subject'); ?>
</span><br /><?php endif; ?>
      <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['timesheet']->get('content'))) ? $this->_run_mod_handler('strip_tags', true, $_tmp) : smarty_modifier_strip_tags($_tmp)))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 130) : smarty_modifier_mb_truncate($_tmp, 130)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

          </td>
  </tr>
<?php endforeach; else: ?>
  <tr>
    <td class="t_border t_bottom_border" style="color: #FF0000;">
      <?php if ($this->_tpl_vars['no_permissions']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['timesheets_no_permissions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
    </td>
  </tr>
<?php endif; unset($_from); ?>
</table>