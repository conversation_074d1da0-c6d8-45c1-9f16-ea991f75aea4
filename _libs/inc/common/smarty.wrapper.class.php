<?php

require_once PH_SMARTY_DIR . 'Smarty.class.php';

Class SmartyWrapper Extends Smarty {

    private $_params = array();

    /**
    * default init values
    */
    const CACHING       = false;
    const COMPILE_CHECK = true;
    const DEBUGGING     = false;
    const FORCE_COMPILE = false;
    const BOOLEANIZE    = false;

    public function __construct($params = array()) {

        parent::__construct();
        $this->_params = $params;

        $this->_init();
    }

    private function _init($params = array()) {

        //set caching
        if (isset($this->_params['caching'])) {
            $this->caching = $this->_params['caching'];
        } else {
            $this->caching = self::CACHING;
        }

        //set compile_check
        if (isset($this->_params['compile_check'])) {
            $this->compile_check = $this->_params['compile_check'];
        } else {
            $this->compile_check = self::COMPILE_CHECK;
        }

        //set debugging mode
        if (isset($this->_params['debugging'])) {
            $this->debugging = $this->_params['debugging'];
        } else {
            $this->debugging = self::DEBUGGING;
        }

        //force compile each time (if needed)
        if (isset($this->_params['force_compile'])) {
            $this->force_compile = $this->_params['force_compile'];
        } else {
            $this->force_compile = self::FORCE_COMPILE;
        }

        //booleanize the config files
        if (isset($this->_params['booleanize'])) {
            $this->config_booleanize = $this->_params['booleanize'];
        } else {
            $this->config_booleanize = self::BOOLEANIZE;
        }

        //set directories
        if (isset($this->_params['template_dir'])) {
            $this->template_dir = $this->_params['template_dir'];
        }
        if (isset($this->_params['compile_dir'])) {
            $this->compile_dir = $this->_params['compile_dir'];
        }
        if (isset($this->_params['config_dir'])) {
            $this->config_dir = $this->_params['config_dir'];
        }
        if (isset($this->_params['cache_dir'])) {
            $this->compile_dir = $this->_params['cache_dir'];
        }

        //create directories
        if (!file_exists($this->compile_dir)) {
            //create compiled templates dir recursively and set enough permission
            @mkdir($this->compile_dir, 0777, true);
        }
        if ($this->caching && !file_exists($this->cache_dir)) {
            //create cache dir (if needed) recursively and set enough permission
            @mkdir($this->cache_dir, 0777, true);
        }

        // add folder with some custom made plugins
        //$this->plugins_dir[] = SMARTY_DIR . $this->plugins_dir[0] . '/custom';

        //we add the folder with custom made plugins first
        //to be able to override smarty plugins
        //for more info see here: http://www.smarty.net/docsv2/en/variable.plugins.dir.tpl
        array_unshift($this->plugins_dir, SMARTY_DIR . $this->plugins_dir[0] . '/custom');

    }

}

?>
