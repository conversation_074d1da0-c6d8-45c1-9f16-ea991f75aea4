<?php
/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */


/**
 * Smarty {overlib_init} function plugin
 *
 * Type:     function<br>
 * Name:     overlib_init<br>
 * Purpose:  initialize overlib
 * @link http://smarty.php.net/manual/en/language.function.popup.init.php {popup_init}
 *          (Smarty online manual)
 * <AUTHOR> <monte at ohrt dot com>
 * @param array
 * @return string
 */
function smarty_function_overlib_init($params, &$smarty)
{
    $zindex = 100001;
    
    if (!empty($params['zindex'])) {
        $zindex = $params['zindex'];
    }
    
    if (!empty($params['src'])) {
        return '<div id="overDiv" style="position:absolute; visibility:hidden; z-index:'.$zindex.';"></div>' . "\n"
         . '<script type="text/javascript" language="JavaScript" src="'.$params['src'].'overlib.js"></script>' . "\n"
         . '<script type="text/javascript" src="'.$params['src'].'overlib_hideform.js"></script>' . "\n";
    } else {
        $smarty->trigger_error("overlib_init: missing src parameter");
    }
}

/* vim: set expandtab: */

?>
