<?php

namespace Tests\Nzoom\TestHelpers;

/**
 * Test helper for mocking General class functionality
 */
class GeneralTestHelper
{
    private static bool $mockEnabled = false;
    private static array $logEntries = [];

    /**
     * Enable General class mocking
     */
    public static function enableMocking(): void
    {
        self::$mockEnabled = true;
        self::$logEntries = [];
    }

    /**
     * Disable General class mocking
     */
    public static function disableMocking(): void
    {
        self::$mockEnabled = false;
        self::$logEntries = [];
    }

    /**
     * Check if mocking is enabled
     */
    public static function isMockingEnabled(): bool
    {
        return self::$mockEnabled;
    }

    /**
     * Mock implementation of General::log()
     */
    public static function log($registry, string $event, string $extra = ''): bool
    {
        if (!self::$mockEnabled) {
            // If mocking is not enabled, call the real General::log method
            return \General::log($registry, $event, $extra);
        }

        self::$logEntries[] = [
            'registry' => $registry,
            'event' => $event,
            'extra' => $extra,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return true;
    }

    /**
     * Clear all captured log entries
     */
    public static function clearLogs(): void
    {
        self::$logEntries = [];
    }

    /**
     * Get all captured log entries
     */
    public static function getLogs(): array
    {
        return self::$logEntries;
    }

    /**
     * Get log entries by event type
     */
    public static function getLogsByEvent(string $event): array
    {
        return array_filter(self::$logEntries, function($entry) use ($event) {
            return $entry['event'] === $event;
        });
    }

    /**
     * Check if a specific event was logged
     */
    public static function hasEvent(string $event): bool
    {
        return !empty(self::getLogsByEvent($event));
    }

    /**
     * Get the last log entry
     */
    public static function getLastLog(): ?array
    {
        return empty(self::$logEntries) ? null : end(self::$logEntries);
    }

    /**
     * Get count of log entries
     */
    public static function getLogCount(): int
    {
        return count(self::$logEntries);
    }

    /**
     * Assert that a specific event was logged
     */
    public static function assertEventLogged(string $event, string $message = ''): void
    {
        if (!self::hasEvent($event)) {
            throw new \PHPUnit\Framework\AssertionFailedError(
                $message ?: "Expected event '{$event}' to be logged, but it was not found."
            );
        }
    }

    /**
     * Assert that a specific event was not logged
     */
    public static function assertEventNotLogged(string $event, string $message = ''): void
    {
        if (self::hasEvent($event)) {
            throw new \PHPUnit\Framework\AssertionFailedError(
                $message ?: "Expected event '{$event}' not to be logged, but it was found."
            );
        }
    }

    /**
     * Assert that a log entry contains specific text
     */
    public static function assertLogContains(string $text, string $message = ''): void
    {
        $found = false;
        foreach (self::$logEntries as $entry) {
            if (strpos($entry['extra'], $text) !== false) {
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            throw new \PHPUnit\Framework\AssertionFailedError(
                $message ?: "Expected log entries to contain '{$text}', but it was not found."
            );
        }
    }
}
