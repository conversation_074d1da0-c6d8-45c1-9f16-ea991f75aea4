<?php

/**
 * Proxy class to intercept General::log calls during testing
 * 
 * This class will be used to replace the global General class during testing
 */
class General
{
    /**
     * Proxy for General::log() that can be intercepted during testing
     */
    public static function log($registry, string $event, string $extra = ''): bool
    {
        // Check if we're in testing mode and have the test helper available
        if (class_exists('Tests\Nzoom\TestHelpers\GeneralTestHelper') && 
            \Tests\Nzoom\TestHelpers\GeneralTestHelper::isMockingEnabled()) {
            return \Tests\Nzoom\TestHelpers\GeneralTestHelper::log($registry, $event, $extra);
        }
        
        // If not in testing mode, we need to call the real General::log
        // Since we can't easily access the original class, we'll simulate the behavior
        // In a real scenario, this would call the actual General::log method
        return true;
    }

    /**
     * Proxy for other General methods that might be needed
     * Add other static methods from General class as needed
     */
    public static function slashesEscape($var)
    {
        // Simple implementation for testing
        if (is_array($var)) {
            if (empty($var)) {
                return $var;
            }
            foreach ($var as $key => $value) {
                $escaped[$key] = self::slashesEscape($value);
            }
            return $escaped ?? [];
        } elseif (is_object($var) || is_null($var)) {
            return $var;
        } else {
            return addslashes($var);
        }
    }
}
