<?php

/**
 * Mock General class for testing
 * 
 * This file should be included before the real General class to override it during testing
 */

if (!class_exists('General')) {
    class General
    {
        /**
         * Captured log entries for testing
         */
        public static array $testLogEntries = [];

        /**
         * Flag to enable/disable test mode
         */
        public static bool $testMode = false;

        /**
         * Mock implementation of General::log() for testing
         *
         * @param object $registry Registry object
         * @param string $event Event type
         * @param string $extra Additional information
         * @return bool Always returns true for testing
         */
        public static function log($registry, string $event, string $extra = ''): bool
        {
            if (self::$testMode) {
                self::$testLogEntries[] = [
                    'registry' => $registry,
                    'event' => $event,
                    'extra' => $extra,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                return true;
            }

            // If not in test mode, we would call the real implementation
            // For now, just return true to avoid database dependencies in tests
            return true;
        }

        /**
         * Enable test mode
         */
        public static function enableTestMode(): void
        {
            self::$testMode = true;
            self::$testLogEntries = [];
        }

        /**
         * Disable test mode
         */
        public static function disableTestMode(): void
        {
            self::$testMode = false;
            self::$testLogEntries = [];
        }

        /**
         * Clear all captured log entries
         */
        public static function clearTestLogs(): void
        {
            self::$testLogEntries = [];
        }

        /**
         * Get all captured log entries
         */
        public static function getTestLogs(): array
        {
            return self::$testLogEntries;
        }

        /**
         * Get log entries by event type
         */
        public static function getTestLogsByEvent(string $event): array
        {
            return array_filter(self::$testLogEntries, function($entry) use ($event) {
                return $entry['event'] === $event;
            });
        }

        /**
         * Check if a specific event was logged
         */
        public static function hasTestEvent(string $event): bool
        {
            return !empty(self::getTestLogsByEvent($event));
        }

        /**
         * Get the last log entry
         */
        public static function getLastTestLog(): ?array
        {
            return empty(self::$testLogEntries) ? null : end(self::$testLogEntries);
        }

        /**
         * Get count of log entries
         */
        public static function getTestLogCount(): int
        {
            return count(self::$testLogEntries);
        }

        /**
         * Mock implementation of slashesEscape for testing
         */
        public static function slashesEscape($var)
        {
            if (is_array($var)) {
                if (empty($var)) {
                    return $var;
                }
                $escaped = [];
                foreach ($var as $key => $value) {
                    $escaped[$key] = self::slashesEscape($value);
                }
                return $escaped;
            } elseif (is_object($var) || is_null($var)) {
                return $var;
            } else {
                return addslashes($var);
            }
        }
    }
}
