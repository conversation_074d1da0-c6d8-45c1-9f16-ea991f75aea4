<?php

namespace Tests\Nzoom\TestHelpers;

/**
 * Mock for the General class to capture logging calls during testing
 */
class GeneralMock
{
    /**
     * Captured log entries
     */
    public static array $logEntries = [];

    /**
     * Mock implementation of General::log()
     *
     * @param object $registry Registry object
     * @param string $event Event type
     * @param string $extra Additional information
     * @return bool Always returns true for testing
     */
    public static function log($registry, string $event, string $extra = ''): bool
    {
        self::$logEntries[] = [
            'registry' => $registry,
            'event' => $event,
            'extra' => $extra,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return true;
    }

    /**
     * Clear all captured log entries
     */
    public static function clearLogs(): void
    {
        self::$logEntries = [];
    }

    /**
     * Get all captured log entries
     */
    public static function getLogs(): array
    {
        return self::$logEntries;
    }

    /**
     * Get log entries by event type
     */
    public static function getLogsByEvent(string $event): array
    {
        return array_filter(self::$logEntries, function($entry) use ($event) {
            return $entry['event'] === $event;
        });
    }

    /**
     * Check if a specific event was logged
     */
    public static function hasEvent(string $event): bool
    {
        return !empty(self::getLogsByEvent($event));
    }

    /**
     * Get the last log entry
     */
    public static function getLastLog(): ?array
    {
        return empty(self::$logEntries) ? null : end(self::$logEntries);
    }

    /**
     * Get count of log entries
     */
    public static function getLogCount(): int
    {
        return count(self::$logEntries);
    }
}
