<?php

namespace Tests\Nzoom\Export\Provider;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test case for ModelTableProvider back_label functionality
 */
class ModelTableProviderBackLabelTest extends ExportTestCase
{
    private RegistryMock $mockRegistry;
    private ModelTableProvider $provider;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        $this->mockRegistry = new RegistryMock();
        $this->provider = new ModelTableProvider(
            $this->mockRegistry,
            'full_num',
            'Full Num'
        );
    }

    public function testApplyBackLabelWithValidBackLabel(): void
    {
        $provider = $this->provider;
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('applyBackLabel');
        $method->setAccessible(true);

        $groupingData = [
            'back_labels' => [
                0 => '',        // No back_label for index 0
                1 => ' kg',     // Back_label for index 1
                2 => ' units'   // Back_label for index 2
            ]
        ];

        // Test with back_label
        $result = $method->invoke($provider, '100', 1, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('100 kg', $result['value']);
        $this->assertEquals(ExportValue::TYPE_STRING, $result['type']);

        // Test without back_label (empty string)
        $result = $method->invoke($provider, '50', 0, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('50', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);

        // Test with another back_label
        $result = $method->invoke($provider, '25', 2, $groupingData, ExportValue::TYPE_FLOAT);
        $this->assertEquals('25 units', $result['value']);
        $this->assertEquals(ExportValue::TYPE_STRING, $result['type']);
    }

    public function testApplyBackLabelWithoutBackLabels(): void
    {
        $provider = $this->provider;
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('applyBackLabel');
        $method->setAccessible(true);

        $groupingData = []; // No back_labels

        $result = $method->invoke($provider, '100', 1, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('100', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);
    }

    public function testApplyBackLabelFromVarDataWithValidBackLabel(): void
    {
        $provider = $this->provider;
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('applyBackLabelFromVarData');
        $method->setAccessible(true);

        $varData = [
            'back_label' => ' %'
        ];

        $result = $method->invoke($provider, '95', 'percentage', $varData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('95 %', $result['value']);
        $this->assertEquals(ExportValue::TYPE_STRING, $result['type']);
    }

    public function testApplyBackLabelFromVarDataWithoutBackLabel(): void
    {
        $provider = $this->provider;
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('applyBackLabelFromVarData');
        $method->setAccessible(true);

        $varData = []; // No back_label

        $result = $method->invoke($provider, '95', 'percentage', $varData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('95', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);
    }

    public function testApplyBackLabelWithEmptyBackLabel(): void
    {
        $provider = $this->provider;
        $reflection = new \ReflectionClass($provider);
        $method = $reflection->getMethod('applyBackLabel');
        $method->setAccessible(true);

        $groupingData = [
            'back_labels' => [
                0 => '',        // Empty back_label
                1 => null,      // Null back_label
                2 => '0',       // Zero string back_label (PHP empty('0') returns true)
                3 => ' units'   // Non-empty back_label
            ]
        ];

        // Test with empty string back_label
        $result = $method->invoke($provider, '100', 0, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('100', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);

        // Test with null back_label
        $result = $method->invoke($provider, '100', 1, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('100', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);

        // Test with '0' back_label (PHP empty('0') returns true, so it won't be applied)
        $result = $method->invoke($provider, '100', 2, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('100', $result['value']);
        $this->assertEquals(ExportValue::TYPE_INTEGER, $result['type']);

        // Test with non-empty back_label
        $result = $method->invoke($provider, '50', 3, $groupingData, ExportValue::TYPE_INTEGER);
        $this->assertEquals('50 units', $result['value']);
        $this->assertEquals(ExportValue::TYPE_STRING, $result['type']);
    }
}
