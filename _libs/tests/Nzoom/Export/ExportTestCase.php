<?php

namespace Tests\Nzoom\Export;

use Tests\Nzoom\NzoomTestCase;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportHeader;

// Include mock General class for testing (must be included before the real one)
require_once __DIR__ . '/../TestHelpers/MockGeneral.php';

// Include the real General class for logging functionality (if not already loaded)
if (!class_exists('General', false)) {
    require_once __DIR__ . '/../../../inc/common/general.class.php';
}

/**
 * Base test case for Export package tests
 *
 * Provides common functionality and test data for Export-related tests
 */
abstract class ExportTestCase extends NzoomTestCase
{
    /**
     * Set up before each test
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Enable General class test mode for logging capture
        \General::enableTestMode();
    }

    /**
     * Clean up after each test
     */
    protected function tearDown(): void
    {
        // Disable General class test mode
        \General::disableTestMode();

        parent::tearDown();
    }
    /**
     * Create sample export data for testing
     * 
     * @param int $recordCount Number of records to create
     * @return ExportData
     */
    protected function createSampleExportData(int $recordCount = 3): ExportData
    {
        $columns = [
            new ExportColumn('id', 'ID', 'integer'),
            new ExportColumn('name', 'Name', 'string'),
            new ExportColumn('email', 'Email', 'string'),
            new ExportColumn('created_at', 'Created At', 'datetime')
        ];

        $records = [];
        for ($i = 1; $i <= $recordCount; $i++) {
            $record = new ExportRecord();
            $record->addValue('id', $i, 'integer');
            $record->addValue('name', "User {$i}", 'string');
            $record->addValue('email', "user{$i}@example.com", 'string');
            $record->addValue(new ExportValue('created_at', date('Y-m-d H:i:s'), 'datetime'));
            $records[] = $record;
        }

        $header = new ExportHeader();
        foreach ($columns as $column) {
            $header->addColumn($column);
        }

        $exportData = new ExportData('TestModel', $header);
        foreach ($records as $record) {
            $exportData->addRecord($record);
        }

        return $exportData;
    }

    /**
     * Create sample export data with different data types
     * 
     * @return ExportData
     */
    protected function createMixedTypeExportData(): ExportData
    {
        $columns = [
            new ExportColumn('text_col', 'Text Column', 'string'),
            new ExportColumn('number_col', 'Number Column', 'float'),
            new ExportColumn('date_col', 'Date Column', 'date'),
            new ExportColumn('datetime_col', 'DateTime Column', 'datetime'),
            new ExportColumn('bool_col', 'Boolean Column', 'string')
        ];

        $record = new ExportRecord();
        $record->addValue('text_col', 'Sample Text', 'string');
        $record->addValue('number_col', 123.45, 'float');
        $record->addValue('date_col', '2024-01-15', 'date');
        $record->addValue('datetime_col', '2024-01-15 14:30:00', 'datetime');
        $record->addValue('bool_col', 'Yes', 'string');

        $header = new ExportHeader();
        foreach ($columns as $column) {
            $header->addColumn($column);
        }

        $exportData = new ExportData('TestModel', $header);
        $exportData->addRecord($record);

        return $exportData;
    }

    /**
     * Assert that exported content contains expected headers
     * 
     * @param string $content Exported content
     * @param array $expectedHeaders Expected header names
     * @param string $message Optional assertion message
     */
    protected function assertContainsHeaders(string $content, array $expectedHeaders, string $message = ''): void
    {
        foreach ($expectedHeaders as $header) {
            $this->assertStringContainsString($header, $content, 
                $message ?: "Content should contain header: {$header}");
        }
    }

    /**
     * Assert that exported content contains expected data values
     * 
     * @param string $content Exported content
     * @param array $expectedValues Expected data values
     * @param string $message Optional assertion message
     */
    protected function assertContainsValues(string $content, array $expectedValues, string $message = ''): void
    {
        foreach ($expectedValues as $value) {
            $this->assertStringContainsString((string)$value, $content, 
                $message ?: "Content should contain value: {$value}");
        }
    }

    /**
     * Create a temporary directory for export testing
     * 
     * @return string Path to temporary directory
     */
    protected function createTempDir(): string
    {
        $tempDir = sys_get_temp_dir() . '/nzoom_export_test_' . uniqid();
        mkdir($tempDir, 0777, true);
        return $tempDir;
    }

    /**
     * Clean up temporary directory and its contents
     *
     * @param string $dirPath Path to directory to delete
     */
    protected function cleanupTempDir(string $dirPath): void
    {
        if (is_dir($dirPath)) {
            $files = array_diff(scandir($dirPath), ['.', '..']);
            foreach ($files as $file) {
                $filePath = $dirPath . '/' . $file;
                if (is_dir($filePath)) {
                    $this->cleanupTempDir($filePath);
                } else {
                    unlink($filePath);
                }
            }
            rmdir($dirPath);
        }
    }

    // Logging test helper methods

    /**
     * Assert that a specific event was logged
     */
    protected function assertEventLogged(string $event, string $message = ''): void
    {
        $this->assertTrue(\General::hasTestEvent($event),
            $message ?: "Expected event '{$event}' to be logged");
    }

    /**
     * Assert that a specific event was not logged
     */
    protected function assertEventNotLogged(string $event, string $message = ''): void
    {
        $this->assertFalse(\General::hasTestEvent($event),
            $message ?: "Expected event '{$event}' not to be logged");
    }

    /**
     * Assert that log entries contain specific text
     */
    protected function assertLogContains(string $text, string $message = ''): void
    {
        $logs = \General::getTestLogs();
        $found = false;

        foreach ($logs as $entry) {
            if (strpos($entry['extra'], $text) !== false) {
                $found = true;
                break;
            }
        }

        $this->assertTrue($found, $message ?: "Expected log entries to contain '{$text}'");
    }

    /**
     * Get all captured log entries
     */
    protected function getLogEntries(): array
    {
        return \General::getTestLogs();
    }

    /**
     * Get log entries by event type
     */
    protected function getLogsByEvent(string $event): array
    {
        return \General::getTestLogsByEvent($event);
    }

    /**
     * Clear all captured log entries
     */
    protected function clearLogs(): void
    {
        \General::clearTestLogs();
    }

    /**
     * Get count of log entries
     */
    protected function getLogCount(): int
    {
        return \General::getTestLogCount();
    }
}
