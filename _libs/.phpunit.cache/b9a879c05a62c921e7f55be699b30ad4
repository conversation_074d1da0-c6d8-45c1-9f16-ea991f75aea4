a:6:{s:9:"classesIn";a:1:{s:48:"Nzoom\Export\Adapter\AbstractExportFormatAdapter";a:6:{s:4:"name";s:27:"AbstractExportFormatAdapter";s:14:"namespacedName";s:48:"Nzoom\Export\Adapter\AbstractExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:13;s:7:"endLine";i:318;s:7:"methods";a:12:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:67:"__construct(Registry $registry, string $module, string $controller)";s:10:"visibility";s:6:"public";s:9:"startLine";i:44;s:7:"endLine";i:53;s:3:"ccn";i:2;}s:16:"setConfiguration";a:6:{s:10:"methodName";s:16:"setConfiguration";s:9:"signature";s:82:"setConfiguration(array $config): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:61;s:7:"endLine";i:65;s:3:"ccn";i:1;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:53:"getExportFilename($prefix, string $extension): string";s:10:"visibility";s:9:"protected";s:9:"startLine";i:74;s:7:"endLine";i:95;s:3:"ccn";i:5;}s:11:"sendHeaders";a:6:{s:10:"methodName";s:11:"sendHeaders";s:9:"signature";s:56:"sendHeaders(string $filename, string $contentType): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:104;s:7:"endLine";i:122;s:3:"ccn";i:3;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:57:"handleExportError(string $message, int $statusCode): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:131;s:7:"endLine";i:155;s:3:"ccn";i:3;}s:16:"getRecordHeaders";a:6:{s:10:"methodName";s:16:"getRecordHeaders";s:9:"signature";s:32:"getRecordHeaders($record): array";s:10:"visibility";s:9:"protected";s:9:"startLine";i:163;s:7:"endLine";i:178;s:3:"ccn";i:5;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:183;s:7:"endLine";i:186;s:3:"ccn";i:1;}s:28:"validateAndPrepareSaveTarget";a:6:{s:10:"methodName";s:28:"validateAndPrepareSaveTarget";s:9:"signature";s:35:"validateAndPrepareSaveTarget($file)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:195;s:7:"endLine";i:206;s:3:"ccn";i:3;}s:20:"validateStringTarget";a:6:{s:10:"methodName";s:20:"validateStringTarget";s:9:"signature";s:44:"validateStringTarget(string $target): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:215;s:7:"endLine";i:224;s:3:"ccn";i:2;}s:24:"validatePhpStreamWrapper";a:6:{s:10:"methodName";s:24:"validatePhpStreamWrapper";s:9:"signature";s:49:"validatePhpStreamWrapper(string $wrapper): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:233;s:7:"endLine";i:266;s:3:"ccn";i:5;}s:16:"validateFilePath";a:6:{s:10:"methodName";s:16:"validateFilePath";s:9:"signature";s:42:"validateFilePath(string $filePath): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:275;s:7:"endLine";i:288;s:3:"ccn";i:3;}s:19:"validateFilePointer";a:6:{s:10:"methodName";s:19:"validateFilePointer";s:9:"signature";s:33:"validateFilePointer($filePointer)";s:10:"visibility";s:7:"private";s:9:"startLine";i:297;s:7:"endLine";i:317;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:319;s:18:"commentLinesOfCode";i:115;s:21:"nonCommentLinesOfCode";i:204;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:91:{i:46;i:6;i:47;i:7;i:48;i:8;i:50;i:9;i:51;i:10;i:63;i:11;i:64;i:12;i:76;i:13;i:77;i:14;i:81;i:15;i:82;i:16;i:85;i:17;i:86;i:18;i:90;i:19;i:91;i:20;i:94;i:21;i:106;i:22;i:107;i:23;i:111;i:24;i:112;i:25;i:116;i:26;i:117;i:27;i:118;i:28;i:119;i:29;i:120;i:30;i:121;i:31;i:134;i:32;i:137;i:33;i:138;i:33;i:139;i:33;i:140;i:33;i:141;i:33;i:142;i:33;i:145;i:34;i:146;i:35;i:148;i:36;i:149;i:37;i:150;i:38;i:151;i:39;i:165;i:40;i:167;i:41;i:168;i:42;i:169;i:43;i:170;i:44;i:171;i:45;i:172;i:46;i:173;i:47;i:174;i:48;i:177;i:49;i:185;i:50;i:197;i:51;i:199;i:52;i:200;i:53;i:202;i:54;i:204;i:55;i:218;i:56;i:219;i:57;i:222;i:58;i:235;i:59;i:238;i:60;i:239;i:60;i:240;i:60;i:241;i:60;i:242;i:60;i:243;i:60;i:244;i:60;i:247;i:61;i:248;i:62;i:252;i:63;i:253;i:64;i:257;i:65;i:258;i:66;i:260;i:67;i:262;i:68;i:265;i:69;i:277;i:70;i:279;i:71;i:280;i:72;i:283;i:73;i:284;i:74;i:287;i:75;i:299;i:76;i:300;i:77;i:303;i:78;i:304;i:79;i:305;i:80;i:309;i:81;i:310;i:82;i:312;i:83;i:313;i:84;i:316;i:85;}}