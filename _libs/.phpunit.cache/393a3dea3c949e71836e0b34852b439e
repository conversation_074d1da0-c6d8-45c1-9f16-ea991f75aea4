a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:955;s:7:"methods";a:27:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:106:"__construct(Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:48;s:7:"endLine";i:56;s:3:"ccn";i:1;}s:18:"getReferenceColumn";a:6:{s:10:"methodName";s:18:"getReferenceColumn";s:9:"signature";s:27:"getReferenceColumn(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:63;s:7:"endLine";i:66;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:73;s:7:"endLine";i:80;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:117;s:3:"ccn";i:9;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:125;s:7:"endLine";i:163;s:3:"ccn";i:9;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:174;s:7:"endLine";i:215;s:3:"ccn";i:6;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:226;s:7:"endLine";i:265;s:3:"ccn";i:5;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:277;s:7:"endLine";i:330;s:3:"ccn";i:7;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:339;s:7:"endLine";i:390;s:3:"ccn";i:6;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:398;s:7:"endLine";i:419;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:427;s:7:"endLine";i:434;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:442;s:7:"endLine";i:467;s:3:"ccn";i:13;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:475;s:7:"endLine";i:502;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:184:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:516;s:7:"endLine";i:528;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:128:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:539;s:7:"endLine";i:554;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:149:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:566;s:7:"endLine";i:604;s:3:"ccn";i:5;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:191:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:619;s:7:"endLine";i:663;s:3:"ccn";i:6;}s:18:"resolveOptionLabel";a:6:{s:10:"methodName";s:18:"resolveOptionLabel";s:9:"signature";s:84:"resolveOptionLabel($value, ?string $fieldType, string $varName, array $groupingData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:676;s:7:"endLine";i:700;s:3:"ccn";i:8;}s:29:"resolveOptionLabelFromVarData";a:6:{s:10:"methodName";s:29:"resolveOptionLabelFromVarData";s:9:"signature";s:72:"resolveOptionLabelFromVarData($value, string $fieldType, array $varData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:710;s:7:"endLine";i:734;s:3:"ccn";i:8;}s:14:"applyBackLabel";a:6:{s:10:"methodName";s:14:"applyBackLabel";s:9:"signature";s:84:"applyBackLabel($value, int $index, array $groupingData, string $originalType): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:745;s:7:"endLine";i:761;s:3:"ccn";i:4;}s:25:"applyBackLabelFromVarData";a:6:{s:10:"methodName";s:25:"applyBackLabelFromVarData";s:9:"signature";s:95:"applyBackLabelFromVarData($value, string $varName, array $varData, string $originalType): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:772;s:7:"endLine";i:784;s:3:"ccn";i:2;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:795;s:7:"endLine";i:831;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:836;s:7:"endLine";i:843;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:848;s:7:"endLine";i:857;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:865;s:7:"endLine";i:876;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:885;s:7:"endLine";i:906;s:3:"ccn";i:7;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:914;s:7:"endLine";i:954;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:956;s:18:"commentLinesOfCode";i:307;s:21:"nonCommentLinesOfCode";i:649;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:335:{i:48;i:5;i:50;i:6;i:51;i:7;i:52;i:7;i:53;i:7;i:54;i:7;i:55;i:8;i:65;i:9;i:75;i:10;i:76;i:10;i:77;i:10;i:78;i:10;i:79;i:10;i:85;i:11;i:87;i:12;i:88;i:13;i:90;i:14;i:91;i:15;i:96;i:16;i:98;i:17;i:100;i:18;i:101;i:19;i:103;i:20;i:106;i:21;i:107;i:22;i:110;i:23;i:112;i:24;i:113;i:24;i:116;i:25;i:127;i:26;i:133;i:27;i:134;i:28;i:138;i:29;i:139;i:30;i:140;i:31;i:143;i:32;i:146;i:33;i:147;i:34;i:151;i:35;i:152;i:36;i:153;i:37;i:156;i:38;i:158;i:39;i:159;i:39;i:162;i:40;i:177;i:41;i:178;i:42;i:179;i:43;i:180;i:44;i:181;i:45;i:183;i:46;i:184;i:47;i:188;i:48;i:191;i:49;i:192;i:50;i:195;i:51;i:196;i:51;i:197;i:51;i:198;i:51;i:199;i:51;i:200;i:51;i:201;i:51;i:202;i:51;i:203;i:51;i:204;i:51;i:207;i:52;i:210;i:53;i:211;i:54;i:214;i:55;i:229;i:56;i:230;i:57;i:232;i:58;i:233;i:59;i:237;i:60;i:240;i:61;i:241;i:62;i:244;i:63;i:245;i:63;i:246;i:63;i:247;i:63;i:248;i:63;i:249;i:63;i:250;i:63;i:251;i:63;i:252;i:63;i:253;i:63;i:254;i:63;i:257;i:64;i:260;i:65;i:261;i:66;i:264;i:67;i:277;i:68;i:280;i:69;i:281;i:70;i:285;i:71;i:288;i:72;i:289;i:72;i:290;i:72;i:291;i:72;i:292;i:72;i:293;i:73;i:296;i:74;i:297;i:74;i:298;i:74;i:299;i:74;i:300;i:74;i:301;i:75;i:303;i:76;i:305;i:77;i:306;i:78;i:309;i:79;i:311;i:80;i:313;i:81;i:314;i:82;i:316;i:83;i:319;i:84;i:322;i:85;i:323;i:86;i:327;i:87;i:329;i:88;i:342;i:89;i:343;i:90;i:347;i:91;i:350;i:92;i:351;i:92;i:352;i:92;i:353;i:92;i:354;i:92;i:355;i:93;i:358;i:94;i:359;i:94;i:360;i:94;i:361;i:94;i:362;i:94;i:363;i:95;i:366;i:96;i:368;i:97;i:370;i:98;i:371;i:99;i:374;i:100;i:376;i:101;i:377;i:102;i:379;i:103;i:382;i:104;i:383;i:105;i:387;i:106;i:389;i:107;i:401;i:108;i:402;i:109;i:403;i:110;i:404;i:111;i:408;i:112;i:411;i:113;i:412;i:114;i:413;i:115;i:414;i:116;i:418;i:117;i:430;i:118;i:431;i:119;i:433;i:120;i:445;i:121;i:446;i:122;i:448;i:123;i:449;i:124;i:451;i:125;i:452;i:126;i:453;i:127;i:454;i:128;i:455;i:129;i:456;i:130;i:457;i:131;i:458;i:132;i:459;i:133;i:463;i:134;i:465;i:135;i:477;i:136;i:480;i:137;i:481;i:138;i:482;i:139;i:484;i:140;i:488;i:141;i:489;i:142;i:490;i:143;i:492;i:144;i:496;i:145;i:497;i:146;i:501;i:147;i:519;i:148;i:521;i:149;i:522;i:150;i:523;i:151;i:524;i:152;i:525;i:153;i:542;i:154;i:545;i:155;i:547;i:156;i:548;i:157;i:549;i:158;i:550;i:159;i:551;i:160;i:568;i:161;i:571;i:162;i:572;i:163;i:575;i:164;i:577;i:165;i:579;i:166;i:580;i:167;i:584;i:168;i:585;i:169;i:587;i:170;i:588;i:171;i:590;i:172;i:594;i:173;i:597;i:174;i:598;i:175;i:600;i:176;i:603;i:177;i:621;i:178;i:624;i:179;i:625;i:180;i:628;i:181;i:630;i:182;i:632;i:183;i:633;i:184;i:637;i:185;i:639;i:186;i:640;i:187;i:641;i:188;i:643;i:189;i:644;i:190;i:646;i:191;i:649;i:192;i:653;i:193;i:656;i:194;i:657;i:195;i:659;i:196;i:662;i:197;i:679;i:198;i:680;i:199;i:684;i:200;i:685;i:201;i:688;i:202;i:691;i:203;i:692;i:204;i:693;i:205;i:694;i:206;i:699;i:207;i:713;i:208;i:714;i:209;i:718;i:210;i:719;i:211;i:722;i:212;i:725;i:213;i:726;i:214;i:727;i:215;i:728;i:216;i:733;i:217;i:748;i:218;i:749;i:219;i:752;i:220;i:755;i:221;i:756;i:222;i:757;i:223;i:760;i:224;i:775;i:225;i:778;i:226;i:779;i:227;i:780;i:228;i:783;i:229;i:797;i:230;i:798;i:231;i:802;i:232;i:803;i:233;i:804;i:234;i:805;i:235;i:806;i:236;i:807;i:237;i:809;i:238;i:811;i:239;i:812;i:240;i:813;i:241;i:814;i:242;i:815;i:243;i:816;i:244;i:818;i:245;i:820;i:246;i:821;i:247;i:823;i:248;i:824;i:249;i:826;i:250;i:827;i:251;i:830;i:252;i:839;i:253;i:840;i:253;i:841;i:253;i:842;i:253;i:848;i:254;i:850;i:255;i:851;i:256;i:856;i:257;i:868;i:258;i:869;i:259;i:872;i:260;i:873;i:261;i:875;i:262;i:887;i:263;i:888;i:264;i:891;i:265;i:893;i:266;i:894;i:267;i:896;i:268;i:897;i:269;i:900;i:270;i:901;i:271;i:905;i:272;i:917;i:273;i:918;i:274;i:922;i:275;i:923;i:276;i:927;i:277;i:928;i:278;i:932;i:279;i:933;i:280;i:937;i:281;i:938;i:282;i:939;i:282;i:940;i:282;i:941;i:282;i:942;i:282;i:943;i:282;i:944;i:282;i:945;i:282;i:947;i:283;i:948;i:284;i:953;i:285;}}