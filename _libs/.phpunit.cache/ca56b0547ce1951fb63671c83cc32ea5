a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:16;s:7:"endLine";i:394;s:7:"methods";a:18:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:76;s:7:"endLine";i:83;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:37:"setModelName(string $modelName): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:91;s:7:"endLine";i:95;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:51:"setModelFactoryName(string $modelFactoryName): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:103;s:7:"endLine";i:107;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:117;s:7:"endLine";i:129;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:117:"createExportData(Outlook $outlook, array $filters, string $modelClass, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:140;s:7:"endLine";i:144;s:3:"ccn";i:1;}s:26:"createExportDataWithTables";a:6:{s:10:"methodName";s:26:"createExportDataWithTables";s:9:"signature";s:129:"createExportDataWithTables(Outlook $outlook, array $filters, string $factoryClass, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:155;s:7:"endLine";i:164;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:175;s:7:"endLine";i:178;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:189;s:7:"endLine";i:205;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:213;s:7:"endLine";i:222;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:233;s:7:"endLine";i:256;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:263;s:7:"endLine";i:270;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:278;s:7:"endLine";i:282;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:289;s:7:"endLine";i:296;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:303;s:7:"endLine";i:306;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:314;s:7:"endLine";i:317;s:3:"ccn";i:1;}s:26:"getReferenceColumnForModel";a:6:{s:10:"methodName";s:26:"getReferenceColumnForModel";s:9:"signature";s:53:"getReferenceColumnForModel(string $modelClass): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:326;s:7:"endLine";i:333;s:3:"ccn";i:2;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:53:"getExportFilename($prefix, string $extension): string";s:10:"visibility";s:9:"protected";s:9:"startLine";i:342;s:7:"endLine";i:361;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:57:"handleExportError(string $message, int $statusCode): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:370;s:7:"endLine";i:393;s:3:"ccn";i:3;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:395;s:18:"commentLinesOfCode";i:172;s:21:"nonCommentLinesOfCode";i:223;}s:15:"ignoredLinesFor";a:1:{i:0;i:16;}s:17:"executableLinesIn";a:81:{i:78;i:11;i:79;i:12;i:80;i:13;i:81;i:14;i:82;i:15;i:93;i:16;i:94;i:17;i:105;i:18;i:106;i:19;i:119;i:20;i:120;i:20;i:121;i:20;i:122;i:20;i:123;i:20;i:124;i:20;i:125;i:20;i:126;i:20;i:128;i:21;i:142;i:22;i:143;i:23;i:157;i:24;i:160;i:25;i:161;i:26;i:163;i:27;i:177;i:28;i:189;i:29;i:193;i:30;i:195;i:31;i:196;i:32;i:199;i:33;i:200;i:34;i:201;i:35;i:202;i:36;i:203;i:37;i:215;i:38;i:217;i:39;i:218;i:40;i:221;i:41;i:235;i:42;i:236;i:43;i:241;i:44;i:242;i:44;i:243;i:44;i:244;i:44;i:245;i:44;i:248;i:45;i:252;i:46;i:253;i:47;i:265;i:48;i:266;i:49;i:269;i:50;i:280;i:51;i:281;i:52;i:291;i:53;i:292;i:54;i:295;i:55;i:305;i:56;i:316;i:57;i:328;i:58;i:329;i:59;i:332;i:60;i:344;i:61;i:347;i:62;i:348;i:63;i:351;i:64;i:352;i:65;i:356;i:66;i:357;i:67;i:360;i:68;i:373;i:69;i:376;i:70;i:377;i:70;i:378;i:70;i:379;i:70;i:380;i:70;i:383;i:71;i:384;i:72;i:386;i:73;i:387;i:74;i:388;i:75;i:389;i:76;}}