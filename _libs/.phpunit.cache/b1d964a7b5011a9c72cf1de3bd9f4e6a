a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:19;s:7:"endLine";i:512;s:7:"methods";a:17:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:46;s:7:"endLine";i:49;s:3:"ccn";i:1;}s:16:"setTableProvider";a:6:{s:10:"methodName";s:16:"setTableProvider";s:9:"signature";s:90:"setTableProvider(?Nzoom\Export\Provider\ExportTableProviderInterface $tableProvider): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:56;s:7:"endLine";i:59;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:66;s:7:"endLine";i:69;s:3:"ccn";i:1;}s:22:"withModelTableProvider";a:6:{s:10:"methodName";s:22:"withModelTableProvider";s:9:"signature";s:103:"withModelTableProvider(string $referenceColumnName, string $referenceColumnLabel, array $options): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:79;s:7:"endLine";i:91;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:100;s:7:"endLine";i:125;s:3:"ccn";i:3;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:133;s:7:"endLine";i:165;s:3:"ccn";i:7;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:174;s:7:"endLine";i:179;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:188;s:7:"endLine";i:231;s:3:"ccn";i:8;}s:22:"getModelReferenceValue";a:6:{s:10:"methodName";s:22:"getModelReferenceValue";s:9:"signature";s:45:"getModelReferenceValue(Model $model): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:239;s:7:"endLine";i:263;s:3:"ccn";i:5;}s:22:"extractTablesForRecord";a:6:{s:10:"methodName";s:22:"extractTablesForRecord";s:9:"signature";s:84:"extractTablesForRecord(Nzoom\Export\Entity\ExportRecord $record, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:271;s:7:"endLine";i:284;s:3:"ccn";i:4;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:293;s:7:"endLine";i:317;s:3:"ccn";i:2;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:34:"setChunkSize(int $chunkSize): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:324;s:7:"endLine";i:327;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:338;s:7:"endLine";i:376;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:145:"createCursorStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:388;s:7:"endLine";i:445;s:3:"ccn";i:4;}s:32:"extractModelTypeFromFactoryClass";a:6:{s:10:"methodName";s:32:"extractModelTypeFromFactoryClass";s:9:"signature";s:62:"extractModelTypeFromFactoryClass(string $factoryClass): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:454;s:7:"endLine";i:461;s:3:"ccn";i:1;}s:25:"extractModelTypeFromModel";a:6:{s:10:"methodName";s:25:"extractModelTypeFromModel";s:9:"signature";s:47:"extractModelTypeFromModel(Model $model): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:469;s:7:"endLine";i:489;s:3:"ccn";i:2;}s:11:"createEmpty";a:6:{s:10:"methodName";s:11:"createEmpty";s:9:"signature";s:80:"createEmpty(string $modelType, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:498;s:7:"endLine";i:511;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:513;s:18:"commentLinesOfCode";i:180;s:21:"nonCommentLinesOfCode";i:333;}s:15:"ignoredLinesFor";a:1:{i:0;i:19;}s:17:"executableLinesIn";a:163:{i:48;i:5;i:58;i:6;i:68;i:7;i:79;i:8;i:81;i:9;i:84;i:10;i:85;i:10;i:86;i:10;i:87;i:10;i:89;i:11;i:90;i:12;i:103;i:13;i:104;i:14;i:105;i:15;i:106;i:16;i:110;i:17;i:113;i:18;i:114;i:18;i:115;i:18;i:116;i:18;i:119;i:19;i:120;i:20;i:121;i:21;i:124;i:22;i:135;i:23;i:136;i:24;i:138;i:25;i:139;i:26;i:141;i:27;i:143;i:28;i:147;i:29;i:148;i:30;i:150;i:31;i:151;i:31;i:152;i:31;i:153;i:31;i:154;i:31;i:155;i:31;i:156;i:31;i:157;i:32;i:159;i:33;i:164;i:34;i:176;i:35;i:177;i:36;i:190;i:37;i:191;i:37;i:192;i:37;i:195;i:38;i:196;i:39;i:197;i:40;i:198;i:41;i:202;i:42;i:204;i:43;i:205;i:44;i:208;i:45;i:211;i:46;i:212;i:47;i:215;i:48;i:216;i:49;i:217;i:50;i:218;i:51;i:222;i:52;i:226;i:53;i:227;i:54;i:230;i:55;i:241;i:56;i:242;i:57;i:245;i:58;i:248;i:59;i:249;i:60;i:250;i:61;i:254;i:62;i:255;i:63;i:256;i:64;i:257;i:65;i:258;i:66;i:262;i:67;i:274;i:68;i:276;i:69;i:277;i:70;i:279;i:71;i:281;i:72;i:282;i:72;i:295;i:73;i:296;i:73;i:297;i:73;i:298;i:73;i:299;i:73;i:300;i:73;i:301;i:73;i:302;i:73;i:303;i:73;i:304;i:73;i:305;i:73;i:306;i:73;i:308;i:74;i:313;i:76;i:309;i:76;i:310;i:76;i:311;i:76;i:312;i:76;i:316;i:77;i:326;i:78;i:341;i:79;i:344;i:80;i:347;i:81;i:348;i:81;i:349;i:81;i:350;i:81;i:353;i:82;i:370;i:82;i:355;i:83;i:356;i:83;i:357;i:83;i:358;i:83;i:361;i:84;i:364;i:85;i:365;i:86;i:366;i:87;i:369;i:88;i:373;i:89;i:375;i:90;i:391;i:91;i:394;i:92;i:397;i:93;i:398;i:93;i:399;i:93;i:400;i:93;i:403;i:94;i:406;i:95;i:439;i:95;i:408;i:96;i:411;i:97;i:412;i:98;i:413;i:99;i:414;i:100;i:418;i:101;i:419;i:102;i:422;i:103;i:424;i:104;i:425;i:105;i:429;i:106;i:430;i:107;i:433;i:108;i:434;i:109;i:435;i:110;i:438;i:111;i:442;i:112;i:444;i:113;i:457;i:114;i:460;i:115;i:472;i:116;i:475;i:117;i:483;i:118;i:484;i:119;i:488;i:120;i:501;i:121;i:504;i:122;i:505;i:122;i:506;i:122;i:507;i:122;i:508;i:122;i:510;i:123;}}