a:6:{s:9:"classesIn";a:1:{s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";a:6:{s:4:"name";s:23:"JsonExportFormatAdapter";s:14:"namespacedName";s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:15;s:7:"endLine";i:510;s:7:"methods";a:19:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:30;s:7:"endLine";i:55;s:3:"ccn";i:3;}s:18:"extractJsonOptions";a:6:{s:10:"methodName";s:18:"extractJsonOptions";s:9:"signature";s:40:"extractJsonOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:63;s:7:"endLine";i:76;s:3:"ccn";i:7;}s:16:"writeJsonContent";a:6:{s:10:"methodName";s:16:"writeJsonContent";s:9:"signature";s:95:"writeJsonContent($saveTarget, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:87;s:7:"endLine";i:117;s:3:"ccn";i:5;}s:15:"prepareJsonData";a:6:{s:10:"methodName";s:15:"prepareJsonData";s:9:"signature";s:66:"prepareJsonData(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:125;s:7:"endLine";i:136;s:3:"ccn";i:5;}s:21:"prepareArrayStructure";a:6:{s:10:"methodName";s:21:"prepareArrayStructure";s:9:"signature";s:72:"prepareArrayStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:144;s:7:"endLine";i:164;s:3:"ccn";i:5;}s:22:"prepareObjectStructure";a:6:{s:10:"methodName";s:22:"prepareObjectStructure";s:9:"signature";s:73:"prepareObjectStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:172;s:7:"endLine";i:189;s:3:"ccn";i:2;}s:22:"prepareNestedStructure";a:6:{s:10:"methodName";s:22:"prepareNestedStructure";s:9:"signature";s:73:"prepareNestedStructure(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:197;s:7:"endLine";i:220;s:3:"ccn";i:6;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:84:"processExportRecord(Nzoom\Export\Entity\ExportRecord $record, array $columns): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:229;s:7:"endLine";i:247;s:3:"ccn";i:3;}s:18:"formatValueForJson";a:6:{s:10:"methodName";s:18:"formatValueForJson";s:9:"signature";s:64:"formatValueForJson(Nzoom\Export\Entity\ExportValue $exportValue)";s:10:"visibility";s:7:"private";s:9:"startLine";i:255;s:7:"endLine";i:284;s:3:"ccn";i:9;}s:15:"formatDateValue";a:6:{s:10:"methodName";s:15:"formatDateValue";s:9:"signature";s:45:"formatDateValue($value, string $type): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:293;s:7:"endLine";i:311;s:3:"ccn";i:7;}s:11:"addMetadata";a:6:{s:10:"methodName";s:11:"addMetadata";s:9:"signature";s:75:"addMetadata(array $data, Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:320;s:7:"endLine";i:326;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:62:"getMetadata(Nzoom\Export\Entity\ExportData $exportData): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:334;s:7:"endLine";i:361;s:3:"ccn";i:3;}s:14:"getJsonOptions";a:6:{s:10:"methodName";s:14:"getJsonOptions";s:9:"signature";s:35:"getJsonOptions(array $options): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:369;s:7:"endLine";i:419;s:3:"ccn";i:7;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:424;s:7:"endLine";i:427;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:432;s:7:"endLine";i:436;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:441;s:7:"endLine";i:444;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:449;s:7:"endLine";i:452;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:457;s:7:"endLine";i:460;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:465;s:7:"endLine";i:509;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:511;s:18:"commentLinesOfCode";i:140;s:21:"nonCommentLinesOfCode";i:371;}s:15:"ignoredLinesFor";a:1:{i:0;i:15;}s:17:"executableLinesIn";a:204:{i:30;i:3;i:34;i:4;i:37;i:5;i:40;i:6;i:41;i:7;i:42;i:8;i:46;i:9;i:48;i:10;i:50;i:11;i:53;i:12;i:65;i:13;i:66;i:14;i:67;i:15;i:68;i:16;i:71;i:17;i:72;i:18;i:73;i:19;i:74;i:20;i:90;i:21;i:93;i:22;i:96;i:23;i:97;i:24;i:98;i:25;i:102;i:26;i:103;i:27;i:104;i:28;i:105;i:29;i:108;i:30;i:109;i:31;i:110;i:32;i:115;i:33;i:116;i:34;i:127;i:35;i:128;i:36;i:129;i:37;i:130;i:38;i:131;i:39;i:132;i:40;i:134;i:41;i:146;i:42;i:147;i:43;i:148;i:44;i:149;i:45;i:151;i:46;i:152;i:47;i:153;i:48;i:155;i:49;i:158;i:50;i:159;i:51;i:163;i:52;i:175;i:53;i:176;i:54;i:177;i:55;i:178;i:56;i:180;i:57;i:181;i:57;i:182;i:57;i:184;i:58;i:185;i:59;i:188;i:60;i:199;i:61;i:200;i:62;i:201;i:63;i:204;i:64;i:205;i:65;i:209;i:66;i:210;i:67;i:211;i:68;i:212;i:69;i:213;i:70;i:214;i:71;i:219;i:72;i:231;i:73;i:232;i:74;i:235;i:75;i:236;i:76;i:237;i:77;i:238;i:78;i:239;i:79;i:242;i:80;i:246;i:81;i:257;i:82;i:258;i:83;i:261;i:84;i:262;i:85;i:267;i:86;i:268;i:87;i:270;i:88;i:271;i:89;i:273;i:90;i:274;i:91;i:276;i:92;i:277;i:93;i:278;i:94;i:280;i:95;i:282;i:96;i:296;i:97;i:297;i:98;i:298;i:99;i:299;i:100;i:300;i:101;i:301;i:102;i:302;i:103;i:303;i:104;i:304;i:105;i:306;i:106;i:310;i:107;i:322;i:108;i:323;i:108;i:324;i:108;i:325;i:108;i:336;i:109;i:337;i:110;i:339;i:111;i:340;i:112;i:341;i:113;i:342;i:113;i:343;i:113;i:344;i:113;i:345;i:113;i:349;i:114;i:350;i:115;i:351;i:116;i:354;i:117;i:355;i:117;i:356;i:117;i:357;i:117;i:358;i:117;i:359;i:117;i:360;i:117;i:371;i:118;i:374;i:119;i:375;i:119;i:376;i:121;i:378;i:122;i:379;i:123;i:383;i:124;i:384;i:124;i:385;i:126;i:387;i:127;i:388;i:128;i:392;i:129;i:393;i:129;i:394;i:131;i:396;i:132;i:397;i:133;i:401;i:134;i:402;i:134;i:403;i:136;i:405;i:137;i:406;i:138;i:410;i:139;i:411;i:139;i:412;i:141;i:414;i:142;i:415;i:143;i:418;i:144;i:426;i:145;i:435;i:146;i:443;i:147;i:451;i:148;i:459;i:149;i:467;i:150;i:468;i:150;i:469;i:150;i:470;i:150;i:471;i:150;i:472;i:150;i:473;i:150;i:474;i:150;i:475;i:150;i:476;i:150;i:477;i:150;i:478;i:150;i:479;i:150;i:480;i:150;i:481;i:150;i:482;i:150;i:483;i:150;i:484;i:150;i:485;i:150;i:486;i:150;i:487;i:150;i:488;i:150;i:489;i:150;i:490;i:150;i:491;i:150;i:492;i:150;i:493;i:150;i:494;i:150;i:495;i:150;i:496;i:150;i:497;i:150;i:498;i:150;i:499;i:150;i:500;i:150;i:501;i:150;i:502;i:150;i:503;i:150;i:504;i:150;i:505;i:150;i:506;i:150;i:507;i:150;i:508;i:150;}}