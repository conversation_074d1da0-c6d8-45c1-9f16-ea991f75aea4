a:6:{s:9:"classesIn";a:1:{s:43:"Nzoom\Export\Adapter\CsvExportFormatAdapter";a:6:{s:4:"name";s:22:"CsvExportFormatAdapter";s:14:"namespacedName";s:43:"Nzoom\Export\Adapter\CsvExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:15;s:7:"endLine";i:496;s:7:"methods";a:20:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:50;s:7:"endLine";i:75;s:3:"ccn";i:3;}s:17:"extractCsvOptions";a:6:{s:10:"methodName";s:17:"extractCsvOptions";s:9:"signature";s:39:"extractCsvOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:83;s:7:"endLine";i:116;s:3:"ccn";i:19;}s:15:"writeCsvContent";a:6:{s:10:"methodName";s:15:"writeCsvContent";s:9:"signature";s:78:"writeCsvContent($saveTarget, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:126;s:7:"endLine";i:162;s:3:"ccn";i:6;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:83:"processExportDataRecords($output, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:171;s:7:"endLine";i:197;s:3:"ccn";i:5;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:68:"processExportRecord(Nzoom\Export\Entity\ExportRecord $record): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:205;s:7:"endLine";i:215;s:3:"ccn";i:2;}s:17:"formatValueForCsv";a:6:{s:10:"methodName";s:17:"formatValueForCsv";s:9:"signature";s:71:"formatValueForCsv(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:223;s:7:"endLine";i:257;s:3:"ccn";i:12;}s:15:"formatDateValue";a:6:{s:10:"methodName";s:15:"formatDateValue";s:9:"signature";s:68:"formatDateValue($value, string $type, ?string $customFormat): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:267;s:7:"endLine";i:284;s:3:"ccn";i:7;}s:16:"getDecimalPlaces";a:6:{s:10:"methodName";s:16:"getDecimalPlaces";s:9:"signature";s:37:"getDecimalPlaces(string $format): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:292;s:7:"endLine";i:300;s:3:"ccn";i:2;}s:12:"getDelimiter";a:6:{s:10:"methodName";s:12:"getDelimiter";s:9:"signature";s:36:"getDelimiter(array $options): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:308;s:7:"endLine";i:327;s:3:"ccn";i:4;}s:18:"normalizeDelimiter";a:6:{s:10:"methodName";s:18:"normalizeDelimiter";s:9:"signature";s:45:"normalizeDelimiter(string $delimiter): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:335;s:7:"endLine";i:348;s:3:"ccn";i:6;}s:13:"setDateFormat";a:6:{s:10:"methodName";s:13:"setDateFormat";s:9:"signature";s:35:"setDateFormat(string $format): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:356;s:7:"endLine";i:359;s:3:"ccn";i:1;}s:17:"setDatetimeFormat";a:6:{s:10:"methodName";s:17:"setDatetimeFormat";s:9:"signature";s:39:"setDatetimeFormat(string $format): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:367;s:7:"endLine";i:370;s:3:"ccn";i:1;}s:13:"getDateFormat";a:6:{s:10:"methodName";s:13:"getDateFormat";s:9:"signature";s:23:"getDateFormat(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:377;s:7:"endLine";i:380;s:3:"ccn";i:1;}s:17:"getDatetimeFormat";a:6:{s:10:"methodName";s:17:"getDatetimeFormat";s:9:"signature";s:27:"getDatetimeFormat(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:387;s:7:"endLine";i:390;s:3:"ccn";i:1;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:395;s:7:"endLine";i:398;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:403;s:7:"endLine";i:407;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:412;s:7:"endLine";i:415;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:420;s:7:"endLine";i:423;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:428;s:7:"endLine";i:431;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:436;s:7:"endLine";i:495;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:497;s:18:"commentLinesOfCode";i:156;s:21:"nonCommentLinesOfCode";i:341;}s:15:"ignoredLinesFor";a:1:{i:0;i:15;}s:17:"executableLinesIn";a:173:{i:50;i:7;i:54;i:8;i:57;i:9;i:60;i:10;i:61;i:11;i:62;i:12;i:66;i:13;i:68;i:14;i:70;i:15;i:73;i:16;i:85;i:17;i:87;i:18;i:88;i:19;i:89;i:20;i:90;i:21;i:93;i:22;i:94;i:23;i:95;i:24;i:96;i:25;i:99;i:26;i:100;i:27;i:101;i:28;i:102;i:29;i:105;i:30;i:106;i:31;i:107;i:32;i:108;i:33;i:111;i:34;i:112;i:35;i:113;i:36;i:114;i:37;i:129;i:38;i:130;i:39;i:131;i:40;i:132;i:41;i:134;i:42;i:136;i:43;i:137;i:44;i:142;i:45;i:143;i:46;i:147;i:47;i:148;i:48;i:151;i:49;i:154;i:50;i:158;i:51;i:159;i:52;i:173;i:53;i:175;i:54;i:177;i:55;i:180;i:56;i:182;i:57;i:185;i:58;i:186;i:59;i:190;i:60;i:191;i:61;i:196;i:62;i:207;i:63;i:208;i:64;i:210;i:65;i:211;i:66;i:214;i:67;i:225;i:68;i:226;i:69;i:229;i:70;i:230;i:71;i:235;i:72;i:236;i:73;i:238;i:74;i:239;i:75;i:241;i:76;i:242;i:77;i:244;i:78;i:246;i:79;i:247;i:80;i:248;i:81;i:250;i:82;i:252;i:83;i:253;i:84;i:255;i:85;i:271;i:86;i:273;i:87;i:274;i:88;i:275;i:89;i:276;i:90;i:277;i:91;i:279;i:92;i:283;i:93;i:295;i:94;i:296;i:95;i:299;i:96;i:311;i:97;i:312;i:98;i:316;i:99;i:317;i:100;i:321;i:101;i:322;i:102;i:323;i:103;i:326;i:104;i:337;i:105;i:338;i:106;i:339;i:107;i:340;i:108;i:341;i:109;i:342;i:110;i:343;i:111;i:344;i:112;i:346;i:113;i:358;i:114;i:369;i:115;i:379;i:116;i:389;i:117;i:397;i:118;i:406;i:119;i:414;i:120;i:422;i:121;i:430;i:122;i:438;i:123;i:439;i:123;i:440;i:123;i:441;i:123;i:442;i:123;i:443;i:123;i:444;i:123;i:445;i:123;i:446;i:123;i:447;i:123;i:448;i:123;i:449;i:123;i:450;i:123;i:451;i:123;i:452;i:123;i:453;i:123;i:454;i:123;i:455;i:123;i:456;i:123;i:457;i:123;i:458;i:123;i:459;i:123;i:460;i:123;i:461;i:123;i:462;i:123;i:463;i:123;i:464;i:123;i:465;i:123;i:466;i:123;i:467;i:123;i:468;i:123;i:469;i:123;i:470;i:123;i:471;i:123;i:472;i:123;i:473;i:123;i:474;i:123;i:475;i:123;i:476;i:123;i:477;i:123;i:478;i:123;i:479;i:123;i:480;i:123;i:481;i:123;i:482;i:123;i:483;i:123;i:484;i:123;i:485;i:123;i:486;i:123;i:487;i:123;i:488;i:123;i:489;i:123;i:490;i:123;i:491;i:123;i:492;i:123;i:493;i:123;i:494;i:123;}}