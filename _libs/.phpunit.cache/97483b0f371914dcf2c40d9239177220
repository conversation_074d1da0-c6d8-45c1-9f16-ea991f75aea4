a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:16;s:7:"endLine";i:393;s:7:"methods";a:18:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:76;s:7:"endLine";i:83;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:37:"setModelName(string $modelName): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:91;s:7:"endLine";i:94;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:51:"setModelFactoryName(string $modelFactoryName): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:102;s:7:"endLine";i:105;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:115;s:7:"endLine";i:127;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:117:"createExportData(Outlook $outlook, array $filters, string $modelClass, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:138;s:7:"endLine";i:142;s:3:"ccn";i:1;}s:26:"createExportDataWithTables";a:6:{s:10:"methodName";s:26:"createExportDataWithTables";s:9:"signature";s:129:"createExportDataWithTables(Outlook $outlook, array $filters, string $factoryClass, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:153;s:7:"endLine";i:162;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:173;s:7:"endLine";i:176;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:187;s:7:"endLine";i:203;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:211;s:7:"endLine";i:220;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:231;s:7:"endLine";i:254;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:261;s:7:"endLine";i:268;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:276;s:7:"endLine";i:279;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:286;s:7:"endLine";i:293;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:300;s:7:"endLine";i:303;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:311;s:7:"endLine";i:314;s:3:"ccn";i:1;}s:26:"getReferenceColumnForModel";a:6:{s:10:"methodName";s:26:"getReferenceColumnForModel";s:9:"signature";s:53:"getReferenceColumnForModel(string $modelClass): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:323;s:7:"endLine";i:330;s:3:"ccn";i:2;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:53:"getExportFilename($prefix, string $extension): string";s:10:"visibility";s:9:"protected";s:9:"startLine";i:339;s:7:"endLine";i:358;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:57:"handleExportError(string $message, int $statusCode): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:367;s:7:"endLine";i:392;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:394;s:18:"commentLinesOfCode";i:172;s:21:"nonCommentLinesOfCode";i:222;}s:15:"ignoredLinesFor";a:1:{i:0;i:16;}s:17:"executableLinesIn";a:79:{i:78;i:11;i:79;i:12;i:80;i:13;i:81;i:14;i:82;i:15;i:93;i:16;i:104;i:17;i:117;i:18;i:118;i:18;i:119;i:18;i:120;i:18;i:121;i:18;i:122;i:18;i:123;i:18;i:124;i:18;i:126;i:19;i:140;i:20;i:141;i:21;i:155;i:22;i:158;i:23;i:159;i:24;i:161;i:25;i:175;i:26;i:187;i:27;i:191;i:28;i:193;i:29;i:194;i:30;i:197;i:31;i:198;i:32;i:199;i:33;i:200;i:34;i:201;i:35;i:213;i:36;i:215;i:37;i:216;i:38;i:219;i:39;i:233;i:40;i:234;i:41;i:239;i:42;i:240;i:42;i:241;i:42;i:242;i:42;i:243;i:42;i:246;i:43;i:250;i:44;i:251;i:45;i:263;i:46;i:264;i:47;i:267;i:48;i:278;i:49;i:288;i:50;i:289;i:51;i:292;i:52;i:302;i:53;i:313;i:54;i:325;i:55;i:326;i:56;i:329;i:57;i:341;i:58;i:344;i:59;i:345;i:60;i:348;i:61;i:349;i:62;i:353;i:63;i:354;i:64;i:357;i:65;i:370;i:66;i:371;i:67;i:375;i:68;i:376;i:68;i:377;i:68;i:378;i:68;i:379;i:68;i:382;i:69;i:383;i:70;i:385;i:71;i:386;i:72;i:387;i:73;i:388;i:74;}}