{if !$no_row}
<tr>
  <td colspan="{$cols_count+1}">
{/if}
    <table id="articles_{$idx}_batch" class="t_grouping_table">
      <tr>
        <th>{#num#}</th>
        <th style="min-width: 100px;">{#finance_warehouses_documents_batch#}</th>
        <th style="min-width: 60px;{if $val.vars_settings.available_quantity.width} width: {$val.vars_settings.available_quantity.width}{if preg_match('#^\d+$#', $val.vars_settings.available_quantity.width)}px{/if}{/if}">
          {assign var='var_label' value=$val.vars_settings.available_quantity.label|default:$table.vars.quantity.label|default:#gt2_quantity#|escape}
          {capture assign='var_help'}{if $val.vars_settings.available_quantity.help}{$val.vars_settings.available_quantity.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''}
        </th>
        {if !$val.vars_settings.delivery_price.hidden}
        <th style="{if $val.vars_settings.delivery_price.hidden} display: none;{/if}{if $val.vars_settings.delivery_price.width} width: {$val.vars_settings.delivery_price.width}{if preg_match('#^\d+$#', $val.vars_settings.delivery_price.width)}px{/if}{/if}">
          {assign var='var_label' value=$val.vars_settings.delivery_price.label|default:#finance_documents_types_gt2_last_delivery_price#|escape}
          {capture assign='var_help'}{if $val.vars_settings.delivery_price.help}{$val.vars_settings.delivery_price.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''}
        </th>
        {/if}
        {if $val.has_serial && !$val.vars_settings.serial.hidden}
        <th style="{if $val.vars_settings.serial.width} width: {$val.vars_settings.serial.width}{if preg_match('#^\d+$#', $val.vars_settings.serial.width)}px{/if}{/if}">
          {assign var='var_label' value=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#|escape}
          {capture assign='var_help'}{if $val.vars_settings.serial.help}{$val.vars_settings.serial.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''} {#required#}
        </th>
        {/if}
        {if $val.has_expire && !$val.vars_settings.expire.hidden}
        <th style="{if $val.vars_settings.expire.width} width: {$val.vars_settings.expire.width}{if preg_match('#^\d+$#', $val.vars_settings.expire.width)}px{/if}{/if}">
          {assign var='var_label' value=$val.vars_settings.expire.label|default:#finance_warehouses_documents_expire#|escape}
          {capture assign='var_help'}{if $val.vars_settings.expire.help}{$val.vars_settings.expire.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_helpe label_sufix=''} {#required#}
        </th>
        {/if}
        {if !$val.vars_settings.custom.hidden}
        <th style="{if $val.vars_settings.custom.width} width: {$val.vars_settings.custom.width}{if preg_match('#^\d+$#', $val.vars_settings.custom.width)}px{/if}{/if}">
          {assign var='var_label' value=$val.vars_settings.custom.label|default:#finance_warehouses_documents_custom#|escape}
          {capture assign='var_help'}{if $val.vars_settings.custom.help}{$val.vars_settings.custom.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''}
        </th>
        {/if}
      </tr>
      {foreach from=$val.batch_data item=bo name=r}
      <tr id="articles_{$idx}_batch_{$smarty.foreach.r.iteration}">
        <td style="text-align: right;">
          {$smarty.foreach.r.iteration}
        </td>
        {if empty($bo)}
          <td colspan="6">&nbsp;</td>
        {else}
          <td>
            {$bo.batch_code}
          </td>
          <td style="white-space: nowrap;{if $val.vars_settings.available_quantity.text_align}text-align: {$val.vars_settings.available_quantity.text_align};{/if}">
            {$bo.quantity} / {$bo.available_quantity}
          </td>
          {if !$val.vars_settings.delivery_price.hidden}
          <td style="white-space: nowrap;{if $val.vars_settings.delivery_price.text_align}text-align: {$val.vars_settings.delivery_price.text_align};{/if}">
            {$bo.delivery_price} {$bo.currency}
          </td>
          {/if}
          {if $val.has_serial && !$val.vars_settings.serial.hidden}
          <td style="white-space: nowrap;{if $val.vars_settings.serial.text_align}text-align: {$val.vars_settings.serial.text_align};{/if}">
            {$bo.serial}
          </td>
          {/if}
          {if $val.has_expire && !$val.vars_settings.expire.hidden}
          <td style="white-space: nowrap;{if $val.vars_settings.expire.text_align}text-align: {$val.vars_settings.expire.text_align};{/if}">
            {$bo.expire|date_format:#date_short#}
          </td>
          {/if}
          {if !$val.vars_settings.custom.hidden}
          <td style="white-space: nowrap;{if $val.vars_settings.custom.text_align}text-align: {$val.vars_settings.custom.text_align};{/if}">
            {$bo.custom}
          </td>
          {/if}
        {/if}
      </tr>
      {/foreach}
    </table>
{if !$no_row}
  </td>
</tr>
{/if}