{literal}
<script id="grid-col-status" type="text/x-template">
  ${indicateIfLateTask(properties.status, properties.planned_finish_date)}
  <span class="nz-tooltip-trigger nz-tooltip-autoinit nz-grid-cell-status-text${if(rights.setstatus)} nz-grid-cell-editable${/if}"
      ${if(rights.setstatus)}onclick="changeStatus(${properties.id},&apos;${module}&apos;)"${/if}
      data-tooltip-element="#doc-status-${properties.id}"
      data-tooltip-position="panel: bottom center at: top center">
        ${statusName(properties.status, properties.substatus_name)}
  </span>
  <div id="doc-status-${properties.id}" class="nz-tooltip-content nz-tooltip-content--small nz-tooltip-notch__bottom-center nz-status-info">
    ${statusHelpText(properties.status, properties.substatus_name)}
  </div>
</script>
{/literal}
