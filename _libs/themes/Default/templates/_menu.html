        <ul id="main_menu{if $postfix}_{$postfix}{/if}" class="zpHideOnLoad">
          {foreach name='g' from=$menu item='menu_option' key='key'}
            {capture assign='postfix'}{$postfix}_{$smarty.foreach.g.iteration}{/capture}
            <li id="menu_item{$postfix}"{if $menu_option.selected} class="menu-path"{/if}>
              <img src="{$menu_option.icon}" width="14" height="14" alt="" border="0" />
              <a href="{$menu_option.url|regex_replace:$amp_regex:'&amp;'|escape|replace:'&amp;':'&'}"{if $menu_option.target} target="{$menu_option.target}"{/if} title="{$menu_option.legend}">{$menu_option.i18n}</a>
              {if $menu_option.options}
                {include file=`$theme->templatesDir`_menu.html
                         menu=$menu_option.options
                         postfix=$postfix
                }
              {/if}
            </li>
          {/foreach}
        </ul>