  <div class="floatl">
    <table border="0" cellpadding="0" cellspacing="0" class="t_header t_table_border">
      <tr>
        <td class="t_caption t_border td1" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
        <td class="t_caption t_border td2" nowrap="nowrap"><div class="t_caption_title">{#history_event_type#|escape}</div></td>
        <td class="t_caption t_border td3" nowrap="nowrap"><div class="t_caption_title">{#history_event_text#|escape}</div></td>
        <td class="t_caption td4" nowrap="nowrap"><div class="t_caption_title">{#date#|escape}</div></td>
      </tr>
    {foreach name='i' from=$history item='event'}
      <tr id="history_{$event.h_id}" class="{cycle values='t_odd,t_even'}{if $event.audits} strong{else}{/if}{if !$no_audit} pointer{/if}{if $smarty.foreach.i.first && !$smarty.request.audit || $smarty.request.audit == $event.h_id} attention{/if}"{if !$no_audit} onclick="this.up('table').select('tr').each(function(t) {ldelim} t.removeClassName('attention'); {rdelim}.bind(t)); this.addClassName('attention'); ajaxUpdater({ldelim}link: '{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=audit&amp;audit={$event.h_id}{if $model->get('type')}&amp;model_type={$model->get('type')}{/if}{if $model->get('archived_by')}&amp;archive=1{/if}', target: 'model_audit', highlight: 'model_audit'{rdelim})"{/if}>
        <td class="t_border hright td1">{math equation='x-(y*(z-1))-(c-1)' x=$pagination.total y=$pagination.rpp z=$pagination.page c=$smarty.foreach.i.iteration}</td>
        <td class="t_border td2">{$event.action_type_name}</td>
        <td class="t_border td3">{$event.data|default:"&nbsp;"}</td>
        <td class="td4">{$event.h_date|date_format:#date_mid#|escape}</td>
      </tr>
    {foreachelse}
      <tr class="{cycle values='t_odd,t_even'}">
        <td class="error" colspan="4">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
      <tr>
        <td class="t_footer" colspan="4"></td>
      </tr>
    </table>
    <div class="pagemenu floatr">
    {include file="`$theme->templatesDir`pagination.html"
      found=$pagination.found
      total=$pagination.total
      rpp=$pagination.rpp
      page=$pagination.page
      pages=$pagination.pages
      target="model_history"
      link="`$submitLink`&amp;page="
      use_ajax=1
      hide_rpp=1
      hide_stats=1
    }
    </div>
  </div>
  <br class="clear" />
  <br />
  {if $audit_subpanel_template}
  <div id="model_audit">
    {include file=$audit_subpanel_template}
  </div>
  {/if}
  {if !$no_audit && !($smarty.request.source == 'ajax')}
  <script type="text/javascript">
    if (document.location.href.toQueryParams().audit) $$('#history_' + document.location.href.toQueryParams().audit + '[onclick]').each(function(el){ldelim} document.location.hash = '#' + el.id; {rdelim});
  </script>
  {/if}