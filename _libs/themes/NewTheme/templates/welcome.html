{if $validLogin}
  {capture assign='info'}
    <strong>{#welcome_name#|escape}:</strong> {$currentUser->get('firstname')|escape} {$currentUser->get('lastname')|escape}<br />
    <strong>{#welcome_username#|escape}:</strong> {$currentUser->get('username')}<br />
    <strong>{#welcome_role#|escape}:</strong>
    {$currentUser->get('role_name')|escape}
    <br />
    <strong>{#welcome_last_login#|escape}:</strong> {if $currentUser->get('last_login') ne '0000-00-00 00:00:00'}{$currentUser->get('last_login')|date_format:#date_mid#|escape}{else}{#welcome_no_login_yet#|escape}{/if}<br />
    <strong>{#welcome_remote_addr#|escape}:</strong> {$currentUser->get('remote_addr')|default:#welcome_no_login_yet#|escape}<br />
  {/capture}
  <img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#system_info#|escape}" class="help" {popup text=$info|escape caption=#system_info#|escape} /> {#welcome#|escape}, {$currentUser->get('firstname')|escape}
{else}
  &nbsp;
{/if}
