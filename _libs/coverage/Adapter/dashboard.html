<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Adapter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Adapter</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#932"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#973"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1007"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1028"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableHeaders">addTableHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1045"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableNamedRanges">addTableNamedRanges</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1078"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processTableRecord">processTableRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1102"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeTableColumns">finalizeTableColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#202"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">11%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#867"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#891"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">36%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#754"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">46%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#568"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#394"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">68%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#202"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">15</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#932"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createTableWorksheet">createTableWorksheet</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#973"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::populateTableWorksheet">populateTableWorksheet</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1045"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableNamedRanges">addTableNamedRanges</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1078"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processTableRecord">processTableRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#891"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::collectTablesByType">collectTablesByType</abbr></a></td><td class="text-right">11</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#867"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportTables">processExportTables</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#754"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">8</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#394"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">7</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#568"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1007"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableDataToWorksheet">addTableDataToWorksheet</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1028"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addTableHeaders">addTableHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#1102"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeTableColumns">finalizeTableColumns</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">3</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 13:46:57 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,0,0,0,0,0,0,0,1,1,2,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,1,1,1,1,0,3,6,7,11,58], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[83.51648351648352,37,"<a href=\"AbstractExportFormatAdapter.php.html#13\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[95.93023255813954,76,"<a href=\"CsvExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[73.56115107913669,242,"<a href=\"ExcelExportFormatAdapter.php.html#27\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[97.46192893401016,69,"<a href=\"JsonExportFormatAdapter.php.html#15\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,2,"<a href=\"AbstractExportFormatAdapter.php.html#44\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[100,1,"<a href=\"AbstractExportFormatAdapter.php.html#61\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[100,5,"<a href=\"AbstractExportFormatAdapter.php.html#74\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"AbstractExportFormatAdapter.php.html#104\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[84.61538461538461,3,"<a href=\"AbstractExportFormatAdapter.php.html#131\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[100,5,"<a href=\"AbstractExportFormatAdapter.php.html#163\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[100,1,"<a href=\"AbstractExportFormatAdapter.php.html#183\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[100,3,"<a href=\"AbstractExportFormatAdapter.php.html#195\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[100,2,"<a href=\"AbstractExportFormatAdapter.php.html#215\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[94.11764705882352,5,"<a href=\"AbstractExportFormatAdapter.php.html#233\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[83.33333333333334,3,"<a href=\"AbstractExportFormatAdapter.php.html#275\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[90,4,"<a href=\"AbstractExportFormatAdapter.php.html#297\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[100,3,"<a href=\"CsvExportFormatAdapter.php.html#50\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[90.47619047619048,19,"<a href=\"CsvExportFormatAdapter.php.html#83\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::extractCsvOptions<\/a>"],[93.33333333333333,6,"<a href=\"CsvExportFormatAdapter.php.html#126\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::writeCsvContent<\/a>"],[100,5,"<a href=\"CsvExportFormatAdapter.php.html#171\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportDataRecords<\/a>"],[100,2,"<a href=\"CsvExportFormatAdapter.php.html#205\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::processExportRecord<\/a>"],[94.44444444444444,12,"<a href=\"CsvExportFormatAdapter.php.html#223\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatValueForCsv<\/a>"],[87.5,7,"<a href=\"CsvExportFormatAdapter.php.html#267\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::formatDateValue<\/a>"],[100,2,"<a href=\"CsvExportFormatAdapter.php.html#292\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDecimalPlaces<\/a>"],[75,4,"<a href=\"CsvExportFormatAdapter.php.html#308\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[100,6,"<a href=\"CsvExportFormatAdapter.php.html#335\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#356\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDateFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#367\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::setDatetimeFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#377\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDateFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#387\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDatetimeFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#395\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#403\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#412\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#420\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#428\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#436\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[93.75,4,"<a href=\"ExcelExportFormatAdapter.php.html#61\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[72.72727272727273,10,"<a href=\"ExcelExportFormatAdapter.php.html#112\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#145\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[60,3,"<a href=\"ExcelExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[11.11111111111111,4,"<a href=\"ExcelExportFormatAdapter.php.html#202\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[87.5,2,"<a href=\"ExcelExportFormatAdapter.php.html#253\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#274\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[100,2,"<a href=\"ExcelExportFormatAdapter.php.html#303\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeadersWithHiddenId<\/a>"],[75,4,"<a href=\"ExcelExportFormatAdapter.php.html#327\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addNamedRanges<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#378\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[68.75,6,"<a href=\"ExcelExportFormatAdapter.php.html#394\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#441\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[77.77777777777779,3,"<a href=\"ExcelExportFormatAdapter.php.html#454\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processMainSheetRecord<\/a>"],[100,5,"<a href=\"ExcelExportFormatAdapter.php.html#482\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getRecordIdFromMetadata<\/a>"],[75,17,"<a href=\"ExcelExportFormatAdapter.php.html#507\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[60,5,"<a href=\"ExcelExportFormatAdapter.php.html#568\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[89.47368421052632,15,"<a href=\"ExcelExportFormatAdapter.php.html#596\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#647\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#670\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#701\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#718\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[100,2,"<a href=\"ExcelExportFormatAdapter.php.html#730\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeMainSheetColumnsWithHiddenId<\/a>"],[46.15384615384615,5,"<a href=\"ExcelExportFormatAdapter.php.html#754\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[76,8,"<a href=\"ExcelExportFormatAdapter.php.html#791\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#848\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[28.57142857142857,4,"<a href=\"ExcelExportFormatAdapter.php.html#867\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportTables<\/a>"],[36.84210526315789,5,"<a href=\"ExcelExportFormatAdapter.php.html#891\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::collectTablesByType<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#932\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createTableWorksheet<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#959\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::sanitizeWorksheetName<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#973\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::populateTableWorksheet<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#1007\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableDataToWorksheet<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#1028\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableHeaders<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#1045\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addTableNamedRanges<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#1078\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processTableRecord<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#1102\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeTableColumns<\/a>"],[100,4,"<a href=\"ExcelExportFormatAdapter.php.html#1124\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[88.88888888888889,3,"<a href=\"ExcelExportFormatAdapter.php.html#1145\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[100,83,"<a href=\"ExcelExportFormatAdapter.php.html#1173\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[100,3,"<a href=\"ExcelExportFormatAdapter.php.html#1275\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[90,4,"<a href=\"ExcelExportFormatAdapter.php.html#1298\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1321\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[100,4,"<a href=\"ExcelExportFormatAdapter.php.html#1329\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1346\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1354\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1362\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#1370\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[100,3,"<a href=\"JsonExportFormatAdapter.php.html#30\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[100,7,"<a href=\"JsonExportFormatAdapter.php.html#63\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::extractJsonOptions<\/a>"],[92.85714285714286,5,"<a href=\"JsonExportFormatAdapter.php.html#87\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::writeJsonContent<\/a>"],[100,5,"<a href=\"JsonExportFormatAdapter.php.html#125\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareJsonData<\/a>"],[90.9090909090909,5,"<a href=\"JsonExportFormatAdapter.php.html#144\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareArrayStructure<\/a>"],[100,2,"<a href=\"JsonExportFormatAdapter.php.html#172\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareObjectStructure<\/a>"],[100,6,"<a href=\"JsonExportFormatAdapter.php.html#197\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::prepareNestedStructure<\/a>"],[88.88888888888889,3,"<a href=\"JsonExportFormatAdapter.php.html#229\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::processExportRecord<\/a>"],[100,9,"<a href=\"JsonExportFormatAdapter.php.html#255\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatValueForJson<\/a>"],[90.9090909090909,7,"<a href=\"JsonExportFormatAdapter.php.html#293\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::formatDateValue<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#320\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::addMetadata<\/a>"],[100,3,"<a href=\"JsonExportFormatAdapter.php.html#334\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMetadata<\/a>"],[96.29629629629629,7,"<a href=\"JsonExportFormatAdapter.php.html#369\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#424\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#432\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#441\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#449\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#457\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#465\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
