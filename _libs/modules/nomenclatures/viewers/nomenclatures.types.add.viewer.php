<?php

class Nomenclatures_Types_Add_Viewer extends Viewer {
    public $template = 'types_add.html';

    public function prepare() {
        $this->model = $this->registry['nomenclatures_type'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare sections
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.sections.factory.php';
        $filters = array('model_lang' => $this->model->get('model_lang'),
                         'where' => array('ns.active = 1'),
                         'sanitize' => true);
        $this->data['type_section'] = Nomenclatures_Sections::search($this->registry, $filters);

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare counters
        require_once $this->modelsDir . 'nomenclatures.counters.factory.php';
        $filters = array('sanitize' => true);
        $this->data['counters'] = Nomenclatures_Counters::search($this->registry, $filters);

        //prepare categories tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = array_values(Nomenclatures_Categories::getTree($this->registry, array('sanitize' => true)));

        //get type categories
        $this->data['cats'] = $this->model->getCategories();

        $this->registry['include_tree'] = true;

        // get all available validate unique fields
        $validate_unique_fields = $this->registry['config']->getParamAsArray($this->module, 'validate_unique_fields');
        $validate_unique_options = array();
        foreach ($validate_unique_fields as $field) {
            $validate_unique_options[] = array(
                'label' => $this->i18n($this->module . '_' . $field),
                'option_value' => 'unique_' . $field,
                'active_option' => true
            );
        }
        $this->data['validate_unique_options'] = $validate_unique_options;
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures_types_add');
        $this->data['title'] = $title;
    }
}

?>
