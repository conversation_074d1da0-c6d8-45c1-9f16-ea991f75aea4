<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="groups" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=groups" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#groups_name#|escape}</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#groups_description#|escape}</div></td>
          <td class="t_caption t_border {$sort.manager.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.manager.link}">{#groups_manager#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#date#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach name='i' from=$groups item='group'}
      {strip}
      {capture assign='info'}
        <strong>{#groups_name#|escape}:</strong> {$group->get('name')|escape}<br />
        <strong>{#groups_manager#|escape}:</strong> {$group->get('manager_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$group->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$group->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$group->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$group->get('modified_by_name')|escape}<br />
        {if $group->isDeleted()}<strong>{#deleted#|escape}:</strong> {$group->get('deleted')|date_format:#date_mid#|escape}{if $group->get('deleted_by_name')} {#by#|escape} {$group->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$group->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $group->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$group->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$group->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="4" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$group disabled='edit,delete,view'}
          </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$group->get('active')} t_inactive{/if}{if $group->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: '{$pagination.total}'
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$group->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($group->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($group->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$group->get('id')}">{$group->get('name')|escape|default:"&nbsp;"|indent:$group->get('level'):"---"}</a></td>
          <td class="t_border">{$group->get('description')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted}">{$group->get('manager_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added.isSorted}">{$group->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$group}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html session_param=$session_param|default:$pagination.session_param exclude='multiedit'}
      </form>
    </td>
  </tr>
</table>

<br /><br />
<h1><img src="{$theme->imagesUrl}relations.png" width="16" height="16" border="0" title="{#documents_tree#|escape}" alt="{#documents_tree#|escape}" class="help" /> {#groups_tree#|escape}</h1>
<script type='text/javascript'>
var func = function() {ldelim}initTree(''){rdelim}
Event.observe(window, 'load', func);
</script>

<div id="tree_container" class="tree_container">
<a href="javascript:Zapatec.Tree.all['tree_'].collapseAll()">{#collapse_all#|escape}</a> |
<a href="javascript:Zapatec.Tree.all['tree_'].expandAll()">{#expand_all#|escape}</a>
<ul id="tree_">
{strip}
{foreach name='i' from=$groups item='tree' key='idx'}
{assign var='level' value=$tree->get('level')}
{if $smarty.foreach.i.last}
  {assign var='next_level' value=0}
{else}
  {assign var='next_idx' value=$idx+1}
  {assign var='next_level' value=$groups[$next_idx]->get('level')}
{/if}
<li{if $tree->isDeleted() || !$tree->isActivated()} class="inactive_option" title="{#inactive_option#}"{/if}>{$tree->get('name')|escape}
{if $next_level > $level}
   <ul>
{elseif $next_level eq $level}
   </li>
{else}
   {repeat string='</li></ul>' num=$level-$next_level}</li>
{/if}
{/foreach}
{/strip}
</ul>
</div>
