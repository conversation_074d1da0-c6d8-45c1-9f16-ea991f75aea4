<table cellspacing="0" cellpadding="5" border="1">
  <tr>
    <th>{#about#|escape}</th>
    {if isset($events.0.type)}
      <th>{#events_type#|escape}</th>
    {/if}
    {if isset($events.0.customer)}
      <th>{#events_customer#|escape}</th>
    {/if}
    <th>{#events_start#|escape}</th>
    <th>{#events_end#|escape}</th>
    {if $events.0.user_status}
    <th>{#events_user_status#|escape}</th>
    {/if}
    {if $events.0.finish_url}
    <th>{#events_finish#|escape}</th>
    {/if}
  </tr>
{foreach name='i' from=$events item='event'}
  <tr>
    <td><a href="{$event.view_url}">{$event.name}</a></td>
    {if isset($event.type)}
      <td>{$event.type|default:'&nbsp;'}</td>
    {/if}
    {if isset($event.customer)}
      <td>{$event.customer|default:'&nbsp;'}</td>
    {/if}
    <td>{if $event.allday_event}{$event.event_start|date_format:#date_short#} ({if $event.allday_event == -1}{$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#allday_event#}{/if}){else}{$event.event_start|date_format:#date_mid#}{/if}</td>
    <td>{if $event.allday_event}{$event.event_end|date_format:#date_short#} ({if $event.allday_event == -1}{$event.duration} {if abs($event.duration) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#allday_event#}{/if}){else}{$event.event_end|date_format:#date_mid#}{/if}</td>
    {if $event.user_status}
    {capture assign='user_status_var'}event_status_{$event.user_status}{/capture}
    <td>{$smarty.config.$user_status_var}</td>
    {/if}
    {if $event.finish_url}
    <td><a href="{$event.finish_url}">{if $event.type_keyword == 'plannedtime'}{#addtimesheet_and_finish_event#}{else}{#finish_event#}{/if}</a></td>
    {/if}
  </tr>
{/foreach}
</table>
