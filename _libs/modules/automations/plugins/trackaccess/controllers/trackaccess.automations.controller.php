<?php
include_once 'traits/updateWorkTimeFromDb.trait.php';
include_once 'traits/updateWorkedTimeFromApi.trait.php';

class Trackaccess_Automations_Controller extends Automations_Controller
{

    use updateWorkTimeFromDbTrait;
    use updateWorkedTimeFromApiTrait;

    public function updateWorkedTime($params)
    {

        //take the response from the api
        $response = $this->apiRequest();
        if (!$response) {
            $this->executionErrors[] = $this->i18n('error_while_requesting_data');
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        //format the returned data as we need
        $fdata = $this->formatApiData($response);
        if (!$fdata) {
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        //record the new data with the help of the document model
        $registry = $this->registry;
        $documentType = $this->settings['document_type'];
        $date = "-{$this->settings['worktime_days_before']} days";
        $date = new DateTime($date);
        $date = $date->format('Y-m-d');
        $filters = array(
            'where' => array(
                "d.type = {$documentType}",
                "d.date = '{$date}'"
            )
        );
        $workedTimeDocument = Documents::searchOne($registry, $filters);
        $rowCount = count($fdata);
        $action = 'edit';

        //if no Document found for the specified day we start building new one.
        if (!$workedTimeDocument) {
            $action = 'add';
            //start building new document
            $workedTimeDocument = new Document($this->registry, array('type' => $documentType));
            $workedTimeDocument->set('date', $date);
            $filters = array(
                'where' => array(
                    "dt.id = {$documentType}",
                    'dt.active = 1',
                    'dt.inheritance = 0'
                ),
                'sanitize' => true,
                'model_lang' => $registry['lang']
            );
            $docType = Documents_Types::searchOne($registry, $filters);
            $workedTimeDocument->set('name', $docType->get('default_name'));
            $workedTimeDocument->set('customer', $docType->get('default_customer'));

            if ($workedTimeDocument->save() == false) {
                $this->executionErrors[] = $this->i18n('error_while_saving');
                $this->updateAutomationHistory($params, $workedTimeDocument, 0);
                return false;
            }
        }
        $registry->set('get_old_vars', true, true);
        $workedTimeDocument->getVars();
        $registry->set('get_old_vars', false, true);
        $oldDocument = clone $workedTimeDocument;
        $assocVars = $workedTimeDocument->getAssocVars();

        //we set the additional vars from the data
        //for every row from the new data we put the value as rowNum=>val
        //IMPORTANT: replace the entire group table
        foreach ($fdata[0] as $varName => $notImportant) {
            if (array_key_exists($varName, $assocVars) && $assocVars[$varName]['grouping'] != 0) {
                $varValues = array_column($fdata, $varName);
                //reindex values array to start from 1
                $assocVars[$varName]['value'] = array_combine(
                    range(1, count($varValues)),
                    $varValues
                );
            }
        }

        $workedTimeDocument->set('vars', array_values($assocVars), true);

        if ($workedTimeDocument->saveVars() && $workedTimeDocument->save()) {
            $filters = array('where' => array('d.id = ' . $workedTimeDocument->get('id')),
                'sanitize' => true);
            $new_model = Documents::searchOne($registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_model->getVars();
            $this->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');
            $this->updateAutomationHistory($params, $workedTimeDocument, 1);
            $audit_parent = Documents_History::saveData($registry,
                array('model' => $workedTimeDocument,
                    'action_type' => $action,
                    'new_model' => $new_model,
                    'old_model' => $oldDocument));
            return true;
        } else {
            $this->executionErrors[] = $this->i18n('error_while_saving');
            $this->updateAutomationHistory($params, $workedTimeDocument, 0);
            return false;
        }
    }

    public function updateWorkedTimeFromDb($params)
    {
        $date = "-{$this->settings['worktime_days_before']} days";
        $date = new DateTime($date);
        if(isset($this->settings['testDate']) && !empty($this->settings['testDate'])) {
            $date = new DateTime($this->settings['testDate']);
        }
        $date = $date->format('Y-m-d');
        General::log($this->registry, __METHOD__, sprintf($this->i18n('starting_message'), $date));
        if ($this->settings['mode'] === 'test') {
            $communicator = $this->proxyConnect();
        } else {
            $communicator = $this->dbConnect();
            if (!$communicator->connected()) {
                General::log($this->registry, __METHOD__, $this->i18n('error_while_connecting'));
                $this->executionErrors[] = $this->i18n('error_while_connecting');
                $this->updateAutomationHistory($params, 0, 0);
                return false;
            }
        }
        $q = <<<SQL
        SELECT w.*
        FROM worktime_v2 AS w
        WHERE w.for_day = '$date'
        SQL;
        $results = $communicator->getResults($q);
        General::log($this->registry, get_class($communicator), "QUERY: " . $q);
        General::log($this->registry, get_class($communicator), "RESULTS COUNT: " . count($results));
        $action = 'edit';
        $workedTimeDocument = $this->_getWorkedTimeDocument($action, $date);
        $this->registry->set('get_old_vars', true, true);
        $workedTimeDocument->getVars();
        $this->registry->set('get_old_vars', false, true);
        $oldDocument = clone $workedTimeDocument;
        $assocVars = $workedTimeDocument->getAssocVars();

        $fData = $this->_formatDbData($results);

        foreach ($fData[0] ?? [] as $varName => $irrelevant)
        {
            if (array_key_exists($varName, $assocVars) && $assocVars[$varName]['grouping'] != 0) {
                $varValues = array_column($fData, $varName);
                //reindex values array to start from 1
                $assocVars[$varName]['value'] = array_combine(
                    range(1, count($varValues)),
                    $varValues
                );
            }

        }

        $workedTimeDocument->set('vars', array_values($assocVars), true);
        if ($workedTimeDocument->saveVars() && $workedTimeDocument->save()) {
            $filters = array('where' => array('d.id = ' . $workedTimeDocument->get('id')),
                'sanitize' => true);
            $new_model = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_model->getVars();
            $this->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');
            $this->updateAutomationHistory($params, $workedTimeDocument, 1);
            $audit_parent = Documents_History::saveData($this->registry,
                array('model' => $workedTimeDocument,
                    'action_type' => $action,
                    'new_model' => $new_model,
                    'old_model' => $oldDocument));
            return true;
        }
        General::log($this->registry, __METHOD__, $this->i18n('error_while_saving'));
        $this->executionErrors[] = $this->i18n('error_while_saving');
        $this->updateAutomationHistory($params, $workedTimeDocument, 0);
        return false;
    }


    public function resyncWorkTimeDb($params)
    {
        if (empty($this->settings['resyncStart'])) {
            General::log($this->registry, __METHOD__, $this->i18n('missing_resync_start_setting'));
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        if (!Validator::validDate($this->settings['resyncStart'])) {
            General::log($this->registry, __METHOD__, $this->i18n('incorrect_resync_start_setting'));
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }


        if ($this->settings['mode'] === 'test') {
            $communicator = $this->proxyConnect();
        } else {
            $communicator = $this->dbConnect();
            if (!$communicator->connected()) {
                General::log($this->registry, __METHOD__, $this->i18n('error_while_connecting'));
                $this->executionErrors[] = $this->i18n('error_while_connecting');
                $this->updateAutomationHistory($params, 0, 0);
                return false;
            }
        }

        $insParams = [
            'start' => $this->settings['resyncStart'],
            'end' => $this->settings['resyncStart'],
        ];
        if (!empty($this->settings['resyncEnd'])) {
            if (Validator::validDate($this->settings['resyncEnd'])) {
                if (strtotime($this->settings['resyncEnd']) > strtotime($this->settings['resyncStart'])) {
                    $insParams['end'] = $this->settings['resyncEnd'];
                } else {
                    General::log($this->registry, __METHOD__, $this->i18n('incorrect_resync_end_setting'));
                }
            } else {
                General::log($this->registry, __METHOD__, $this->i18n('incorrect_resync_end_setting'));
            }
        }
        General::log($this->registry, __METHOD__, sprintf($this->i18n('started_resync_period_process'), $insParams['start'], $insParams['end']));
        $ins = <<< SQL
        INSERT INTO resync (start_date, end_date) VALUES ('{$insParams['start']}', '{$insParams['end']}');
        SQL;
        $results = $communicator->execute($ins);
        $q = <<<SQL
        SELECT * FROM resync WHERE end_date = '{$insParams['end']}' AND start_date = '{$insParams['start']}'
        SQL;
        while (empty($communicator->getResults($q))){
            sleep(30);
        }
        General::log($this->registry, __METHOD__, sprintf($this->i18n('finished_resync_period_process'), $insParams['start'], $insParams['end']));
        $this->updateAutomationHistory($params, 0, 0);
        return true;
    }

    private function _getWorkedTimeDocument(&$action, $date)
    {
        $documentType = $this->settings['document_type'];
        $filters = array(
            'where' => array(
                "d.type = {$documentType}",
                "d.date = '{$date}'"
            )
        );
        $workedTimeDocument = Documents::searchOne($this->registry, $filters);



        //if no Document found for the specified day we start building new one.
        if (!$workedTimeDocument) {
            $action = 'add';
            //start building new document
            $workedTimeDocument = new Document($this->registry, array('type' => $documentType));
            $workedTimeDocument->set('date', $date);
            $filters = array(
                'where' => array(
                    "dt.id = {$documentType}",
                    'dt.active = 1',
                    'dt.inheritance = 0'
                ),
                'sanitize' => true,
                'model_lang' => $this->registry['lang']
            );
            $docType = Documents_Types::searchOne($this->registry, $filters);
            $workedTimeDocument->set('name', $docType->get('default_name'));
            $workedTimeDocument->set('customer', $docType->get('default_customer'));

            if (!$workedTimeDocument->save()) {
                return false;
            }
        }
        return $workedTimeDocument;
    }
}
