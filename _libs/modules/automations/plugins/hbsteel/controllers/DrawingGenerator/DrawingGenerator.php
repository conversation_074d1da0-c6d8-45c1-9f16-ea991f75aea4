<?php

/**
 * Object to generate drawings from a template like nomenclature
 * that defines the image background and the positions of the parameters
 * <AUTHOR>
 */
class DrawingGenerator
{
    public const SETTINGS_TYPE_REQUIRED = 'required';
    public const SETTINGS_TYPE_OTHER = 'other';

    public const DEFAULT_FONTSIZE = 11;
    public const DEFAULT_FONTPATH = PH_BASE_LIBS_DIR . '/inc/ext/html2ps/fonts/DroidSans.ttf';
    public const DEFAULT_COLOR = [0, 0, 0];

    public const DEFAULT_FORMAT = 'jpg';
    public const DEFAULT_QUALITY = 95;
    public const DEFAULT_FILENAME = 'Drawing_[row]';

    private $registry;
    private array $settings;
    private $parsedSettings;

    private static $settings_map = [
        self::SETTINGS_TYPE_REQUIRED => [
            'templateNomIdCol' => 'template_col_id_var_name',
            'templateNom_fileVarName' => 'template_nom__file_var_name',
            'templateNom_numVarName' => 'template_nom__num_var_name',
            'templateNom_xVarName' => 'template_nom__x_var_name',
            'templateNom_yVarName' => 'template_nom__y_var_name',

            'paramColNameTemplate' => 'param_col_name_template',
            'destinationCol' => 'destination_col_var_name',
        ],
        self::SETTINGS_TYPE_OTHER => [
            'fontPath' => 'font_path',
            'fontSize' => 'font_size',
            'fontColor' => 'font_color',
            'unlinkReplacedFiles' => 'unlink_replaced_files',
            'outputFormat' => 'output_format',
            'outputQuality' => 'output_quality',
            'outputFilenameTemplate' => 'output_filename_template'
        ]
    ];



    private static $logTemplates = [
        'missingField' => "The field '%s' (that was set in settings with key '%s') is not found in the "
            ."%s with ID '%s' and type '%s'!"
    ];
    private $renderer;
    private $filenameTemplate;
    private $filenameTranslationMap;

    /**
     * The settings can be as follows:
     * [required]
     *  'template_col_id_var_name' =>  <String - the name variable in the document, that contains the id for the nomenclature to be used as a template. Should be a var ina group table>
     *  'template_nom__file_var_name' => <String - The name of the var in the nomencalture, where the template image is uploaded>
     *  'template_nom__num_var_name' => <String - The name of the var in the nomenclature, where a parameter ID is set. Should be part of a group table>
     *  'template_nom__x_var_name' =>  <Stsing - The name of the var in the nomenclature, where a parameter X coordinate is set. Should be part of a group table>
     *  'template_nom__y_var_name' =>  <Stsing - The name of the var in the nomenclature, where a parameter Y coordinate is set. Should be part of a group table>
     *
     *  'param_col_name_template' =>  <Stsing - A template string for the name of the parameter variables. For example 'form_parameter%d' meas that the variables, containing ieach parameter have names startin with 'form_parameter', and concatenated with the parameter ID as set in the nomenclatures 'template_nom__num_var_name' column. 'form_parameter1', ''form_parameter2', ...>
     *  'destination_col_var_name' => <String - the name variable in the document, that should receive the new image file. This variable should be a fileupload. Optionaly can be set up to be image and for thumbnails >
     *
     * [optional]
     *  'font_path' => <String [default: '[LIBS]/inc/ext/html2ps/fonts/DroidSans.ttf'] - path to the font file on the server use '[LIBS]' for the instance path to the _libs folder>
     *  'font_size' => <int [default: 11] - the font size in points>
     *  'font_color' => <String [default: 0,0,0] (black)] - 3 int values delimeted by a coma (,) representing Red Green And Blue values for the font color>
     *  'unlink_replaced_files' => <bool [default: false] - true if the images should be deleted physically when generating new ones>
     *  'output_format' => <String [default: jpg] - one of 3 variants ('jpg','png','gif')>
     *  'output_quality' => <int [default: 95] - for jpg format, represents the quality, for png format represents the compression level but in %>
     *  'output_filename_template' => <String [default: 'Drawing_[row]'] - the template to generate a filename for the new drawings. available variables are all the document variables + [row]>
     *
     * @param Registry $registry
     * @param array $settings
     */
    public function __construct($registry, $settings)
    {
        $this->registry = $registry;
        $this->settings = $settings;
    }

    /**
     * Generates drawings from template, nomenclature for all rows in a document
     * and attaches the file to a field at hte same row.
     *
     * @return boolean - result of the operation
     * @throws Exception
     */
    function generateDrawings(Document $document, $prevDocument) : bool
    {
        include_once 'Shape.php';
        include_once 'DrawingRenderer.php';

        $settings = $this->getSettings();

        // Configure the Shape resource
        Shape::configure(
            $settings['templateNom_fileVarName'],
            $settings['templateNom_numVarName'],
            $settings['templateNom_xVarName'],
            $settings['templateNom_yVarName']
        );

        // simplify the condition that will be used in the loop
        $shouldUnlinkFiles = !empty($settings['unlinkReplacedFiles'])
            && ($settings['unlinkReplacedFiles'] === 'true' || $settings['unlinkReplacedFiles'] === '1');

        // Extract the nomenclature Id's and then get the Nom models
        $tempNomIds = $this->getTemplateNomIdsFromDocument($document);
        $tempNomIdsFiltered = array_values(array_unique(array_filter($tempNomIds)));

        // If no shapes selected in the document, noting to do
        if (empty($tempNomIdsFiltered)) {
            return true;
        }

        $nomenclatures = $this->getNomenclatures($tempNomIdsFiltered );

        $docVars = $document->getAssocVars();
        $prevDocVars = $prevDocument->getAssocVars();

        $shapes = [];
        $deleteFilesIds = [];
        $deleteFilesPaths = [];
        $nomChecked = false;
        foreach ($tempNomIds as $row => $nomId) {
            $ShouldUpdateRow = false;
            // The nomenclature doesn't exist. This is a problem,
            // and probably can show some message to teh user, or write a log...
            if (!array_key_exists($nomId, $nomenclatures)) {
                continue;
            }

            $nom = $nomenclatures[$nomId];
            $shape = new Shape($nom);

            // Check only once, as the nomenclatures should be from the same type.
            if (!$nomChecked) {
                $this->shapeValidOrThrow($shape);
                $nomChecked = true;
            }

            // If the nomenclature is updated, in the last minute, then the user is probably trying to test and configure the parameters
            $nomModifiedDate = DateTime::createFromFormat('Y-m-d H:i:s', $nom->get('modified'));
            if ($nomModifiedDate && (new DateTime())->format('U')-$nomModifiedDate->format('U') < 60) {
                $ShouldUpdateRow = true;
            } else {
                // If the selected nomenclature ID is changed
                if (($prevDocVars[$settings['templateNomIdCol']]['value'] ?? null) !== ($docVars[$settings['templateNomIdCol']]['value'] ?? null)) {
                    $ShouldUpdateRow = true;
                } else {
                    // if any parameter is updated
                    $ShouldUpdateRow = $this->getRowUpdatedStatus($shape, $document, $prevDocument, $row);
                }
            }

            // Only update rows/shapes that satisfy above conditions
            if ($ShouldUpdateRow) {
                // If prevous image exist, colect the ID for deletion. If $shouldUnlinkFiles - unlink the file
                if (array_key_exists('value', $docVars[$settings['destinationCol']]) && array_key_exists($row, $docVars[$settings['destinationCol']]['value'])) {
                    $oldFile = $docVars[$settings['destinationCol']]['value'][$row];
                    if (is_object($oldFile) && is_a($oldFile, File::class)) {
                        $deleteFilesIds[] = $oldFile->get('id');
                        // If the file should be removed physically
                        if ($shouldUnlinkFiles && is_file($oldFilePath = $oldFile->get('path'))) {
                            $deleteFilesPaths[] = $oldFilePath;
                        }
                    }
                }
                // Prep the shapes to be drowned
                $shapes[$row] = $shape;
            }
        }

        // Files records are marked as deleted
        if (!empty($deleteFilesIds)) {
            Files::delete($this->registry, $deleteFilesIds);
        }

        $renderer = $this->getDrawingRenderer();

        // Actually drawing shapes, create File records update the models variables
        foreach ($shapes as $row => $shape) {
            if (!array_key_exists('value', $docVars[$settings['destinationCol']])) {
                $docVars[$settings['destinationCol']]['value'] = [];
            }
            // Draw the parameters and save to tmp file
            $tmpFileName = $renderer->renderToFile($shape, $this->getDataArrayForRow($shape, $document, $row));

            // Create the File record and attach the image to the document. Returns the ID of the File
            $nzImageId = $this->attachFile($this->generateFileName($document, $row), $tmpFileName, $document);
            if ($nzImageId) {
                // Assign the new File to the destination field
                $docVars[$settings['destinationCol']]['value'][$row] = $nzImageId;
            }
        }

        // Update the Model
        $document->set('vars', array_values($docVars), true);
        $result = $document->edit();

        if ($result && !empty($deleteFilesPaths)) {
            // Make sure to delete files after the model is saved successfully
            foreach ($deleteFilesPaths as $path) {
                unlink($path);
            }
        }

        return $result;
    }

    private function getSettings()
    {
        if (!is_null($this->parsedSettings)) {
            return $this->parsedSettings;
        }

        $settings = [];
        foreach (self::$settings_map as $type=>$map) {
            $requiredFlag = $type === self::SETTINGS_TYPE_REQUIRED;
            foreach ($map as $k=>$v) {
                if ($requiredFlag && !array_key_exists($v, $this->settings)) {
                    throw new \Exception("Required setting '{$v}' is missing!");
                }
                $settings[$k] = $this->settings[$v]??null;
            }
        }
        return $this->parsedSettings = $settings;
    }

    /**
     * Return the nomenclatures with the given Ids in an array that has the ID-s for a key and the model for a value
     *
     * @param array $tempNomIds
     * @return ?Nomenclature[]
     */
    private function getNomenclatures(array $tempNomIds): ?array
    {
        $filters = array(
            'where' => array(
                "n.id IN (" . implode(',', $tempNomIds) . ")",
            ),
        );
        $nomenclatures = Nomenclatures::search($this->registry, $filters);
        $result=[];
        if($nomenclatures) {
            foreach ($nomenclatures as $nomenclature) {
                $result[$nomenclature->get('id')] = $nomenclature;
            }
        }
        return $result;
    }

    /**
     * Compares the 2 documents parameters and Returns true if the specifyed row is modified by the user.
     *
     * @param Shape $shape
     * @param $docVars Document
     * @param $prevDocument Document
     * @return bool
     * @throws Exception
     */
    private function getRowUpdatedStatus(Shape $shape, Document $document, Document $prevDocument, $row): bool
    {
        $settings = $this->getSettings();
        $params = $shape->getParams();
        $docVars = $document->getAssocVars();
        $prevDocVars = $prevDocument->getAssocVars();
        $rowUpdateStatus = false;
        foreach ($params as $p) {
            $id = (string)$p->getId();
            $varName = sprintf($settings['paramColNameTemplate'], $id);
            $rowUpdateStatus = $rowUpdateStatus || (array_key_exists($varName, $docVars) && array_key_exists('value', $docVars[$varName])
                    && (array_key_exists($varName, $prevDocVars) && array_key_exists('value', $prevDocVars[$varName]))
                    && ($docVars[$varName]['value'][$row] ?? '') !== ($prevDocVars[$varName]['value'][$row] ?? ''));
        }
        return $rowUpdateStatus;
    }

    /**
     * Returns an array of nomenclatures ids from the configured field (templateNomIdCol) in the document
     * Throws an exception if the configured field doesn't exist.
     * Can return empty array
     *
     * @param Document $document
     * @return array
     * @throws Exception
     */
    private function getTemplateNomIdsFromDocument(Document $document): array
    {
        $settings = $this->getSettings();
        $vars = $document->getAssocVars();
        $tempNomIdColVar = $vars[$settings['templateNomIdCol']]??null;
        if (!$tempNomIdColVar) {
            throw new Exception(sprintf(
                    self::$logTemplates['missingField'],
                    $settings['templateNomIdCol'],
                    self::$settings_map[self::SETTINGS_TYPE_REQUIRED]['templateNomIdCol'],
                    'document',
                    $document->get('id'),
                    $document->get('type')
                )
                ."\nThis field should contain an ID pointing to the template nomenclature for the drawing");
        }
        return $tempNomIdColVar['value'] ?? [];
    }

    /**
     * Generated an array of parameters, to be drawn on the image
     * parameter definitions are retreaved from the Shame, actual data is retrieved from the document and the number of the row
     *
     * @param Shape $shape
     * @param Document $document
     * @param $row
     * @return array
     * @throws Exception
     */
    private function getDataArrayForRow(Shape $shape, Document $document, $row): array
    {
        $settings = $this->getSettings();
        $params = $shape->getParams();
        $docVars = $document->getAssocVars();
        $data = [];
        foreach ($params as $p) {
            $id = (string)$p->getId();
            $varName = sprintf($settings['paramColNameTemplate'], $id);
            if (array_key_exists($varName, $docVars) && array_key_exists('value', $docVars[$varName])) {
                $data[$id] = $docVars[$varName]['value'][$row] ?? '';
            }
        }
        return $data;
    }

    /**
     * Returns the font path,
     * The value comes from the setting or is the default, but it's always correct.
     * If the 'fontPath' setting does not point to a file, the default path is returned
     *
     * @return string
     * @throws Exception
     */
    private function getFontPath(): string
    {
        $settings = $this->getSettings();
        if (array_key_exists('fontPath', $settings) && !empty($settings['fontPath'])) {
            // Use [LIBS] placeholder for the actual path of the _libs dir
            $val = str_replace('[LIBS]', PH_BASE_LIBS_DIR, $settings['fontPath']);

            return file_exists($val) ? $val : self::DEFAULT_FONTPATH;
        }
        return self::DEFAULT_FONTPATH;
    }

    /**
     * Returns an integer for the font size,
     * The value comes from the setting or is the default, but it's always correct
     *
     * @return int
     * @throws Exception
     */
    private function getFontSize(): int
    {
        $settings = $this->getSettings();
        $val = (int)($settings['fontSize'] ?? self::DEFAULT_FONTSIZE);
        return  $val ?: self::DEFAULT_FONTSIZE;
    }

    /**
     * Returns an array with 3 integer values, that specify R G B, components for the font color.
     * The values returned are either form the settings or the default values bur they are always correct.
     *
     * @return int[]
     * @throws Exception
     */
    private function getColor(): array
    {
        $settings = $this->getSettings();
        if (array_key_exists('fontColor', $settings)) {
            $rawVal = trim($settings['fontColor']);
            // The property is there but the value is empty
            if ($rawVal === '') {
                return self::DEFAULT_COLOR;
            }

            // Parse the value and convert all components to int
            $color = array_map(
                function ($el) {
                    return (int) $el;
                },
                explode(',', str_replace(' ', '', $rawVal)));
            $colorComponents = count($color);

            // Just right
            if ($colorComponents === 3) {
                return $color;
            }

            // Less than required: grayscale_mode - using one value for all components
            if ($colorComponents === 1) {
                return array_fill(0, 3, $color[0]);
            }

            // Something is not right here, but just add one more value to ensure the return value is correct.
            if ($colorComponents === 2) {
                return $color + $color[1];
            }

            // More than required  $colorComponents > 3
            return array_slice($color,0,3);
        }
        return self::DEFAULT_COLOR;
    }

    /**
     * Validates the compatibility of a nomenclature against the settings.
     * If any problem is detected throws an exception.
     *
     * @param Shape $shape
     * @return void
     * @throws Exception
     */
    private function shapeValidOrThrow(Shape $shape): void
    {
        $settings = $this->getSettings();
        $vars = $shape->getVars();
        $nom = $shape->getTemplate();

        if (empty($vars[$settings['templateNom_fileVarName']])) {
            throw new Exception(sprintf(
                    self::$logTemplates['missingField'],
                    $settings['templateNom_fileVarName'],
                    self::$settings_map[self::SETTINGS_TYPE_REQUIRED]['templateNom_fileVarName'],
                    'nomenclature',
                    $nom->get('id'),
                    $nom->get('type')
                )
                . "\nThis field should be of type 'fileupload' and it should contain a template image for the drawing");
        }

        if (empty($vars[$settings['templateNom_numVarName']])) {
            throw new Exception(sprintf(
                    self::$logTemplates['missingField'],
                    $settings['templateNom_numVarName'],
                    self::$settings_map[self::SETTINGS_TYPE_REQUIRED]['templateNom_numVarName'],
                    'nomenclature',
                    $nom->get('id'),
                    $nom->get('type')
                )
                . "\nThis field should be of type 'text' or 'dropdown' and it should be a group var, that contain the number (identification) of the parameter");
        }

        if (empty($vars[$settings['templateNom_xVarName']])) {
            throw new Exception(sprintf(
                    self::$logTemplates['missingField'],
                    $settings['templateNom_xVarName'],
                    self::$settings_map[self::SETTINGS_TYPE_REQUIRED]['templateNom_xVarName'],
                    'nomenclature',
                    $nom->get('id'),
                    $nom->get('type')
                )
                . "\nThis field should be of type 'text' and it should be a group var, that contain the X coordinate for a parameter");
        }

        if (empty($vars[$settings['templateNom_yVarName']])) {
            throw new Exception(sprintf(
                    self::$logTemplates['missingField'],
                    $settings['templateNom_yVarName'],
                    self::$settings_map[self::SETTINGS_TYPE_REQUIRED]['templateNom_yVarName'],
                    'nomenclature',
                    $nom->get('id'),
                    $nom->get('type')
                )
                . "\nThis field should be of type 'text' and it should be a group var, that contain the Y coordinate for a parameter");
        }
    }

    /**
     * Attach a temporary file to a document and move it to 'resources' directory
     *
     * @param $imageFilename
     * @param $tmpFileName
     * @param Document $document
     * @return ?int
     */
    private function attachFile($imageFilename, $tmpFileName, Document $document): ?int
    {
        $file_data = array(
            'name' => $imageFilename,
            'type' => mime_content_type($tmpFileName),
            'tmp_name' => $tmpFileName,
            'error' => 0,
            'size' => filesize($tmpFileName),
        );
        $file_params = array(
            'name' => $imageFilename,
            'description' => '',
            'revision' => '',
            'permission' => 'all',
        );
        $nzImageId = Files::attachFile($this->registry, $file_data, $file_params, $document);

        return $nzImageId ?: null;
    }

    /**
     * Returns a configured TempImageSaver instance
     * @return TempImageSaver
     * @throws Exception
     */
    private function getImageSaver(): TempImageSaver
    {
        $settings = $this->getSettings();

        return new TempImageSaver(
            $settings['outputFormat'] ?? self::DEFAULT_FORMAT,
            $settings['outputQuality'] ?? self::DEFAULT_QUALITY
        );
    }

    /**
     * Returns a configured DrawingRenderer instance
     *
     * @return DrawingRenderer
     * @throws Exception
     */
    private function getDrawingRenderer(): DrawingRenderer
    {
        if (is_null($this->renderer)) {
            $this->renderer = new DrawingRenderer($this->getFontPath(), $this->getFontSize(), $this->getColor());
            $this->renderer->setSaver($this->getImageSaver());
        }
        return $this->renderer;
    }

    /**
     * Returns an array with placeholders and their values from the document.
     * Only placeholders actually available in the outputFilenameTemplate are present
     *
     * @param Document $document
     * @return array
     * @throws Exception
     */
    private function buildFilenameTranslations(Document $document): array
    {
        if (!is_null($this->filenameTranslationMap)) {
            return $this->filenameTranslationMap;
        }

        $translations = [];
        if (preg_match_all('/\[([a-zA-Z-\d_]+)]/', $this->getFilenameTemplate(), $matches)) {
            $docVars = $document->getAssocVars();
            foreach ($matches[1] as $v) {
                if ($v === 'row') {
                    continue;
                }
                if (array_key_exists($v, $docVars)) {
                    $value = $docVars[$v]['value'];
                } elseif ($document->get($v)) {
                    $value = $document->get($v);
                } else {
                    $value = '';
                }

                $translations["[$v]"] = $value;
            }
        }

        return $this->filenameTranslationMap = $translations;
    }

    /**
     * Generates a concrete filename for the output file, by using a configured template or the default
     *
     * @param Document $document
     * @param string $row
     * @return string
     */
    private function generateFileName(Document $document, $row): string
    {
        $translations = $this->buildFilenameTranslations($document);
        $translations["[row]"] = (string)$row;

        $settings = $this->getSettings();
        $name = strtr($this->getFilenameTemplate(), $translations);
        $extention = $settings['outputFormat'] ?? self::DEFAULT_FORMAT;

        return "{$name}.{$extention}";
    }

    /**
     * Returns the template of a filename
     * @return mixed|string
     * @throws Exception
     */
    private function getFilenameTemplate()
    {
        if (!is_null($this->filenameTemplate)) {
            return $this->filenameTemplate;
        }
        $settings = $this->getSettings();
        return $this->filenameTemplate = $settings['outputFilenameTemplate'] ?? self::DEFAULT_FILENAME;
    }
}
