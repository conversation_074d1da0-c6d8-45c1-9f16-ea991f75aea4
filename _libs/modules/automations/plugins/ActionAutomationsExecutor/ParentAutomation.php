<?php

class ParentAutomation
{
    private int $id;
    private string $module;
    private string $controller;
    private string $automationType;
    private int $startModelType;
    private array $settings = [];
    private string $plugin;
    private string $method;
    private Model $model;
    private ?self $parentAutomation;

    private int $level = 1;
    private const MAX_LEVEL = 10;

    /**
     * Constructor for the parent automation class.
     *
     * @param array $params Array of parameters used to initialize the automation. Expected keys include 'id', 'module', 'controller', 'automation_type', 'start_model_type', 'plugin', 'method', and 'model'.
     * @param array $settings Array of settings specific to the automation configuration.
     * @param self|null $parentAutomation Optional parent automation object to define hierarchical nesting of automations.
     *
     * @return void
     *
     * @throws Exception If the maximum level of nested automations is exceeded.
     */
    public function __construct(array $params, array $settings, ?self $parentAutomation = null)
    {
        $this->id = $params['id'];
        $this->module = $params['module'];
        $this->controller = $params['controller'] ?? '';
        $this->automationType = $params['automation_type'];
        $this->startModelType = $params['start_model_type'];
        $this->settings = $settings;
        $this->plugin = $params['plugin'];
        $this->method = $params['method'];
        $this->model = $params['model'];

        if (!is_null($parentAutomation)) {
            $level = $parentAutomation->getLevel() + 1;
            if ($level > self::MAX_LEVEL) {
                throw new Exception('Max level of nested automations reached: ' . self::MAX_LEVEL);
            }
            $this->setParentAutomation($parentAutomation);
            $this->setLevel($level);
        }
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function getController(): string
    {
        return $this->controller;
    }

    public function getAutomationType(): string
    {
        return $this->automationType;
    }

    public function getStartModelType(): int
    {
        return $this->startModelType;
    }

    public function getSettings(): array
    {
        return $this->settings;
    }

    public function getPlugin(): string
    {
        return $this->plugin;
    }

    public function getMethod(): string
    {
        return $this->method;
    }

    public function getModel(): Model
    {
        return $this->model;
    }

    private function setParentAutomation(self $parentAutomation): void
    {
        $this->parentAutomation = $parentAutomation;
    }

    public function getParentAutomation(): ?self
    {
        return $this->parentAutomation;
    }

    public function getRootAutomation(): self
    {
        return $this->parentAutomation ? $this->parentAutomation->getRootAutomation() : $this;
    }

    private function setLevel(int $level): void
    {
        $this->level = $level;
    }

    public function getLevel(): int
    {
        return $this->level;
    }
}
