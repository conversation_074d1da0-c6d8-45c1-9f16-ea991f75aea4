<?php

require_once 'tags.validator.php';

/**
 * Tags model class
 */
Class Tag extends Model {
    public $modelName = 'Tag';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //get model types using tag - used only in list/search
        if ($this->get('id') && $registry->get('getModelTypes')) {
            $this->getCurrentModelTypes();
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_TAGS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new tag base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //UPDATE THE TAGS_TYPES TABLE OF THE MODEL
        $this->updateModelTypes();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_TAGS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('edit tag base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //UPDATE THE TAGS_TYPES TABLE OF THE MODEL
        $this->updateModelTypes();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }


    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('color')) {
            $set['color'] = sprintf("color='%s'", $this->get('color'));
        }
        if ($this->isDefined('place')) {
            $set['place'] = sprintf("place=%d", $this->get('place'));
        }
        if ($this->isDefined('section')) {
            $set['section'] = sprintf("section=%d", $this->get('section'));
        }
        if ($this->isDefined('model')) {
            $set['model'] = sprintf("model='%s'", $this->get('model'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name'] = sprintf("name='%s'", $this->get('name'));
        $update['description']  = sprintf("description='%s'", $this->get('description'));

        $insert = $update;
        $insert['parent_id'] = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']      = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        $query2 = 'INSERT INTO ' . DB_TABLE_TAGS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);
        $db->Execute($query2);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('save tag i18n details', $db, $query2);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Gets current model type ids from database and sets them to model
     *
     * @return bool - result of the operation
     */
    public function getModelTypes() {
        $db = $this->registry['db'];

        $query = 'SELECT type_id FROM ' . DB_TABLE_TAGS_TYPES . "\n" .
                 'WHERE tag_id=' . $this->get('id');
        $model_types = $db->GetCol($query);

        return $this->set('model_types', $model_types, true);
    }

    /**
     * Gets all available model types for current model as option values
     *
     * @return array|null - available model types as option values, null when
     *      module does not have types at all
     */
    public function getAvailableModelTypes() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $params = array(
            0 => $this->registry,
            'value' => 'id',
            'label' => 'name',
            'where' => 't.active',
            'order_by' => 'name ASC'
        );

        $model = $this->get('model');
        switch ($model) {
            case 'finance_annulments':
            case 'finance_expenses_reasons':
            case 'finance_incomes_reasons':
            case 'finance_warehouses_documents':
                $params['table'] = 'DB_TABLE_FINANCE_DOCUMENTS_TYPES';
                $params['table_i18n'] = 'DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N';
                $params['where'] .= ' AND t.model = \'' . General::plural2singular($model) . '\'';
                break;
            case 'contracts':
            case 'customers':
            case 'documents':
            case 'finance_payments':
            case 'nomenclatures':
            case 'projects':
            case 'tasks':
            default:
                $params['table'] = 'DB_TABLE_' . strtoupper($model) . '_TYPES';
                $params['table_i18n'] = $params['table'] . '_I18N';
                break;
        }

        // no types
        if (!defined($params['table']) || !defined($params['table_i18n'])) {
            if ($sanitize_after) {
                $this->sanitize();
            }
            return null;
        }

        $available_model_types = Dropdown::getCustomDropdown($params);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $available_model_types;
    }

    /**
     * Prepares current model types as option values and sets them to model
     *
     * @return mixed - result of the operation
     */
    public function getCurrentModelTypes() {

        $available_model_types = $this->getAvailableModelTypes() ?: array();

        $this->getModelTypes();
        $model_types = $this->get('model_types');

        $current_model_types =
            array_filter($available_model_types,
                         function ($a) use ($model_types) {
                             return in_array($a['option_value'], $model_types);
                         });

        return $this->set('current_model_types', $current_model_types, true);
    }

    /**
     * Save data for model types that current tag is applicable for
     *
     * @return bool - result of the operation
     */
    public function updateModelTypes() {
        if (!$this->isDefined('model_types')) {
            //no model types selected
            return true;
        }

        $db = $this->registry['db'];

        // first delete all records
        $query1 = 'DELETE FROM ' . DB_TABLE_TAGS_TYPES . "\n" .
                  'WHERE tag_id = \'' . $this->get('id') . '\'';
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $this->registry['logger']->dbError('save tag model types', $db, $query1);
        }

        $model_types = $this->get('model_types');

        // insert all model types for current tag id
        if ($model_types) {
            $query2 = array();
            foreach ($model_types as $model_type) {
                $query2[] = sprintf("(%d, '%s')", $this->get('id'), $model_type);
            }
            $query2 = 'INSERT INTO ' . DB_TABLE_TAGS_TYPES . ' (tag_id, type_id) VALUES' . "\n" . implode(",\n", $query2);
            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('save tag model types', $db, $query2);
            }
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Method checks if tag is already used in models
     *
     * @return boolean - true if tag is attached to models, otherwise false
     */
    public function isUsed() {
        if (!$this->get('id')) {
            return false;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $query =
            'SELECT' . "\n" .
            '(SELECT COUNT(tag_id) FROM ' . DB_TABLE_TAGS_MODELS . ' WHERE tag_id=' . $this->get('id') . ') + ' . "\n" .
            '(SELECT COUNT(tag_id) FROM ' . DB_TABLE_ARCHIVE_TAGS_MODELS . ' WHERE tag_id=' . $this->get('id') . ')';
        $has_models = $this->registry['db']->GetOne($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $has_models > 0;
    }
}

?>
