<?php
Class Reports_Export_Viewer extends Viewer {
    public $template = 'index.html';
    public $export_filename = '';
    public $export_template_path = '';
    public $export_generated_files = array();

    public function prepare() {
        $filters = Reports::saveSearchParams($this->registry, $this->registry->get('filters_values'), 'reports_' . (isset($this->registry['report_type']['name']) ? $this->registry['report_type']['name'] : '') . '_');

        if ($this->action == 'export' && $this->registry['report_type']['name']) {
            if (!$this->export_template_path) {
                $this->export_template_path = PH_MODULES_DIR . 'reports/plugins/' . $this->registry['report_type']['name'] . '/export_report.html';
            }

            // gets model of the report
            $reports = Reports::getReports($this->registry, array('name' => $this->registry['report_type']['name']));
            $report = $reports[0];

            // attach an extender
            $report->extender = new Extender();

            $reports_results = Reports::search($this->registry, $filters);

            // load lang files
            $this->loadReportLangFiles($report->get('type'));

            // Load report translations from DB
            $this->loadI18nFromDb($this->module, [$report->get('type')]);

            //report's filters
            $current_filters = $this->registry->get('report_filters');
            foreach ($current_filters as $key => $value) {
                if (array_key_exists($key, $filters)) {
                    $current_filters[$key]['value'] = $filters[$key];
                }
            }

            $this->registry->set('report_filters', $current_filters, true);

            if ($this->registry->isRegistered('report_filters')) {
                $reports_settings = array();

                //separate the reports settings from the other filters
                $final_filters = $this->registry->get('report_filters');
                $already_used_filters = array();
                foreach ($final_filters as $key => $value) {
                    if (isset($value['setting']) && $value['setting']) {
                        if (! in_array($key, $already_used_filters)) {
                            if (isset($final_filters[$key . '_options'])) {
                                $value['additional_options'] = $final_filters[$key . '_options'];
                                $already_used_filters[] = $key . '_options';
                                unset($final_filters[$key . '_options']);
                            }
                            $reports_settings[] = $value;
                            unset($final_filters[$key]);
                        }
                    }
                }

                $this->data['report_filters'] = $final_filters;
                $this->data['reports_settings'] = $reports_settings;
            }

            $additional_report_options = false;
            if (isset($reports_results['additional_options'])) {
                $additional_report_options = $reports_results['additional_options'];
                $this->data['reports_additional_options'] = $additional_report_options;
                unset ($reports_results['additional_options']);
            }
            $this->data['reports_results'] = $reports_results;

            //prepare the file name to export
            $this->export_filename = $this->composeExcelExportFileName($filters['report_type']);

            // take export pattern
            $export_pattern_id = $this->registry->get('export_pattern');
            $pattern = '';

            if ($export_pattern_id) {
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                require_once PH_MODULES_DIR . 'patterns/models/patterns.parts.factory.php';
                $patterns_filters = array('where'        => array('p.model = "Report"',
                                                                  'p.model_type = "' . $report->get('id') . '"',
                                                                  'p.id = "' . $export_pattern_id . '"',
                                                                  'p.active = 1'),
                                          'sort'         => array('p.position ASC', 'p.id ASC'),
                                          'model_lang'   => $this->registry['lang'],
                                          'sanitize'     => true);
                $pattern = Patterns::searchOne($this->registry, $patterns_filters);
            }

            $report->set('report_results', $reports_results, true);
            $report->set('reports_additional_options', $additional_report_options, true);

            $this->data['report_type'] = $filters['report_type'];
            $this->data['templatesDirPlugin'] = PH_MODULES_DIR . "reports/plugins/";

            if ($pattern) {
                // prepare placeholders
                if ($pattern->get('format') == 'pdf') {
                    $this->generatePDF($report, $pattern);
                } else {
                    $this->generateXLS($report, $pattern);
                }
            } else {
                $this->generateXLS($report, false);
            }

            //  if any files were created during export
            // the files are deleted
            $this->clearGenereatedFiles();
            exit;
        } else {
            list($reports_results, $pagination) = Reports::pagedSearch($this->registry, $filters);
        }
    }

    /*
     * Generating an xls file
     */
    public function generateXLS($report, $pattern) {
        //assign export template
        if (!$pattern) {
            $this->setTemplate($this->export_template_path);
            $this->setFrameset('frameset_blank.html');

            //fetch content
            $content = $this->fetch($this->export_template_path);
        } else {
            $this->prepareReportCustomPlaceholders($report, $export_format='xls');
            $content = $pattern->get('content');
            $content = $report->extender->expand($content);
        }

        //send it to the user
        header('Content-Disposition: attachment; filename="' . $this->export_filename . '"');
        header("Content-type: application/x-msexcel; charset=utf-8");
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        print $content;
    }

    /*
     * Generating a pdf file
     */
    function generatePDF($report, $pattern) {
        $this->prepareReportCustomPlaceholders($report, $export_format='pdf');
        $basic_placeholders = $report->getPatternsVars();

        //get the header
        $header_content = '';
        $header_height = '';
        if ($pattern->get('header')) {
            $filters = array('where'        => array('pp.id = ' . $pattern->get('header')),
                             'sanitize'     => true,
                             'model_lang'   => $this->registry['lang']);
            $header = Patterns_Parts::searchOne($this->registry, $filters);
            $header_content = $header->get('content');
            $header_height = $header->get('height');
            $header_content = $report->extender->expand($header_content);

            //check for additional variables
            if (preg_match('#\[([a-zA-Z0-9-_])+\]#', $header_content)) {
                $header_content = $report->extender->expand($header_content);
            }
            $header_content = str_replace('&nbsp;', '&#160;', $header_content);
            $header_preview = $header_content;
        }

        //get the footer
        $footer_content = '';
        $footer_height = '';
        if ($pattern->get('footer')) {
            $filters = array('where'        => array('pp.id = ' . $pattern->get('footer')),
                             'sanitize'     => true,
                             'model_lang'   => $this->registry['lang']);
            $footer = Patterns_Parts::searchOne($this->registry, $filters);
            $footer_height = $footer->get('height');
            $footer_content = $footer->get('content');
            $footer_content = $report->extender->expand($footer_content);

            //check for additional variables
            if (preg_match('#\[([a-zA-Z0-9-_])+\]#', $footer_content)) {
                $footer_content = $report->extender->expand($footer_content);
            }
            $footer_content = str_replace('&nbsp;', '&#160;', $footer_content);
            $footer_preview = $footer_content;
        }

        $content = $pattern->get('content');
        $content = $report->extender->expand($content);
        //check for additional variables
        if (preg_match('#\[([a-zA-Z0-9\_\|\%\.\:\-])+\]#', $content)) {
            $content = $report->extender->expand($content);
        }

        //prepare the CSS (get the css content from a css template)
        //strip it from the content and put it in the head section
        list($content, $css_styles) = General::stripCSS($content);
        $css = '<style type="text/css">' . "\n" .
               file_get_contents($this->registry['theme']->stylesDir . '_print.csst') . "\n" .
               (is_array($css_styles) ? implode("\n", $css_styles) : '') .
               '</style>' . "\n";

        //Convert content to pdf
        $params = array(
            'encoding'      => 'utf-8',
            'header_html'   => $css . $header_content,
            'header_height' => $header_height,
            'footer_html'   => $css . $footer_content,
            'footer_height' => $footer_height,
            'landscape'     => $pattern->get('landscape'),
            'output'        => 0
        );

        $base_filename = $report->composeOutputFileName($pattern->get('prefix'));
        $result = html2pdf::process($base_filename, '', $css . $content, $params);
    }

    /*
     * Function to prepare the report's custom placeholders
     */
    function prepareReportCustomPlaceholders(&$report, $export_format='pdf') {
        // get all the default vars
        $default_vars = $report->getPatternsVars();

        // assign the default vars to the extender
        foreach ($default_vars as $var_name => $var_value) {
            $report->extender->add($var_name, $var_value, true);
        }

        $reports_results = $report->get('report_results');
        $additional_report_options = $report->get('reports_additional_options');

        if (isset($additional_report_options['placeholders'])) {
            foreach ($additional_report_options['placeholders'] as $placeholder) {
                // If the report placeholder type is: chart
                if (!empty($placeholder['type']) && $placeholder['type'] == 'chart') {
                    if ($export_format=='pdf') {
                        $placeholder_content = '<img src="' . $placeholder['properties']['url'] . '" border="0">';
                    }
                    $this->export_generated_files[] = $placeholder['properties']['dir'];
                } elseif (!empty($placeholder['type']) && $placeholder['type'] == 'value') {
                    // If the report placeholder type is: value
                    //   then prepare a placeholder related to this value
                    $placeholder_content = nl2br($placeholder['properties']['value']);
                } else {
                    // Default report placeholder type is: table
                    $table_viewer = new Reports_Export_Viewer($this->registry);
                    $table_viewer->setFrameset('frameset_blank.html');
                    $table_viewer->setTemplate($this->export_template_path);
                    $table_viewer->loadReportLangFiles($report->get('type'));

                    $table_viewer->data['reports_additional_options'] = $additional_report_options;
                    $table_viewer->data['reports_results']            = $reports_results;
                    $table_viewer->data['prepare_placeholder']        = $placeholder['name'];

                    // Get each table as a placeholder
                    $placeholder_content = $table_viewer->fetch();
                }
                // Add a placeholder
                $report->extender->add($placeholder['name'], $placeholder_content, true);
            }
        }
        return true;
    }

    /*
     * Function to load custom report lang files
     */
    public function loadReportLangFiles($report_name) {
        $i18n_files = array();

        //added lang files for the selected report if file exists
        $lang_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report_name,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        if (file_exists($lang_file)) {
            $i18n_files[] = $lang_file;
        }

        //load lang files
        $i18n_files[] = sprintf('%s%s%s/%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $this->registry['lang'],
            'documents.ini');
        if ($this->registry->isRegistered('custom_report_i18n_files')) {
            $custom_report_i18n_files = $this->registry->get('custom_report_i18n_files');
            if (!is_array($custom_report_i18n_files)) {
                $custom_report_i18n_files = array($custom_report_i18n_files);
            }
            $i18n_files = array_merge($i18n_files, $custom_report_i18n_files);
        }
        $this->loadCustomI18NFiles($i18n_files);
    }

    /*
     * Method to load the needed template no matter of the templatesDir set in the model
     */
    public function setTemplate($template = '') {
        if ($template) {
            $this->template = $template;
        }

        $this->renderer->assign('template', $this->template);

        return $this->template;
    }

    /*
     * Method to clear the files genereted during the export
     */
    public function clearGenereatedFiles() {
        $export_generated_files = $this->export_generated_files;
        if (!empty($export_generated_files)) {
            foreach ($export_generated_files as $egf) {
                unlink($egf);
            }
        }

        return true;
    }

    /**
     * Compose transliterated file name for the Excel export file
     *
     * @param $report_type - the report type
     *
     * @return string
     */
    public function composeExcelExportFileName($report_type) {
        // If the export file name is set by the report setting: export_filename
        if (defined('EXPORT_FILENAME')) {
            // Use the setting as a filename
            $filename = EXPORT_FILENAME;

            // If the filename from the setting contains the placeholder: <type>
            if (preg_match('#<type>#', $filename)) {
                // Then use the report type for the name
                $filename = $report_type. '.xls';
            }
        } else {
            /*
             * Use the i18n name of the report
             */
            // Get the i18n name
            $query = "
                SELECT ri.name
                  FROM " . DB_TABLE_REPORTS . " AS r
                  LEFT JOIN " . DB_TABLE_REPORTS_I18N . " AS ri
                    ON (ri.parent_id = r.id
                      AND ri.lang = '{$this->registry->get('lang')}')
                  WHERE r.type = '{$report_type}'";
            $filename = $this->registry['db']->GetOne($query);

            // Replace any bad characters from the filename
            $filename = preg_replace('#[\(\)\&]#', '', trim($filename));

            // Replace spaces with _ and add the XLS extension
            $filename = preg_replace('#\s+#', '_', $filename) . '.xls';
        }

        // Return the file name transliterated to latin
        return Transliterate::convert($filename);
    }
}
