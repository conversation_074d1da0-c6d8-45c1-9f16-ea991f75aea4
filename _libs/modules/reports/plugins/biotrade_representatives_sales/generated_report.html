{if is_array($reports_results) && $reports_results|@count}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 800px;">
    <tr class="reports_title_row hcenter">
      <td class="t_border" width="14">&nbsp;</td>
      <td class="t_border" width="80">{#reports_date#|escape}</td>
      <td class="t_border" width="280">{#reports_pharmacy_doctor#|escape}</td>
      <td class="t_border" width="80">{#reports_type#|escape}</td>
      <td class="t_border" width="280">{#reports_article#|escape}</td>
      <td class="" width="80">{#reports_quantity#|escape}</td>
    </tr>
    {foreach from=$reports_results key='rk' name='ri' item='res'}
      {if $res.rowspan}{cycle values='t_odd1 t_odd2,t_even1 t_even2' assign='c'}{/if}
      <tr class="{$c}">
        {if $res.rowspan}
        <td class="t_border hcenter vmiddle" rowspan="{$res.rowspan}" title="{if $res.status eq 'finished'}{#reports_finished#|escape}{else}{#reports_unfinished#|escape}{/if}">
          <div class="documents_status {if $res.status eq 'finished'}closed{else}{$res.status}{/if}" style="padding-left: 0px;"></div>
        </td>
        {/if}
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;{$controller_param}=incomes_reasons&amp;incomes_reasons=view&amp;view={$res.id}" target="_blank">{$res.issue_date|date_format:#date_short#|default:"-"}</a>
        </td>
        <td class="t_border">
          <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$res.customer}" target="_blank">{$res.customer_name|escape|default:"&nbsp;"}</a>
        </td>
        <td class="t_border">{$res.article_type|default:"&nbsp;"}</td>
        <td class="t_border">{$res.article_name|default:"&nbsp;"}</td>
        <td class="hright">{$res.quantity|default:0}</td>
      </tr>
    {/foreach}
    <tr>
      <td class="t_footer" colspan="6"></td>
    </tr>
  </table>
  <br />
{else}
  <h1 style="padding: 5px;">{#error_reports_no_results_to_show#|escape}</h1>
{/if}
