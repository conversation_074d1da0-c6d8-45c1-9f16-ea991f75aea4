<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            // DEFINE STARTING DATE OF A DOCUMENT FILTER
            $filter = array (
                'custom_id' => 'date_from',
                'name' => 'date_from',
                'type' => 'date',
                'label' => $this->i18n('from'),
                'help' => $this->i18n('from')
            );
            $filters['date_from'] = $filter;


            // DEFINE FINAL DATE OF A DOCUMENT FILTER
            $filter = array (
                'custom_id' => 'date_to',
                'name' => 'date_to',
                'type' => 'date',
                'label' => $this->i18n('to'),
                'help' => $this->i18n('to')
            );
            $filters['date_to'] = $filter;


            // ALL THE DOCUMENTS ADDED BY A CERTAIN USER
            //prepare options for added_by_all
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters_users = array('model_lang' => $registry['lang'], 
                                   'sanitize' => true);
            $users = Users::search($registry, $filters_users);

            $_options_users = array();
            foreach($users as $user) {
                $_options_users[] = array(
                    'label' => $user->get('firstname') . ' ' . $user->get('lastname'), 'option_value' => $user->get('id'));
            }

            $filter = array (
                'custom_id' => 'added_by',
                'name' => 'added_by',
                'type' => 'dropdown',
                'first_option_label' => $this->i18n('all'),
                'label' => $this->i18n('added_by'),
                'help' => $this->i18n('added_by'),
                'options' => $_options_users,
            );
            $filters['added_by'] = $filter;

            return $filters;
        }
    }
?>