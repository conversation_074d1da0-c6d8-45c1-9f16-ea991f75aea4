<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'export':
                $this->_index();
                break;
            case 'insertids':
                $this->_insertIds();
                break;
            case 'dashlet':
                $this->_dashlet();
                break;
            case 'send_as_mail':
                $this->_sendAsMail();
                break;
            case 'create_model':
            case 'clone_selected':
                $this->_createModel();
                break;
            case 'ajax_email_content':
                $this->_email_content();
                break;
            case 'payments_protocol':
                $this->_paymentsProtocol();
                break;
            case 'ajax_select_customers':
                $this->_selectCustomer();
                break;
            case 'generate_report':
                $this->setAction('generate_report');
                $this->_generate_report();
            case 'index':
            default:
                $this->setAction('index');
                $this->_index();
        }
    }

    /**
     * AJAX function to show a lightbox for selecting a customer for the new document
     */
    public function _selectCustomer() {
        //get current report
        $report = $this->getReportType();
        $report = $report['name'];
        $report = Reports::getReports($this->registry, array('name' => $report));
        $report = $report[0];
        Reports::getReportSettings($this->registry, $report->get('type'));

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report->get('type'),
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->registry['translater']->loadFile($i18n_file);

        $result = array(
            'content' => '',
            'title'   => ''
        );

        // Prepare the customers dropdown
        $request             = $this->registry['request'];
        $available_customers = array();
        foreach ($request->get('included_rows') as $included_row) {
            $decoded_row = json_decode(base64_decode($included_row), true);
            if (!array_key_exists($decoded_row['article_deliverer'], $available_customers)) {
                $available_customers[$decoded_row['article_deliverer']] = array(
                    'option_label' => $decoded_row['article_deliverer_name'],
                    'value'        => $decoded_row['article_deliverer']
                );
            }
        }

        $viewer                              = new Viewer($this->registry);
        $viewer->data['available_customers'] = $available_customers;
        $viewer->data['client_label']        = $this->i18n('reports_customer');
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $report->get('type') . '/';
        $viewer->template     = '_select_customer.html';
        $result['content']    = $viewer->fetch();
        $result['title']      = $this->i18n('reports_select_customer_for_payments_protocol');

        print json_encode($result);
        exit;
    }

    /**
     * Function to create payments protocol document for selected rows
     */
    public function _paymentsProtocol() {
        $registry = &$this->registry;

        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];

        //  load module i18n file
        //    (we need this, because for example: into the history of the model, the status will not get it's translation)
        $i18n_file = sprintf('%s%s%s%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $registry['lang'],
            '/documents.ini');
        $registry['translater']->loadFile($i18n_file);

        // load plugin i18n file
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $registry['lang'],
            '/reports.ini');
        $registry['translater']->loadFile($i18n_file);

        Reports::getReportSettings($registry, $report);

        // get request and all post params
        $request = $registry['request'];

        $selected_rows = $request->get('included_rows');

        $decoded_rows  = array();
        $customer_id   = $request->get('customer_for_new_document');

        // parse the encoded data
        foreach ($selected_rows as $new_row) {
            $decoded_row = json_decode(base64_decode($new_row), true);
            $decoded_rows[] = $decoded_row;
            unset($decoded_row);
        }

        // get default name for this type
        $query = 'SELECT `name`' . "\n" .
                 '  FROM `' . DB_TABLE_DOCUMENTS_TYPES_I18N . '`' . "\n" .
                 '  WHERE `parent_id` = \'' . DOCUMENT_PAYMENTS_PROTOCOL . '\'';
        $document_name = $registry['db']->GetOne($query);

        // PREPARE THE DATA FOR THE NEW DOCUMENT
        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        $document     = new Document($registry);
        $document->set('type', DOCUMENT_PAYMENTS_PROTOCOL, true);
        $document->getVars();
        $old_document = clone $document;
        $old_document->sanitize();

        $document->set('type_name', $document_name, true);
        $document->set('name', $document_name, true);
        $document->set('date', date('Y-m-d'), true);
        $document->set('employee', $registry['currentUser']->get('employee'), true);
        $document->set('active', 1, true);
        $document->set('customer', $customer_id, true);

        $assoc_vars = $document->getAssocVars();
        $document->unsetProperty('assoc_vars', true);

        // prepare recording of the additional data
        $additional_vars_list = array(
            'article_deliverer'     => FIELD_ARTICLE_DELIVERER_ID,
            'article_deliverer_name'=> FIELD_ARTICLE_DELIVERER_NAME,
            'project'               => FIELD_PROJECT_ID,
            'project_name'          => FIELD_PROJECT_NAME,
            'article_id'            => FIELD_ARTICLE_ID,
            'article_name'          => FIELD_ARTICLE_NAME,
            'article_description'   => FIELD_ARTICLE_DESCRIPTION,
            'article_measure_name'  => FIELD_ARTICLE_MEASURE_NAME,
            'price'                 => FIELD_PRICE,
            'quantity'              => FIELD_QUANTITY,
            'subtotal'              => FIELD_SUBTOTAL,
            USED_PAYMENTS_FIELD     => USED_PAYMENTS_FIELD
        );
        foreach ($decoded_rows as $key => $row_data) {
            foreach($row_data as $var => $val) {
                if (array_key_exists($var, $additional_vars_list)) {
                    $assoc_vars[$additional_vars_list[$var]]['value'][] = $val;
                }
            }
        }
        $document->set('vars', array_values($assoc_vars), true);

        // try to save the document
        if ($document->save()) {
            $filters = array('where'      => array('d.id = ' . $document->get('id')),
                             'model_lang' => $document->get('model_lang'));
            $new_document = Documents::searchOne($registry, $filters);
            $registry->set('get_old_vars', true, true);
            $new_document->getVars();
            $registry->set('get_old_vars', false, true);

            // write history
            Documents_History::saveData($registry, array('model' => $new_document,
                                                         'action_type' => 'add',
                                                         'new_model' => $new_document,
                                                         'old_model' => $old_document));

            // close the tab with report after the process is finished
            $redirectUrl = sprintf('%s?%s=%s&%s=%s&%s=%d', $_SERVER['PHP_SELF'], $registry->get('module_param'), 'documents', 'documents', 'edit', 'edit', $new_document->get('id'));

            header('Location: ' . $redirectUrl);
        }
        exit;
    }
}

?>
