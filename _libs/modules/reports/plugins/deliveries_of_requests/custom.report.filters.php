<?php
class Custom_Report_Filters extends Report_Filters {
    private static $registry;

    /**
     * Defining filters for the certain type report
     */
    function defineFilters(&$registry) {
        self::$registry = &$registry;

        // Prepare an array for the filters
        $filters = array();

        $settings = Reports::getReportSettings($registry);

        $this->loadDefaultFilter($registry, $filters, 'autocompleter', array('autocomplete_type' => 'customers', 'filter_name' => 'deliverer'));
        $filters['deliverer']['label'] = $this->i18n('reports_filter_deliverer');
        $filters['deliverer']['autocomplete']['filters'] = array('type' => $settings['deliverer_types']);

        $filters['for_date_from'] = array (
            'name'              => 'for_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'for_date_to',
            'label'             => $this->i18n('reports_filter_for_date')
        );
        $filters['for_date_to'] = array (
            'name' => 'for_date_to'
        );

        $this->loadDefaultFilter($registry, $filters, 'period_from_to');
        $filters['period_from']['label'] = $this->i18n('reports_filter_period_from');

        $filters['delivery_date_from'] = array (
            'name'              => 'delivery_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'delivery_date_to',
            'label'             => $this->i18n('reports_filter_delivery_date')
        );
        $filters['delivery_date_to'] = array (
            'name' => 'delivery_date_to'
        );

        $filters['good'] = array (
            'name'            => 'good',
            'type'            => 'autocompleter',
            'label'           => $this->i18n('reports_filter_good'),
            'autocomplete'    => array(
                'search' => array('<code>', '<name>'),
                'type'         => 'nomenclatures',
                'clear'        => 1,
                'suggestions'  => '<name>',
                'fill_options' => array(
                    '$good => <id>',
                    '$good_autocomplete => <name>'
                ),
                'filters'      => array(
                    '<type>' => $settings['good_types']
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures', 'ajax_select')
            )
        );

        $filters['order'] = array (
            'name'            => 'order',
            'type'            => 'autocompleter',
            'label'           => $this->i18n('reports_filter_order'),
            'autocomplete'    => array(
                'type'         => 'documents',
                'clear'        => 1,
                'suggestions'  => '<full_num>',
                'fill_options' => array(
                    '$order => <id>',
                    '$order_autocomplete => <full_num>'
                ),
                'filters'      => array(
                    '<type>' => $settings['order_types']
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents', 'ajax_select')
            )
        );

        $filters['request'] = array (
            'name'            => 'request',
            'type'            => 'autocompleter',
            'label'           => $this->i18n('reports_filter_request'),
            'autocomplete'    => array(
                'type'         => 'documents',
                'clear'        => 1,
                'suggestions'  => '<full_num>',
                'fill_options' => array(
                    '$request => <id>',
                    '$request_autocomplete => <full_num>'
                ),
                'filters'      => array(
                    '<type>' => $settings['doc_type_delivery_request']
                ),
                'url'          => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents', 'ajax_select')
            )
        );

        $filters['reclamation'] = array (
            'name'    => 'reclamation',
            'label'   => $this->i18n('reports_filter_reclamation'),
            'type'    => 'dropdown',
            'options' => self::getFieldOptions($registry, 'free_field5')
        );

        $filters['order_status'] = array(
            'name'    => 'order_status',
            'label'   => $this->i18n('reports_filter_order_status'),
            'type'    => 'checkbox_group',
            'options' => $this->getFieldOptions($registry, 'free_field1')
        );

        return $filters;
    }

    function processDependentFilters(&$filters) {
        $registry = &self::$registry;

        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        if (empty($filters['order_status']['value']) && !$registry->get('generated_report')) {
            $filters['order_status']['value'] = array(1, 2);
        }

        return $filters;
    }
}
