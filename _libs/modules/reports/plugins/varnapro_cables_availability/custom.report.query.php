<?php
    Class Varnapro_Cables_Availability Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $records = array();

            if (!empty($filters['customer']) && !empty($filters['project'])) {

                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type="' . CABLE_SCHEME .  '" AND (fm.name="' . CABLE_VAR . '" OR fm.name="' . CABLE_QUANTITY . '") ORDER BY fm.position';
                $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                $cable_var_id = '';
                $cable_quantity_id = '';

                foreach ($var_ids as $vars) {
                    if ($vars['name'] == CABLE_VAR) {
                        $cable_var_id = $vars['id'];
                    } else if ($vars['name'] == CABLE_QUANTITY) {
                        $cable_quantity_id = $vars['id'];
                    }
                }

                $sql = array();

                $sql['select'] = 'SELECT nom.id as id_idx, nom.id as nom_id, nom.code, nomi18n.name as name, SUM(d_cstm_quantity.value) as documents_quantity, 0 as werehouse_quantity, 0 as needed_quantity, 0 as colored' . "\n";

                $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'INNER JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                 ' ON (p.id=d.project AND p.parent_project="' . $filters['project'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_nomenclature' . "\n" .
                                 '  ON (d.id=d_cstm_nomenclature.model_id AND d_cstm_nomenclature.var_id="' . $cable_var_id . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_quantity' . "\n" .
                                 '  ON (d.id=d_cstm_quantity.model_id AND d_cstm_quantity.var_id="' . $cable_quantity_id . '" AND d_cstm_nomenclature.num=d_cstm_quantity.num)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                 '  ON (d_cstm_nomenclature.value=nom.id)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                 '  ON (nom.id=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n";

                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.active=1';
                $where[] = 'd.type="' . CABLE_SCHEME . '"';
                $where[] = 'd.customer="' . $filters['customer'] . '"';

                $sql['where'] = 'WHERE ' . implode(' AND ', $where);
                $sql['group'] = 'GROUP BY nom.id';
                $sql['order'] = 'ORDER BY nom.code ASC';

                $query = implode("\n", $sql);
                $records = $registry['db']->GetAssoc($query);

                // take werehouse quantities
                $nom_ids = array_keys($records);

                if (!empty($nom_ids)) {
                    $sql_for_werehouse = 'SELECT fwq.nomenclature_id as article_id, SUM(fwq.quantity) as quantity' . "\n" .
                                         'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' AS fwq' . "\n" .
                                         'WHERE fwq.nomenclature_id IN (' . implode(',', $nom_ids) . ')';

                    $records_werehouses = $registry['db']->GetAssoc($sql_for_werehouse);

                    foreach ($records_werehouses as $article_key => $wq) {
                        if (isset($records[$article_key])) {
                            $records[$article_key]['werehouse_quantity'] = $wq['quantity'];
                        }
                    }
                }

                foreach ($records as $key_rec => $record_info) {
                    $difference_quantity = $record_info['werehouse_quantity'] - $record_info['documents_quantity'];
                    if ($difference_quantity<0) {
                        $records[$key_rec]['needed_quantity'] = abs($difference_quantity);
                        $records[$key_rec]['colored'] = 1;
                    }
                }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }

            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                $results = $records;
            }

            return $results;
        }
    }
?>