<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1">
      <tr>
        <td rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap">&nbsp;</td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap"><strong>{#reports_employee#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle; width: 150px;"><strong>{#reports_company_period_to_work#|escape}</strong></td>
        {if $reports_results.include_prev_years ge 1}
          <td colspan="{$reports_results.include_prev_years}" style="text-align: center; vertical-align: middle;"><strong>{#reports_left_days_from_previous_year#|escape}</strong></td>
        {/if}
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_left_days_from_current_year#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;" nowrap="nowrap"><strong>{#reports_requested_days#|escape}</strong></td>
        {if $reports_results.include_available_days_to_date}
          <td rowspan="2" style="text-align: center; vertical-align: middle; width: 120px;"><strong>{$reports_results.include_available_days_to_date_lbl|escape}</strong></td>
        {/if}
      </tr>
      <tr class="reports_title_row">
        {foreach from=$reports_results.labels item=lbl_year}
          <td style="text-align: center; vertical-align: middle; width: 70px;" nowrap="nowrap"><strong>{$lbl_year|escape}</strong></td>
        {/foreach}
      </tr>
      {foreach from=$reports_results.employees item=result name=results}
        <tr>
          <td nowrap="nowrap" style="vertical-align: middle; text-align: right;" width="25">
            {counter name='item_counter' print=true}
          </td>
          <td nowrap="nowrap" style="vertical-align: middle;">
            {$result.name|escape|default:"&nbsp;"}
          </td>
          <td>
            {$result.working_periods|escape|nl2br|default:"&nbsp;"}
          </td>
          {foreach from=$result.left_days_by_year item=ldby}
            <td style="vertical-align: middle; text-align: right;">
              {$ldby|escape|default:"0"}
            </td>
          {/foreach}
          <td style="vertical-align: middle; text-align: right;">
            {$result.requested_days|escape|default:"0"}
          </td>
          {if $reports_results.include_available_days_to_date}
            <td style="vertical-align: middle; text-align: right;">{$result.available_to_date|escape|default:"0"}</td>
          {/if}
        </tr>
      {foreachelse}
        <tr>
          <td colspan="{$reports_results.total_rowspan}"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
