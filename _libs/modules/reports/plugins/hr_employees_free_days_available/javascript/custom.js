/*
 * Function to lead clear AC
 */
function loadDependantAC(department, related_field) {
    // Show loading effect
    Effect.Center('loading');
    Effect.Appear('loading');

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_load_dependant_ac');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('department', department);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            var parent_cell = $(related_field);
            if ($('table_' + related_field)) {
                parent_cell = $('table_' + related_field);
            }
            while (parent_cell.tagName != 'TD') {
                parent_cell = parent_cell.parentNode;
            }

            parent_cell.innerHTML = data;
            var scripts = parent_cell.getElementsByTagName('SCRIPT');
            for (var j = 0; j < scripts.length; j++) {
                ajaxLoadJS(scripts[j]);
            }
        })
        .catch(error => {
            console.error(error);
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}
