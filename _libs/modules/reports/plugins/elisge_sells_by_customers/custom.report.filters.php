<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE CUSTOMERS' FILTER
            $filter = array (
                'custom_id'         => 'customers',
                'name'              => 'customers',
                'type'              => 'autocompleter',
                'required'          => 1,
                'autocomplete_type' => 'customers',
                'autocomplete_buttons' => 'clear',
                'label'             => $this->i18n('reports_customers'),
                'help'              => $this->i18n('reports_customers'),
                'value'             => ''
            );
            $filters['customers'] = $filter;

            return $filters;
        }
    }
?>