<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('FIRST_YEAR_OPTION', 2009);

            define('DOCUMENT_TYPE_WORKING_ID', 19);
            define('DOCUMENT_TYPE_FREE_DAYS_ID', 4);
            define('DOCUMENT_TYPE_SICKNESS_DAYS_ID', 18);
            define('WORKING_TIME_LAYOUT_ID', 391);

            // ACOUNTANT WHO CAN GENERATE THE REPORT FOR ALL CUSTOMERS
            define('USER_ACCOUNTANT_ID', '1,4,8,11,41,42');

            define('ADMINISTRATION', 'name_ad_user');
            define('VARNA_1', 'name_vn_user');
            define('VARNA_2', 'name_vno_user');
            define('DEPOT_1', 'name_depot_user');
            define('DEPOT_2', 'name_shed_user');
            define('SOFIA_1', 'name_s1_user');
            define('SOFIA_2', 'name_s2_user');
            define('BOURGAS', 'name_b_user');
            define('RUSE', 'name_r_user');

            define('ADMINISTRATION_DELAY', 'ad_delay');
            define('VARNA_1_DELAY', 'vn_delay');
            define('VARNA_2_DELAY', 'vno_delay');
            define('DEPOT_1_DELAY', 'depot_delay');
            define('DEPOT_2_DELAY', 'shed_delay');
            define('SOFIA_1_DELAY', 's1_delay');
            define('SOFIA_2_DELAY', 's2_delay');
            define('BOURGAS_DELAY', 'b_delay');
            define('RUSE_DELAY', 'r_delay');

            define('ADMINISTRATION_BEFORE', 'ad_before');
            define('VARNA_1_BEFORE', 'vn_before');
            define('VARNA_2_BEFORE', 'vno_before');
            define('DEPOT_1_BEFORE', 'depot_before');
            define('DEPOT_2_BEFORE', 'shed_before');
            define('SOFIA_1_BEFORE', 's1_before');
            define('SOFIA_2_BEFORE', 's2_before');
            define('BOURGAS_BEFORE', 'b_before');
            define('RUSE_BEFORE', 'r_before');

            define('FREE_DAYS_START_DATE', 'plr_leave_start_date');
            define('FREE_DAYS_FINISH_DATE', 'plr_leave_finish_date');
            define('FREE_DAYS_COUNT', 'plr_leave_days');
            define('FREE_DAYS_PAID_UNPAID', 'plr_leave_type');

            define('SICKNESS_VACATION_EMPLOYEE', 'name_person');
            define('SICKNESS_VACATION_STARTING_DATE', 'shospital_time');
            define('SICKNESS_VACATION_ENDING_DATE', 'fhospital_time');
            define('SICKNESS_VACATION_TOTAL_DAYS', 'total_days');

            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE YEARS FILTER
            //get available years
            $current_year = General::strftime("%Y");

            //prepare years options
            $options_years = array();

            for ($i=FIRST_YEAR_OPTION; $i<=$current_year; $i++) {
                $options_years[] = array(
                    'label'         => $i,
                    'option_value'  => sprintf("%d", $i)
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'years',
                'name'      => 'years',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_years'),
                'help'      => $this->i18n('reports_years_help'),
                'options'   => $options_years,
            );
            $filters['years'] = $filter;

            //DEFINE MONTHS FILTER
            //prepare years options
            $options_months = array();

            for ($i=1; $i<=12; $i++) {
                $time_month = mktime(0, 0, 0, $i);

                $options_months[] = array(
                    'label'         => General::strftime("%B", $time_month),
                    'option_value'  => sprintf("%s", $i)
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'month',
                'name'      => 'month',
                'type'      => 'dropdown',
                'required'  => 1,
                'label'     => $this->i18n('reports_month'),
                'help'      => $this->i18n('reports_month_help'),
                'options'   => $options_months,
            );
            $filters['month'] = $filter;

            $users_all_criteria = explode(',', USER_ACCOUNTANT_ID);

            if (in_array($registry['currentUser']->get('id'), $users_all_criteria)) {
                //DEFINE DEPARTMENT FILTER
                require_once PH_MODULES_DIR . 'layouts/models/layouts.factory.php';
                $filters_department =  array('model_lang' => $registry['lang'],
                                             'sanitize' => true,
                                             'where'  => array('l.active = 1',
                                                               'l.model = "Document"',
                                                               'l.model_type = ' . DOCUMENT_TYPE_WORKING_ID ,
                                                               'l.`system` = 0',
                                                               'l.visible = 1'
                                                        )
                                            );

                $departments = Layouts::search($registry, $filters_department);

                $options_departments = array();
                foreach($departments as $department) {
                    if ($department->get('id') != WORKING_TIME_LAYOUT_ID) {
                        $options_departments[] = array(
                            'label'         => $department->get('name'),
                            'option_value'  => $department->get('id')
                        );
                    }
                }

                //prepare filter
                $filter = array (
                    'custom_id' => 'department',
                    'name'      => 'department',
                    'type'      => 'checkbox_group',
                    'label'     => $this->i18n('reports_department'),
                    'help'      => $this->i18n('reports_department_help'),
                    'options'   => $options_departments,
                );
                $filters['department'] = $filter;

                //DEFINE EMPLOYEE FILTER
                //get employees' options
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters_employees = array('model_lang'    => $registry['lang'],
                                           'sanitize'      => true,
                                           'sort'          => array('ci18n.name', 'ci18n.lastname'),
                                           'where'         => array('c.type = ' . PH_CUSTOMER_EMPLOYEE,
                                                                    'c.active = 1'));
                $employees = Customers::search($registry, $filters_employees);

                //prepare documents' types groups
                $options_employees = array();

                foreach($employees as $employee) {
                    $options_employees[] = array(
                        'label'         => $employee->get('name') . ($employee->get('lastname') ? (' ' . $employee->get('lastname')) : ''),
                        'option_value'  => $employee->get('id')
                    );
                }

                //prepare filter
                $filter = array (
                    'custom_id' => 'employee',
                    'name'      => 'employee',
                    'type'      => 'dropdown',
                    'label'     => $this->i18n('reports_employee'),
                    'help'      => $this->i18n('reports_employee'),
                    'options'   => $options_employees,
                );
                $filters['employee'] = $filter;

                //DEFINE LATES FILTER
                //prepare lates settings
                $filter = array (
                    'name'          => 'lates',
                    'type'          => 'checkbox',
                    'label'         => $this->i18n('reports_lates'),
                    'help'          => $this->i18n('reports_lates_help'),
                    'option_value'  => '1',
                    'value'         => '1'
                );
                $filters['lates'] = $filter;

                //DEFINE EARLY GONE FILTER
                //prepare all settings
                $filter = array (
                    'name'          => 'early_gone',
                    'type'          => 'checkbox',
                    'label'         => $this->i18n('reports_early_gone'),
                    'help'          => $this->i18n('reports_early_gone'),
                    'option_value'  => '1',
                    'value'         => '1'
                );
                $filters['early_gone'] = $filter;
            }

            return $filters;
        }
    }
?>
