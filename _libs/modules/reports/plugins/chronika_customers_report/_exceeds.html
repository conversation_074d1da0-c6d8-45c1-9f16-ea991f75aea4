<table cellpadding="5" cellspacing="0" class="t_table t_list" width="100%">
  <tr>
    <th class="t_border t_caption"></th>
    <th class="t_border t_caption">{#reports_company#}</th>
    <th class="t_border t_caption">{#reports_business_line#}</th>
    <th class="t_border t_caption">{#reports_tax_included#}</th>
    <th class="t_border t_caption">{#reports_worked#}</th>
    <th class="t_border t_caption">{#reports_amount#}</th>
    <th class="t_border t_caption">{#reports_discount#}</th>
    <th class="t_caption">{#reports_total#}</th>
  </tr>
  {assign var=total value=0}
  {foreach from=$data item=items name=i key=k}
    {capture assign=ch_fn}recalcNadTotal('nad_{$smarty.foreach.i.iteration}'){/capture}
    <tr id="nad_dis_{$smarty.foreach.i.iteration}">
      <td class="t_border t_v_border">
        <div class="switch_expand" onclick="if (this.className == 'switch_expand') {ldelim}$('nad_{$smarty.foreach.i.iteration}').style.display=''; this.className='switch_collapse';{rdelim} else {ldelim}$('nad_{$smarty.foreach.i.iteration}').style.display='none';this.className='switch_expand';{rdelim}"></div></td>
      </td>
      <td class="t_border t_v_border">
        {$items[0].customer_name}
        <input type="hidden" name="dis_client[]" value="{$items[0].customer_name}"/>
        <input type="hidden" name="dis_client_id[]" value="{$items[0].customer}"/>
      </td>
      <td class="t_border t_v_border">
        {$items[0].bl_name}{if $items[0].bl_name_add} ({$items[0].bl_name_add}){/if}
        <input type="hidden" name="dis_bl[]" value="{$items[0].bl_name}"/>
        <input type="hidden" name="dis_bl_id[]" value="{$items[0].business_line}"/>
        <input type="hidden" name="nad_dis_desc[]" value="{$items[0].bl_name_add}"/>
      </td>
      <td class="t_border t_v_border">
        <input type="hidden" name="dis_quantity[]" value=""/>
        <input type="hidden" name="dis_mea[]" value="{$items[0].measure_rate}"/>
        {if $items[0].measure_rate eq 2}
          <span id="nad_included_{$smarty.foreach.i.iteration}">{$items[0].bl_pcs}</span> {#reports_pcs#}
        {else}
          <span id="nad_included_{$smarty.foreach.i.iteration}">{$items[0].bl_hours}</span> {#reports_hours#}
        {/if}
      </td>
      <td class="t_border t_v_border" style="text-align: right" id="nad_worked_{$smarty.foreach.i.iteration}">
        0.00
      </td>
      <td class="t_border t_v_border" style="text-align: right" id="nad_total_{$smarty.foreach.i.iteration}">
        0.00
      </td>
      <td class="t_border t_v_border" style="text-align: right">
        <input type="hidden" name="dis_all_bl[]" value=""/>
        <input type="hidden" name="dis_all_lv[]" value=""/>
        <input type="text" name="dis_discount[]" value="{$reports_additional_options.nad_dis.$k|default:''}" onkeypress="return changeKey(this, event, insertOnlyFloats)" onkeyup="{$ch_fn}" style="width: 80px; text-align: right"/>
        <input type="hidden" name="dis_discount_lv[]" value=""/>
        <input type="hidden" name="dis_total[]" value=""/>
        <input type="hidden" name="dis_total_lv[]" value=""/>
      </td>
      <td class="t_v_border" id="nad_sum_{$smarty.foreach.i.iteration}" style="text-align: right">
        0.00
      </td>
    </tr>
    <tr style="display: none" id="nad_{$smarty.foreach.i.iteration}">
      <td colspan="8" class="t_v_border" style="padding: 20px;">
        <table cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 100%; border: 1px solid #c1c1c1; border-bottom: none">
          <tr>
            <th class="t_border t_caption"></th>
            <th class="t_border t_caption">{#reports_date#}</th>
            <th class="t_border t_caption">{#reports_employee#}</th>
            <th class="t_border t_caption">{#reports_action_sub#}</th>
            <th class="t_border t_caption">{#reports_report#}</th>
            <th class="t_border t_caption">{#reports_time_pcs#}</th>
            <th class="t_border t_caption">{#reports_for_report#}</th>
            <th class="t_border t_caption">{#reports_rate#}</th>
            <th class="t_border t_caption">{#reports_amount#}</th>
          </tr>
          {foreach from=$items item=d name=dd}
            <tr id="nad_{$smarty.foreach.i.iteration}_{$smarty.foreach.dd.iteration}">
              <td class="t_border t_v_border">
                <input type="hidden" name="nad_u_key[]" value="{$d.key}"/>
                <input type="hidden" name="nad_old[]" value="{$d.old}"/>
                <input type="checkbox" name="nad_selected[]" {if !$reports_additional_options.nad_keys || isset($reports_additional_options.nad_keys[$d.key])}checked="checked"{/if} onclick="{$ch_fn}" value="{$smarty.foreach.dd.iteration-1}"/>
                <input type="hidden" name="nad_client[]" value="{$d.customer_name}"/>
                <input type="hidden" name="nad_client_id[]" value="{$d.customer}"/>
              </td>
              <td class="t_border t_v_border">
                <input type="hidden" name="nad_date[]" value="{$d.reported_date}"/>
                {$d.reported_date|date_format:#date_short#}
              </td>
              <td class="t_border t_v_border">
                <input type="hidden" name="nad_employee[]" value="{$d.employee_name}"/>
                <input type="hidden" name="nad_employee_id[]" value="{$d.employee}"/>
                <input type="hidden" name="empl_position[]" value="{$d.employee_position}"/>
                {$d.employee_code}
              </td>
              <td class="t_border t_v_border">
                <input type="hidden" name="nad_desc[]" value="{$items[0].bl_name_add}"/>
                <input type="hidden" name="nad_business_line[]" value="{$d.bl_name}"/>
                <input type="hidden" name="nad_business_line_id[]" value="{$d.business_line}"/>
                <input type="hidden" name="nad_activity[]" value="{$d.activity_name}"/>
                <input type="hidden" name="nad_activity_id[]" value="{$d.activity}"/>
                {$d.activity_name}{if $d.subactivity_name} / {$d.subactivity_name}{/if} 
              </td>
              <td class="t_border t_v_border">
                {$d.alt_description|default:$d.action_description}
              </td>
              <td class="t_border t_v_border" style="text-align: right">
                {if $d.measure_rate eq 2}
                  {$d.qty_all}
                  {assign var=quantity value=$d.qty_all}
                {else}
                  {$d.time_correction_four}{if $d.time_to_invoice} ({$d.time_to_invoice}){/if}
                  {assign var=quantity value=$d.time_to_invoice}
                {/if}
              </td>
              <td class="t_border t_v_border" style="text-align: right">
                <input type="hidden" name="nad_measure[]" value="{$d.measure_rate}"/>
                <input type="hidden" name="nad_time_real[]" value="0"/>
                {if $d.measure_rate eq 2}
                  <input type="text" name="nad_time[]" value="{$reports_additional_options.nad_keys[$d.key]|default:$d.qty_all}" class="readonly" readonly="readonly" style="width: 80px; text-align: right"/>
                {else}
                  {capture assign=cValue}{if preg_match('#\d+:\d+#', $d.time_to_invoice)}{$d.time_to_invoice}{else}00:{$d.time_to_invoice}{/if}{/capture}
                  {capture assign=idx}{$smarty.foreach.i.iteration}_{$smarty.foreach.dd.iteration}{/capture}
                  {include file=`$theme->templatesDir`input_time.html
                      name=nad_time
                      index=$idx
                      empty_indexes=true
                      value=$reports_additional_options.nad_keys[$d.key]|default:$cValue
                      standalone=true
                      onchange=$ch_fn
                      width=80
                  }
                {/if}
              </td>
              <td class="t_border t_v_border" style="text-align: right">
                <input type="hidden" name="nad_price[]" value="{$d.price}"/>
                <input type="hidden" name="nad_price_lv[]" value="{$d.priceBGN}"/>
                <input type="hidden" name="nad_sum[]" value="0.00"/>
                <input type="hidden" name="nad_sum_lv[]" value="0.00"/>
                {$d.price}
              </td>
              <td class="t_v_border nad_subtotal" style="text-align: right">
                0.00 (0.00)
              </td>
            </tr>
          {/foreach}
        </table>
        <script type="text/javascript">
            recalcNadTotal('nad_{$smarty.foreach.i.iteration}');
        </script>
      </td>
    </tr>
  {/foreach}
</table>
