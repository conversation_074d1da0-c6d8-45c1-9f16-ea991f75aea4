reports_filter_period_from = Date of payment
reports_show_only_paid = Show all without overdue
reports_filter_client = Client (UCN/EIK)
reports_filter_contract = Credit record num
reports_filter_period_from_help = Choose a future period.

reports_table_title = Estimated earnings per active contracts for a period
reports_th_contract_number_date = Contract number (date)/ payments
reports_th_customer_ucn = Client (PIN)
reports_th_currency = Currency
reports_th_customer_phone = Client phone
reports_th_upcoming_installment = Upcoming contributions
reports_th_date_of_payment = Date of payment
reports_th_principal = Principal
reports_th_interest_rate = Interest rate
reports_th_guarantor_fee = Guarantor fee
reports_th_tax_management = Tax management
reports_th_tax_angagement = Tax arrangement
reports_th_total = Total
reports_th_overdue_installments = Overdue installments
reports_th_date_of_payment_amount = Date of payment / Amount
reports_th_lpg = LPG
reports_th_count = Count
reports_th_principals = Principals
reports_th_interest_rates = Interest rates
reports_th_guarantor_fees = Guarantor fees
reports_th_tax_managements = Tax management
reports_th_tax_angagements = Tax arrangement
reports_th_punishment_interest_rates = Punishment interest rates
reports_th_total_overdue = Total overdue
reports_th_total_expected_earning = Total expected earnings
reports_th_percent_overdue_installments = % overdue installments
reports_th_totals = Total (BGN)

error_reports_past_period = The selected period must not include days before the current date!
