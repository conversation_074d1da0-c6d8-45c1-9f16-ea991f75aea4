<?php
    Class Translators_Choose_Translator Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

            if (!empty($filters['language'])) {
                // get data for the additional vars
                $translators_additional_vars = array(LANGUAGE_CLASS_A_VAR, LANGUAGE_CLASS_B_VAR, LANGUAGE_CLASS_C_VAR, LANGUAGE_COMPETENCE_VAR, TRANSLATOR_LANGUAGE, TRANSLATION_PRICE);

                //sql to take the ids of the needed additional vars
                $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Customer" AND fm.name IN ("' . implode('","', $translators_additional_vars) . '") AND fm.model_type="' . PH_CUSTOMER_EMPLOYEE . '"';

                $translator_add_vars = $registry['db']->GetAll($sql_for_add_vars);

                $translator_competence_class_a = '';
                $translator_competence_class_b = '';
                $translator_competence_class_c = '';
                $translator_language_competence = '';
                $translator_translator_language = '';
                $translator_translation_price = '';

                foreach ($translator_add_vars as $cus_var) {
                    if ($cus_var['name'] == LANGUAGE_CLASS_A_VAR) {
                        $translator_competence_class_a =  $cus_var['id'];
                    } elseif ($cus_var['name'] == LANGUAGE_CLASS_B_VAR) {
                        $translator_competence_class_b =  $cus_var['id'];
                    } elseif ($cus_var['name'] == LANGUAGE_CLASS_C_VAR) {
                        $translator_competence_class_c =  $cus_var['id'];
                    } elseif ($cus_var['name'] == LANGUAGE_COMPETENCE_VAR) {
                        $translator_language_competence =  $cus_var['id'];
                    } elseif ($cus_var['name'] == TRANSLATOR_LANGUAGE) {
                        $translator_translator_language =  $cus_var['id'];
                    } elseif ($cus_var['name'] == TRANSLATION_PRICE) {
                        $translator_translation_price =  $cus_var['id'];
                    }
                }

                // get the doctors working schedules
                $sql_translators = array();
                $sql_translators['select'] = 'SELECT c.id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, c_cstm_lang.value as language, ni18n.name as language_name, ' . "\n" .
                                             '  c_cstm_competence_a.value as competence_a, c_cstm_competence_b.value as competence_b, c_cstm_competence_c.value as competence_c,' . "\n" .
                                             '  c_cstm_price.value as price, u.id as customer_user';
                $sql_translators['from']   = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                             '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                                             '  ON (u.employee=c.id)' . "\n" .
                                             'INNER JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_lang' . "\n" .
                                             ' ON (c_cstm_lang.model_id=c.id AND c_cstm_lang.var_id="' . $translator_translator_language . '" AND c_cstm_lang.value="' . $filters['language'] . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                             '  ON (ni18n.parent_id=c_cstm_lang.value AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_competence_a' . "\n" .
                                             ' ON (c_cstm_competence_a.model_id=c.id AND c_cstm_competence_a.var_id="' . $translator_competence_class_a . '" AND c_cstm_competence_a.num=c_cstm_lang.num)' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_competence_b' . "\n" .
                                             ' ON (c_cstm_competence_b.model_id=c.id AND c_cstm_competence_b.var_id="' . $translator_competence_class_b . '" AND c_cstm_competence_b.num=c_cstm_lang.num)' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_competence_c' . "\n" .
                                             ' ON (c_cstm_competence_c.model_id=c.id AND c_cstm_competence_c.var_id="' . $translator_competence_class_c . '" AND c_cstm_competence_c.num=c_cstm_lang.num)' . "\n" .
                                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_price' . "\n" .
                                             ' ON (c_cstm_price.model_id=c.id AND c_cstm_price.var_id="' . $translator_translation_price . '" AND c_cstm_price.num=c_cstm_lang.num)' . "\n";

                $where = array();
                $where[] = 'c.active=1';
                $where[] = 'c.deleted_by=0';
                $where[] = 'c.subtype="normal"';
                $where[] = 'c.type="' . PH_CUSTOMER_EMPLOYEE . '"';
                if (!empty($filters['translator'])) {
                    $where[] = 'c.id="' . $filters['translator'] . '"';
                }
                if (!empty($filters['language_class'])) {
                    $where[] = sprintf('c_cstm_competence_%s.value="%s"', strtolower($filters['language_class']), LANGUAGE_CLASS_YES);
                }
                $sql_translators['where'] = 'WHERE ' . implode(' AND ', $where);

                $query_translators = implode("\n", $sql_translators);
                $results_translators = $registry['db']->GetAll($query_translators);

                foreach ($results_translators as $translator) {
                    if (!isset($final_results[$translator['id']])) {
                        $final_results[$translator['id']] = array(
                            'id'         => $translator['id'],
                            'name'       => $translator['name'],
                            'competence' => array(),
                            'phones'     => array(),
                            'text_areas' => array(),
                            'tags'       => array(),
                            'documents'  => array(),
                            'user'       => $translator['customer_user']
                        );
                    }
                    if (empty($final_results[$translator['id']]['competence']['A']) && $translator['competence_a']==LANGUAGE_CLASS_YES) {
                        $final_results[$translator['id']]['competence']['A'] = $translator['price'];
                    }
                    if (empty($final_results[$translator['id']]['competence']['B']) && $translator['competence_b']==LANGUAGE_CLASS_YES) {
                        $final_results[$translator['id']]['competence']['B'] = $translator['price'];
                    }
                    if (empty($final_results[$translator['id']]['competence']['C']) && $translator['competence_c']==LANGUAGE_CLASS_YES) {
                        $final_results[$translator['id']]['competence']['C'] = $translator['price'];
                    }
                }

               // clear competences if the competence filter is set
               if (!empty($filters['language_class'])) {
                   foreach ($final_results as $fr_id => $fr) {
                       foreach ($fr['competence'] as $comp_type => $comp_price) {
                           if ($comp_type != $filters['language_class']) {
                               unset($final_results[$fr_id]['competence'][$comp_type]);
                           }
                       }
                       if (empty($final_results[$fr_id]['competence'])) {
                           unset($final_results[$fr_id]['competence']);
                       }
                   }
               }

               // get the specialized areas
               if (!empty($final_results)) {
                   $sql_specialized_areas = array();
                   $sql_specialized_areas['select'] = 'SELECT c.id, c_cstm_text_area.value as text_area, c_cstm_text_area_name.label as text_area_name' . "\n";
                   $sql_specialized_areas['from']   = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                                      'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_text_area' . "\n" .
                                                      ' ON (c_cstm_text_area.model_id=c.id AND c_cstm_text_area.var_id="' . $translator_language_competence . '")' . "\n" .
                                                      'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS c_cstm_text_area_name' . "\n" .
                                                      '  ON (c_cstm_text_area_name.parent_name="' . LANGUAGE_COMPETENCE_VAR . '" AND c_cstm_text_area_name.option_value=c_cstm_text_area.value AND c_cstm_text_area_name.lang="' . $model_lang . '")' . "\n";
                   $sql_specialized_areas['where']  = 'WHERE c.id IN (' . implode(',', array_keys($final_results)) . ') AND c_cstm_text_area.value IS NOT NULL AND c_cstm_text_area.value!="0" AND c_cstm_text_area.value!=""';

                   $query_specialized_areas = implode("\n", $sql_specialized_areas);
                   $results_specialized_areas = $registry['db']->GetAll($query_specialized_areas);

                   // place the found data in the final results
                   foreach ($results_specialized_areas as $rsa) {
                       if (isset($final_results[$rsa['id']]) && !array_key_exists($rsa['text_area'], $final_results[$rsa['id']]['text_areas'])) {
                           $final_results[$rsa['id']]['text_areas'][$rsa['text_area']] = $rsa['text_area_name'];
                       }
                   }
               }

               // if the filter for text areas is set then clear the not needed results
               if (!empty($filters['language_competence'])) {
                   foreach ($final_results as $fr_key => $fr) {
                       if (!array_key_exists($filters['language_competence'], $fr['text_areas'])) {
                           unset($final_results[$fr_key]);
                       }
                   }
               }

               if (!empty($final_results)) {
                   // get tags and phones
                   $sql_tags_phones = array();
                   $sql_tags_phones['select'] = 'SELECT c.id, c.phone, c.gsm, t.id as tag, t.color as tag_color, ti18n.name as tag_name ' . "\n";
                   $sql_tags_phones['from']   = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                                                '  ON (ct.model=\'Customer\' AND ct.model_id=c.id)' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                                                '  ON (ct.tag_id=t.id)' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                                                '  ON (t.id=ti18n.parent_id AND ti18n.lang="' . $model_lang . '")' . "\n";
                   $sql_tags_phones['where']  = 'WHERE c.id IN (' . implode(',', array_keys($final_results)) . ')';
                   $query_tags_phones = implode("\n", $sql_tags_phones);
                   $results_tags_phones = $registry['db']->GetAll($query_tags_phones);

                   // process results
                   foreach ($results_tags_phones as $rtp) {
                       if (isset($final_results[$rtp['id']])) {
                           // process phones
                           if ((!empty($rtp['phone']) || !empty($rtp['gsm'])) && empty($final_results[$rtp['id']]['phones'])) {
                               $phones_list = array();
                               $contact_types = array('phone', 'gsm');
                               foreach ($contact_types as $con_type) {
                                   if (!empty($rtp[$con_type])) {
                                       $contact_rows = explode("\n", $rtp[$con_type]);
                                       foreach($contact_rows as $idx => $contact_data) {
                                           if (strstr($contact_data, '|')) {
                                               @list($contact) = explode("|", $contact_data);
                                               $phones_list[] = array(
                                                   'type' => $con_type,
                                                   'num'  => $contact
                                               );
                                           } else {
                                               $phones_list[] = array(
                                                   'type' => $con_type,
                                                   'num'  => $contact_data
                                               );
                                           }
                                       }
                                   }
                               }
                               $final_results[$rtp['id']]['phones'] = $phones_list;
                           }

                           // process tags
                           if (!empty($rtp['tag'])) {
                               $final_results[$rtp['id']]['tags'][] = array(
                                   'id'    => $rtp['tag'],
                                   'name'  => $rtp['tag_name'],
                                   'color' => $rtp['tag_color'],
                               );
                           }
                       }
                   }

                   // get customers users
                   $customers_users = array();
                   foreach ($final_results as $fr_key => $fr) {
                       if (!empty($fr['user'])) {
                           $customers_users[$fr['user']] = $fr_key;
                       }
                   }

                   if (!empty($customers_users)) {
                       // get additional vars for the required dcoument
                       $additional_vars_names = array(TRANSLATE_REQUEST_LANGUAGE, TRANSLATE_REQUEST_COMPLEXITY, TRANSLATE_REQUEST_NUM_SHEETS);

                       //sql to take the ids of the needed additional vars
                       $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.name IN ("' . implode('","', $additional_vars_names) . '") AND fm.model_type="' . DOCUMENT_TRANSLATE_REQUEST . '"';

                       $doc_add_vars = $registry['db']->GetAll($sql_for_add_vars);

                       $translate_request_language_id = '';
                       $translate_request_complexity_id = '';
                       $translate_request_num_sheets_id = '';

                       foreach ($doc_add_vars as $doc_var) {
                           if ($doc_var['name'] == TRANSLATE_REQUEST_LANGUAGE) {
                               $translate_request_language_id =  $doc_var['id'];
                           } elseif ($doc_var['name'] == TRANSLATE_REQUEST_COMPLEXITY) {
                               $translate_request_complexity_id =  $doc_var['id'];
                           } elseif ($doc_var['name'] == TRANSLATE_REQUEST_NUM_SHEETS) {
                               $translate_request_num_sheets_id =  $doc_var['id'];
                           }
                       }

                       // get documents which the translators are currently working on
                       $sql_documents = array();
                       $sql_documents['select'] = 'SELECT d.id, d.full_num, d.deadline, da.assigned_to as user_id, ni18n.name as language, fi18n.label as complexity_level, d_cstm_num_sheets.value as num_sheets ' . "\n";
                       $sql_documents['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                  'INNER JOIN ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS . ' AS da' . "\n" .
                                                  ' ON (d.id=da.parent_id AND da.assigned_to IN (' . implode(',', array_keys($customers_users)) . ') AND da.assignments_type="' . PH_ASSIGNMENTS_OWNER . '")' . "\n" .
                                                  'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_lang' . "\n" .
                                                  ' ON (d_cstm_lang.model_id=d.id AND d_cstm_lang.var_id="' . $translate_request_language_id . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                                  '  ON (ni18n.parent_id=d_cstm_lang.value AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_complexity' . "\n" .
                                                  ' ON (d_cstm_complexity.model_id=d.id AND d_cstm_complexity.var_id="' . $translate_request_complexity_id . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fi18n' . "\n" .
                                                  '  ON (fi18n.parent_name="' . TRANSLATE_REQUEST_COMPLEXITY . '" AND fi18n.option_value=d_cstm_complexity.value AND fi18n.lang="' . $model_lang . '")' . "\n" .
                                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_num_sheets' . "\n" .
                                                  ' ON (d_cstm_num_sheets.model_id=d.id AND d_cstm_num_sheets.var_id="' . $translate_request_num_sheets_id . '")' . "\n";

                       $where = array();
                       $where[] = 'd.deleted_by=0';
                       $where[] = 'd.active=1';
                       $where[] = 'd.type="' . DOCUMENT_TRANSLATE_REQUEST . '"';
                       $where[] = 'd.status!="closed"';

                        $sql_documents['where'] = 'WHERE ' . implode(' AND ', $where);

                        $query_documents = implode("\n", $sql_documents);
                        $results_documents = $registry['db']->GetAll($query_documents);

                        foreach ($results_documents as $res_doc) {
                            if (isset($customers_users[$res_doc['user_id']]) && isset($final_results[$customers_users[$res_doc['user_id']]])) {
                                $final_results[$customers_users[$res_doc['user_id']]]['documents'][$res_doc['id']] = array(
                                    'id'               => $res_doc['id'],
                                    'num'              => $res_doc['full_num'],
                                    'deadline'         => $res_doc['deadline'],
                                    'language'         => $res_doc['language'],
                                    'complexity_level' => $res_doc['complexity_level'],
                                    'num_sheets'       => $res_doc['num_sheets']
                                );
                            }
                        }
                   }
               }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>