<w:p>
    <w:pPr>
        <w:sectPr>
            <w:pgSz w:w="11906" w:h="16838"/>
            <w:type w:val="nextPage"/>
            <!-- Same as the section from the template -->
            <w:pgMar w:top="1134" w:right="794" w:bottom="720" w:left="851" w:header="454" w:footer="227" w:gutter="0"/>
            <w:cols w:space="708"/>
            <w:docGrid w:linePitch="360"/>
        </w:sectPr>
    </w:pPr>
</w:p>
<w:p>
    <w:r>
        <w:rPr>
            <w:noProof/>
        </w:rPr>
        <w:lastRenderedPageBreak/>
        <w:drawing>
            <wp:anchor distT="0" distB="0" distL="114300" distR="114300" simplePos="0" relativeHeight="251658240" behindDoc="0" locked="0" layoutInCell="1" allowOverlap="1">
                <wp:simplePos x="0" y="0"/>
                <wp:positionH relativeFrom="page">
                    <wp:align>left</wp:align>
                </wp:positionH>
                <wp:positionV relativeFrom="page">
                    <wp:align>top</wp:align>
                </wp:positionV>
                <wp:extent cx="7556558" cy="10676802"/>
                <wp:effectExtent l="0" t="0" r="0" b="0"/>
                <wp:wrapNone/>
                <wp:docPr id="3001" name="Certificate"/>
                <wp:cNvGraphicFramePr>
                    <a:graphicFrameLocks xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" noChangeAspect="1"/>
                </wp:cNvGraphicFramePr>
                <a:graphic xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main">
                    <a:graphicData uri="http://schemas.openxmlformats.org/drawingml/2006/picture">
                        <pic:pic xmlns:pic="http://schemas.openxmlformats.org/drawingml/2006/picture">
                            <pic:nvPicPr>
                                <pic:cNvPr id="0" name="Certificate"/>
                                <pic:cNvPicPr>
                                    <a:picLocks noChangeAspect="1" noChangeArrowheads="1"/>
                                </pic:cNvPicPr>
                            </pic:nvPicPr>
                            <pic:blipFill>
                                <a:blip r:embed="{$rId}">
                                    <a:extLst>
                                        {literal}<a:ext uri="{28A0092B-C50C-407E-A947-70E740481C1C}">{/literal}
                                            <a14:useLocalDpi xmlns:a14="http://schemas.microsoft.com/office/drawing/2010/main" val="0"/>
                                        </a:ext>
                                    </a:extLst>
                                </a:blip>
                                <a:srcRect/>
                                <a:stretch>
                                    <a:fillRect/>
                                </a:stretch>
                            </pic:blipFill>
                            <pic:spPr bwMode="auto">
                                <a:xfrm>
                                    <a:off x="0" y="0"/>
                                    <a:ext cx="7556558" cy="10676802"/>
                                </a:xfrm>
                                <a:prstGeom prst="rect">
                                    <a:avLst/>
                                </a:prstGeom>
                                <a:noFill/>
                                <a:ln>
                                    <a:noFill/>
                                </a:ln>
                            </pic:spPr>
                        </pic:pic>
                    </a:graphicData>
                </a:graphic>
                <wp14:sizeRelH relativeFrom="margin">
                    <wp14:pctWidth>0</wp14:pctWidth>
                </wp14:sizeRelH>
                <wp14:sizeRelV relativeFrom="margin">
                    <wp14:pctHeight>0</wp14:pctHeight>
                </wp14:sizeRelV>
            </wp:anchor>
        </w:drawing>
    </w:r>
</w:p>