<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <table border="1" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td nowrap="nowrap" align="center"><div>{#num#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_production_date#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_contract_number#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_kles_num#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_production_number#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_customer#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_address#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_total_area#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_counts#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_install_date#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_pvc_profile#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_profile_color#|escape}</div></td>
      <td nowrap="nowrap" align="center"><div>{#reports_others_pvc#|escape}</div></td>
    </tr>
    {counter start=0 name='item_counter' print=false}
    {foreach from=$reports_results item=result}
      <tr>
        <td nowrap="nowrap" align="right">
          {counter name='item_counter' print=true}
        </td>
        <td align="left">
          {$result.production_date|date_format:#date_short#|escape}
        </td>
        <td align="left">
          {$result.contract_number}
        </td>
        <td align="left">
          {$result.kles_num|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.production_num}
        </td>
        <td align="left">
          {$result.customer|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.address|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.total_area}
        </td>
        <td align="left">
          {$result.counts_pvc}
        </td>
        <td align="left">
          {$result.install_date|date_format:#date_short#|escape}
        </td>
        <td align="left">
          {$result.pvc_profile|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.profile_color|escape|default:"&nbsp;"}
        </td>
        <td align="left">
          {$result.other_pvc|escape|default:"&nbsp;"}
        </td>
      </tr>
    {foreachelse}
      <tr>
        <td colspan="10">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
  </table>
</body>
</html>