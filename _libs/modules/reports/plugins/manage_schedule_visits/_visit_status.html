<td class="labelbox">
  <a name="error_visit_status"></a>
  <label for="visit_status">{#reports_status#|escape}:</label>
</td>
<td class="unrequired">&nbsp;</td>
<td nowrap="nowrap">
  {foreach from=$status_list item=status_data key=status_key name=sl}
    <input type="radio" value="{$status_key}"{if $status eq $status_key} checked="checked"{/if}{if $status_data.disabled} disabled="disabled"{/if} name="visit_status" id="visit_status_{$smarty.foreach.sl.iteration}" /> <label for="visit_status_{$smarty.foreach.sl.iteration}">{$status_data.name|escape}</label>{if !$smarty.foreach.sl.last}<br />{/if}
  {/foreach}
  <input type="hidden" name="active" id="active" value="{$visit_active}" />
  <input type="hidden" name="group" id="group" value="{$visit_group}" />
</td>