<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td rowspan="2" class="t_border vmiddle" nowrap="nowrap">{#num#|escape}</td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 90px;">{#reports_income_num#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 110px;">{#reports_damage_num#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 110px;">{#reports_policy_num#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 110px;">{#reports_signal_num#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 190px;">{#reports_client#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 90px;">{#reports_ptp_date#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 110px;">{#reports_reg_num#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 140px;">{#reports_car#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 190px;">{#reports_signal_date_hour#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 140px;">{#reports_type_damage#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 140px;">{#reports_type_risk#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 190px;">{#reports_ptp_location#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 110px;">{#reports_responsible#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 190px;">{#reports_reported_by#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 190px;">{#reports_owner#|escape}</div></td>
          <td colspan="4" class="t_border vmiddle"><div style="">{#reports_heavy_damage#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_claimed_amount#|escape}</div></td>
          {if $reports_additional_options.show_claimed_reserved_amount}
            <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_claimed_resserved_amount#|escape}</div></td>
          {/if}
          <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_counterclaim_evaluation#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_nonmaterial_damages#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_material_damages#|escape}</div></td>
          <td rowspan="2" class="t_border vmiddle"><div style="width: 95px;">{#reports_case_law#|escape}</div></td>
          <td rowspan="2" class="vmiddle"><div style="width: 190px;">{#reports_status#|escape}</div></td>
        </tr>
        <tr class="reports_title_row hcenter">
          <td class="t_border vmiddle"><div style="width: 220px;">{#reports_incident_description#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 160px;">{#reports_ptp_participants#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 160px;">{#reports_heir#|escape}</div></td>
          <td class="t_border vmiddle"><div style="width: 160px;">{#reports_collateral#|escape}</div></td>
        </tr>
        {counter start=$pagination.start name='item_counter' print=false}
        {foreach from=$reports_results item=result}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border hright" nowrap="nowrap" width="25">
              {counter name='item_counter' print=true}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.id}" target="_blank">{$result.full_num}</a>
            </td>
            <td class="t_border">
              {$result.custom_num|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.policy_num|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.signal_num|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}" target="_blank">{$result.customer_name|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              {$result.date|date_format:#date_short#|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {foreach from=$result.reg_num item=reg_num name=rn}
                {$reg_num|escape|default:"&nbsp;"}{if !$smart.foreach.rn.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border">
              {foreach from=$result.car item=car name=cr}
                {$car|escape|default:"&nbsp;"}{if !$smart.foreach.cr.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border">
              {$result.signal_datetime|date_format:#date_mid#|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {foreach from=$result.type_damage item=type_damage name=td}
                {$type_damage|escape|default:"&nbsp;"}{if !$smart.foreach.td.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border">
              {$result.type_risk|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.location|escape|nl2br|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.responsible|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.send_signal|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.owner|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.incident_description|escape|nl2br|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {foreach from=$result.ptp_participants item=participant name=ptp}
                {$participant|escape|default:"&nbsp;"}{if !$smart.foreach.ptp.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border">
              {foreach from=$result.heirs item=heir name=hr}
                {$heir|escape|default:"&nbsp;"}{if !$smart.foreach.hr.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="t_border">
              {$result.collateral|escape|nl2br|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.claimed_amount|escape|default:"&nbsp;"}
            </td>
            {if $reports_additional_options.show_claimed_reserved_amount}
              <td class="t_border">
                {$result.claimed_reserved_amount|escape|default:"&nbsp;"}
              </td>
            {/if}
            <td class="t_border">
              {$result.counterclaim_evaluation|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.nonmaterial_damages|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.material_damages|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.case_law|escape|default:"&nbsp;"}
            </td>
            <td>
              {$result.status|escape|default:"&nbsp;"}
            </td>
          </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="{$reports_additional_options.total_colspan}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
        <tr>
          <td class="t_footer" colspan="{$reports_additional_options.total_colspan}"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="pagemenu" bgcolor="#FFFFFF">
      {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=reports&amp;report_type={$report_type}&amp;page={/capture}
      {include file="`$theme->templatesDir`pagination.html"
      found=$pagination.found
      total=$pagination.total
      rpp=$pagination.rpp
      page=$pagination.page
      pages=$pagination.pages
      hide_selection_stats=1
      link=$link
      }
    </td>
  </tr>
</table>
