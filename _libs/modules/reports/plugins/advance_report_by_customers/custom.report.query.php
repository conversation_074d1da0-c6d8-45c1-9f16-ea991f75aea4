<?php
    Class Advance_Report_By_Customers Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();
            $current_report_additional_vars = array(ROLE_USER_ID, BANK_ID, AGENT_ID, ROLE_TYPE, RATING_TYPE, PERIOD_DATE);

            //sql to take the ids of the needed additional vars
            $sql_for_document_add_vars = 'SELECT fm.id, fm.name, fm.type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND (fm.name LIKE "' . OBJECT_TYPE_PREFIX . '%" OR fm.name IN ("' . implode('","', $current_report_additional_vars) . '") OR fm.type="config") AND fm.model_type="' . DOCUMENT_REPORT . '"';
            $document_add_vars = $registry['db']->GetAll($sql_for_document_add_vars);

            $object_type_var_ids = array();
            $object_type_var_names = array();
            $congifurators_var_ids = array();
            $role_user_id_var_id = '';
            $role_type_var_id = '';
            $rating_type_var_id = '';
            $bank_id_var_id = '';
            $agent_var_id = '';
            $period_var_id = '';

            foreach ($document_add_vars as $doc_var) {
                if (preg_match('#^' . OBJECT_TYPE_PREFIX . '.*$#', $doc_var['name'])) {
                    $object_type_var_ids[] = $doc_var['id'];
                    $object_type_var_names[] = $doc_var['name'];
                } elseif ($doc_var['name'] == ROLE_USER_ID) {
                    $role_user_id_var_id = $doc_var['id'];
                } elseif ($doc_var['name'] == BANK_ID) {
                    $bank_id_var_id = $doc_var['id'];
                } elseif ($doc_var['name'] == ROLE_TYPE) {
                    $role_type_var_id = $doc_var['id'];
                } elseif ($doc_var['name'] == RATING_TYPE) {
                    $rating_type_var_id =  $doc_var['id'];
                } elseif ($doc_var['name'] == AGENT_ID) {
                    $agent_var_id =  $doc_var['id'];
                } elseif ($doc_var['name'] == PERIOD_DATE) {
                    $period_var_id =  $doc_var['id'];
                } elseif ($doc_var['type'] == 'config') {
                    $congifurators_var_ids[] = $doc_var['id'];
                }
            }

            $roles_ids = array(ROLE_MADE_BY, ROLE_OBSERVED, ROLE_RATED, ROLE_SIGNED);
            // Get the documents data
            $sql_data['select'] = 'SELECT d.id, d.full_num, d.date, d.status, d.substatus, ds.name as substatus_name, d.customer, CONCAT(ci18n_customer.name, \' \', ci18n_customer.lastname) as customer_name, ' . "\n" .
                                  '  ci18n_customer.address, c.gsm, c.phone, c.email, d_cstm_roles.value as role_id, d_cstm_role_user.value as role_user, CONCAT(ci18n_role_user_name.name, \' \', ci18n_role_user_name.lastname) as role_user_name, ' . "\n" .
                                  '  d_cstm_bank.value as bank, CONCAT(ci18n_bank_name.name, \' \', ci18n_bank_name.lastname) as bank_name, ' . "\n" .
                                  '  d_cstm_agent.value as agent, CONCAT(ci18n_agent_name.name, \' \', ci18n_agent_name.lastname) as agent_name, d_cstm_period.value as period, ' . "\n" .
                                  '  d_cstm_rating_type.value as rating_type, d_cstm_rating_type_name.name as rating_type_name, bb.params as bb_data, bb.id as bb_id, dm.name as media ' . "\n";

            $sql_data['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                  '  ON (ds.id=d.substatus AND ds.doc_type=d.type AND ds.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                  '  ON (c.id=d.customer)' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_customer' . "\n" .
                                  '  ON (ci18n_customer.parent_id=d.customer AND ci18n_customer.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_roles' . "\n" .
                                  '  ON (d_cstm_roles.model_id=d.id AND d_cstm_roles.var_id="' . $role_type_var_id . '" AND d_cstm_roles.value IN ("' . implode('","', $roles_ids) . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_role_user' . "\n" .
                                  '  ON (d_cstm_role_user.model_id=d.id AND d_cstm_role_user.var_id="' . $role_user_id_var_id . '" AND d_cstm_role_user.num=d_cstm_roles.num)' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_role_user_name' . "\n" .
                                  '  ON (ci18n_role_user_name.parent_id=d_cstm_role_user.value AND ci18n_role_user_name.lang="' . $model_lang . '")' . "\n" .

                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_bank' . "\n" .
                                  '  ON (d_cstm_bank.model_id=d.id AND d_cstm_bank.var_id="' . $bank_id_var_id . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_bank_name' . "\n" .
                                  '  ON (ci18n_bank_name.parent_id=d_cstm_bank.value AND ci18n_bank_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_agent' . "\n" .
                                  '  ON (d_cstm_agent.model_id=d.id AND d_cstm_agent.var_id="' . $agent_var_id . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_agent_name' . "\n" .
                                  '  ON (ci18n_agent_name.parent_id=d_cstm_agent.value AND ci18n_agent_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_BB . ' AS bb' . "\n" .
                                  '  ON (bb.model="Document" AND bb.model_type="' . DOCUMENT_REPORT . '" AND bb.model_id=d.id AND bb.meta_id IN (' . implode(',', $congifurators_var_ids) . '))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rating_type' . "\n" .
                                  '  ON (d_cstm_rating_type.model_id=d.id AND d_cstm_rating_type.var_id="' . $rating_type_var_id . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS d_cstm_rating_type_name' . "\n" .
                                  '  ON (d_cstm_rating_type_name.parent_id=d_cstm_rating_type.value AND d_cstm_rating_type_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_period' . "\n" .
                                  '  ON (d_cstm_period.model_id=d.id AND d_cstm_period.var_id="' . $period_var_id . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_MEDIAS . ' AS dm' . "\n" .
                                  '  ON (dm.id=d.media AND dm.lang="' . $model_lang . '")' . "\n";

            // construct where
            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.active=1';
            $where[] = 'd.type="' . DOCUMENT_REPORT . '"';
            $where[] = 'c.is_company IN (' . $filters['is_company'] . ')';
            if (!empty($filters['from_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_period.value, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
            }
            if (!empty($filters['to_date'])) {
                $where[] = 'DATE_FORMAT(d_cstm_period.value, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
            }
            if (!empty($filters['user_bank'])) {
                $where[] = 'd_cstm_bank.value="' . $filters['user_bank'] . '"';
            }
            if (!empty($filters['client'])) {
                $where[] = 'd.customer="' . $filters['client'] . '"';
            }
            if (!empty($filters['agent'])) {
                $where[] = 'd_cstm_agent.value="' . $filters['agent'] . '"';
            }

            $sql_data['where'] = 'WHERE ' . implode(' AND ', $where);
            $sql_data['sort']  = 'ORDER BY d.id DESC';

            $query_data = implode("\n", $sql_data);
            $document_results = $registry['db']->GetAll($query_data);

            $final_results = array();
            $object_ids = array();
            foreach ($document_results as $doc_res) {
                if (!isset($final_results[$doc_res['id']])) {
                    // $bb_data = unserialize($doc['bb_data']);
                    $final_results[$doc_res['id']]['id'] = $doc_res['id'];
                    $final_results[$doc_res['id']]['full_num'] = $doc_res['full_num'];
                    $final_results[$doc_res['id']]['date'] = $doc_res['date'];
                    $phase = '';
                    if ($doc_res['substatus']) {
                        $phase = $doc_res['substatus_name'];
                    } elseif ($doc_res['status'] == 'opened') {
                        $phase = $registry['translater']->translate('reports_status_opened_no_substatus');
                    }
                    $final_results[$doc_res['id']]['phase'] = $phase;
                    $final_results[$doc_res['id']]['media'] = $doc_res['media'];
                    $final_results[$doc_res['id']]['customer'] = $doc_res['customer'];
                    $final_results[$doc_res['id']]['customer_name'] = $doc_res['customer_name'];
                    $final_results[$doc_res['id']]['role_make_report'] = array();
                    $final_results[$doc_res['id']]['role_observed'] = array();
                    $final_results[$doc_res['id']]['role_check_rating'] = array();
                    $final_results[$doc_res['id']]['role_sign_report'] = array();
                    $final_results[$doc_res['id']]['bank'] = $doc_res['bank'];
                    $final_results[$doc_res['id']]['bank_name'] = $doc_res['bank_name'];
                    $final_results[$doc_res['id']]['agent'] = $doc_res['agent'];
                    $final_results[$doc_res['id']]['agent_name'] = $doc_res['agent_name'];
                    $final_results[$doc_res['id']]['period'] = $doc_res['period'];
                    $final_results[$doc_res['id']]['rating_type'] = $doc_res['rating_type'];
                    $final_results[$doc_res['id']]['rating_type_name'] = $doc_res['rating_type_name'];
                    $final_results[$doc_res['id']]['object_types'] = array();
                    $final_results[$doc_res['id']]['invoices'] = array();
                    $final_results[$doc_res['id']]['tags'] = '';
                    $final_results[$doc_res['id']]['rowspan'] = 1;

                    // prepare the contacts field
                    $contact_types = array('address', 'gsm', 'phone', 'email');
                    $contacts = array();
                    foreach ($contact_types as $ct) {
                        $contact_content = $doc_res[$ct];
                        $contact_content = trim($contact_content);
                        if (empty($contact_content)) {
                            continue;
                        }
                        if ($ct != 'address') {
                            // parse the contact data
                            $contact_rows = explode("\n", $contact_content);
                            $cont = array();
                            foreach ($contact_rows as $idx => $contact_data) {
                                if (strpos($contact_data, '|') !== false) {
                                    list($contact, $test) = explode("|", $contact_data, 2);
                                    $cont[$idx] = $contact;
                                } else {
                                    $cont[$idx] = $contact_data;
                                }
                            }
                            if (!empty($cont)) {
                                $contact_content = implode(",", $cont);
                            }
                        }
                        if (!empty($contact_content)) {
                            $contacts[] = '<i>' . $registry['translater']->translate('reports_contact_' . $ct) . ':</i> ' . htmlspecialchars($contact_content);
                        }
                    }
                    if ($registry['action'] == 'export') {
                        $final_results[$doc_res['id']]['customer_contact'] = implode(", ", preg_replace('#\n|\r|\r\n#', ' ', $contacts));
                    } else {
                        $final_results[$doc_res['id']]['customer_contact'] = implode("\n", $contacts);
                    }
                    $final_results[$doc_res['id']]['customer_contact'] = implode(($registry['action'] == 'export' ? ", " : "\n"), $contacts);
                }

                $bb_data = unserialize($doc_res['bb_data']);
                if (!empty($bb_data)) {
                    foreach ($bb_data as $key_var => $var_value) {
                        if (in_array($key_var, $object_type_var_names) && !array_key_exists($var_value, $final_results[$doc_res['id']]['object_types']) && !empty($var_value)) {
                            $final_results[$doc_res['id']]['object_types'][$var_value] = '';
                            if (!in_array($var_value, $object_ids)) {
                                $object_ids[] = $var_value;
                            }
                            break;
                        }
                    }
                }

                // check roles
                $type_role = '';
                if ($doc_res['role_id'] == ROLE_MADE_BY) {
                    $type_role = 'role_make_report';
                } else if ($doc_res['role_id'] == ROLE_OBSERVED) {
                    $type_role = 'role_observed';
                } else if ($doc_res['role_id'] == ROLE_RATED) {
                    $type_role = 'role_check_rating';
                } else if ($doc_res['role_id'] == ROLE_SIGNED) {
                    $type_role = 'role_sign_report';
                }

                if ($type_role) {
                    if (!array_key_exists($doc_res['role_user'], $final_results[$doc_res['id']][$type_role])) {
                        $final_results[$doc_res['id']][$type_role][$doc_res['role_user']] = $doc_res['role_user_name'];
                    }
                }
            }

            // COMPLETE THE OBJECTS DATA
            if (!empty($object_ids)) {
                $sql_object_names = 'SELECT ni18n.parent_id, ni18n.name ' . "\n" .
                                    'FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                    'WHERE ni18n.parent_id IN ("' . implode('","', $object_ids) . '") AND ni18n.lang="' . $registry['lang'] . '"' . "\n";
                $object_ids = $registry['db']->GetAssoc($sql_object_names);
            }

            foreach ($final_results as $fr_key => $fr) {
                $unset = false;
                if (!empty($filters['employee']) || !empty($filters['revised_by'])) {
                    // CHECK THE FILTERS FOR RATED AND MADE REPORT
                    if (!empty($filters['employee']) && !array_key_exists($filters['employee'], $fr['role_make_report'])) {
                        $unset = true;
                    }

                    if (!$unset && !empty($filters['revised_by']) && !array_key_exists($filters['revised_by'], $fr['role_check_rating'])) {
                        $unset = true;
                    }
                }

                if ($unset) {
                    unset($final_results[$fr_key]);
                } else {
                    foreach ($fr['object_types'] as $obj_id => $obj_name) {
                        if (isset($object_ids[$obj_id])) {
                            $final_results[$fr_key]['object_types'][$obj_id] = $object_ids[$obj_id];
                        } else {
                            unset($final_results[$fr_key]['object_types'][$obj_id]);
                        }
                    }
                }
            }

            $document_ids_for_tags = array();
            $invoices_data = array();

            if (!empty($final_results)) {
                // GET TAGS
                $sql_tags_docs = 'SELECT t1.model_id, CONCAT(ti18n_month.name, ".", ti18n_year.name)' . "\n" .
                                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS t1' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n_month' . "\n" .
                                 '  ON (ti18n_month.parent_id=t1.tag_id AND ti18n_month.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t2' . "\n" .
                                 '  ON (t2.model=t1.model AND t2.model_id=t1.model_id AND t2.tag_id IN (' . DOCUMENT_YEAR_TAGS . '))' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n_year' . "\n" .
                                 '  ON (ti18n_year.parent_id=t2.tag_id AND ti18n_year.lang="' . $registry['lang'] . '")' . "\n" .
                                 'WHERE t1.model=\'Document\'' . "\n" .
                                 '  AND t1.model_id IN (' . implode(',', array_keys($final_results)) . ')' . "\n" .
                                 '  AND t1.tag_id IN (' . DOCUMENT_MONTH_TAGS . ')' . "\n";
                $document_ids_for_tags = $registry['db']->GetAssoc($sql_tags_docs);

                // GET THE DATA FROM THE INVOICES
                $invoices_data = array();
                $invoices_sql = 'SELECT d.id, d.custom_num as num, d.date, gt2.price as value_without_vat, gt2.subtotal_with_vat as value_with_vat, gt2.article_id as report_id' . "\n" .
                                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                'INNER JOIN ' . DB_TABLE_GT2_DETAILS. ' AS gt2' . "\n" .
                                '  ON (gt2.model="Document" AND gt2.model_id=d.id AND gt2.article_id IN (' . implode(',', array_keys($final_results)) . '))' . "\n" .
                                'WHERE d.deleted_by=0 AND d.active=1 AND d.type="' . DOCUMENT_INVOICE . '"' . "\n";
                $invoices_data = $registry['db']->GetAll($invoices_sql);
            }

            if (!empty($document_ids_for_tags)) {
                foreach ($final_results as $key => $fr) {
                    if (isset($document_ids_for_tags[$fr['id']])) {
                        $final_results[$key]['tags'] = $document_ids_for_tags[$fr['id']];
                    }
                }
            }

            foreach ($invoices_data as $inv_data) {
                if (isset($final_results[$inv_data['report_id']])) {
                    $final_results[$inv_data['report_id']]['invoices'][] = $inv_data;
                    $final_results[$inv_data['report_id']]['rowspan'] = count($final_results[$inv_data['report_id']]['invoices']);
                }
            }
            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }

?>
