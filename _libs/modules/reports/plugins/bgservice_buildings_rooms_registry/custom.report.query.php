<?php
    Class Bgservice_Buildings_Rooms_Registry Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();

// nom_type_building := 11
// nom_type_room := 12
// nom_type_working_place := 13

// building_city := build_town
// building_address := build_address
// building_room_id := room_id

// room_working_place := work_place_id
// room_employee := empl_id

            $buldings_vars = array(BUILDING_CITY, BUILDING_ADDRESS, BUILDING_ROOM_ID);
            $rooms_vars = array(ROOM_WORKING_PLACE, ROOM_EMPLOYEE);

            //sql to take the ids of the needed additional vars
            $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE (fm.model="Nomenclature" AND fm.model_type=' . NOM_TYPE_BUILDING . ' AND fm.name IN ("' . implode('","', $buldings_vars) . '")) OR (fm.model="Nomenclature" AND fm.model_type=' . NOM_TYPE_ROOM . ' AND fm.name IN ("' . implode('","', $rooms_vars) . '"))';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $building_city_id = '';
            $building_address_id = '';
            $building_room_id_id = '';
            $room_working_place_id = '';
            $room_employee_id = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == BUILDING_CITY) {
                    $building_city_id = $vars['id'];
                } else if ($vars['name'] == BUILDING_ADDRESS) {
                    $building_address_id = $vars['id'];
                } else if ($vars['name'] == BUILDING_ROOM_ID) {
                    $building_room_id_id = $vars['id'];
                } else if ($vars['name'] == ROOM_WORKING_PLACE) {
                    $room_working_place_id = $vars['id'];
                } else if ($vars['name'] == ROOM_EMPLOYEE) {
                    $room_employee_id = $vars['id'];
                }
            }

            // get all the buildings
            $sql_bulidings = array();

            $sql_bulidings['select'] = 'SELECT nom.id AS id, nomi18n.name, n_cstm_city.value as city, n_cstm_address.value as address, ' . "\n" .
                                       '  nom_r.id as room_id, nom_r.code as room_code, nom_r_i18n.name as room_name, ' . "\n" .
                                       '  nom_wp.id as working_place, nom_wp_i18n.name as working_place_name, ' . "\n" .
                                       '  c.id as employee, CONCAT(ci18n_name.name, " ", ci18n_name.lastname) as employee_name' . "\n";
            $sql_bulidings['from']   = 'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                       '  ON (nom.id=nomi18n.parent_id AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_city' . "\n" .
                                       '  ON (n_cstm_city.model_id=nom.id AND n_cstm_city.var_id="' . $building_city_id . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_address' . "\n" .
                                       '  ON (n_cstm_address.model_id=nom.id AND n_cstm_address.var_id="' . $building_address_id . '")' . "\n";

            // if the filter for rooms is selected, only the selected rooms are going to be taken
            if (!empty($filters['room'])) {
                $sql_bulidings['from'] .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_room' . "\n" .
                                          '  ON (n_cstm_room.model_id=nom.id AND n_cstm_room.var_id="' . $building_room_id_id . '" AND n_cstm_room.value="' . $filters['room'] . '")' . "\n";
            } else {
                $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_room' . "\n" .
                                          '  ON (n_cstm_room.model_id=nom.id AND n_cstm_room.var_id="' . $building_room_id_id . '")' . "\n";
            }

            $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom_r' . "\n" .
                                      '  ON (nom_r.id=n_cstm_room.value AND nom_r.deleted_by=0 AND nom_r.active=1 AND nom_r.type="' . NOM_TYPE_ROOM . '")' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_r_i18n' . "\n" .
                                      '  ON (nom_r.id=nom_r_i18n.parent_id AND nom_r_i18n.lang="' . $model_lang . '")' . "\n";

            // if the filter for working_places is selected, only the selected working_places are going to be taken
            if (!empty($filters['working_place'])) {
                $sql_bulidings['from'] .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_wp' . "\n" .
                                          '  ON (n_cstm_wp.model_id=nom_r.id AND n_cstm_wp.var_id="' . $room_working_place_id . '" AND n_cstm_wp.value="' . $filters['working_place'] . '")' . "\n";
            } else {
                $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_wp' . "\n" .
                                          '  ON (n_cstm_wp.model_id=nom_r.id AND n_cstm_wp.var_id="' . $room_working_place_id . '")' . "\n";
            }

            $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom_wp' . "\n" .
                                      '  ON (nom_wp.id=n_cstm_wp.value AND nom_wp.deleted_by=0 AND nom_wp.active=1 AND nom_wp.type="' . NOM_TYPE_WORKING_PLACE . '")' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nom_wp_i18n' . "\n" .
                                      '  ON (nom_wp.id=nom_wp_i18n.parent_id AND nom_wp_i18n.lang="' . $model_lang . '")' . "\n";

            // if the filter for employees is selected, only the selected employees are going to be taken
            if (!empty($filters['employee'])) {
                $sql_bulidings['from'] .= 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_emp' . "\n" .
                                          '  ON (n_cstm_emp.model_id=nom_r.id AND n_cstm_emp.var_id="' . $room_employee_id . '" AND n_cstm_emp.value="' . $filters['employee'] . '" AND n_cstm_emp.num=n_cstm_wp.num)' . "\n";
            } else {
                $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_emp' . "\n" .
                                          '  ON (n_cstm_emp.model_id=nom_r.id AND n_cstm_emp.var_id="' . $room_employee_id . '" AND n_cstm_emp.num=n_cstm_wp.num)' . "\n";
            }

            $sql_bulidings['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                      '  ON c.id=n_cstm_emp.value AND c.active=1 AND c.deleted_by=0' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_name' . "\n" .
                                      '  ON (ci18n_name.parent_id=c.id AND ci18n_name.lang="' . $model_lang . '")' . "\n";

            $where = array();
            $where[] = 'nom.deleted_by=0';
            $where[] = 'nom.active=1';
            $where[] = 'nom.type="' . NOM_TYPE_BUILDING . '"';
            if (!empty($filters['building'])) {
                $where[] = 'nom.id="' . $filters['building'] . '"';
            }
            $sql_bulidings['where'] = 'WHERE ' . implode(' AND ', $where);
            $sql_bulidings['order'] = 'ORDER BY nomi18n.name ASC' . "\n";

            $query_buildings = implode("\n", $sql_bulidings);
            $records_buildings = $registry['db']->GetAll($query_buildings);

            foreach ($records_buildings as $rec_build) {
                if (!isset($final_results[$rec_build['id']])) {
                    $final_results[$rec_build['id']] = array(
                        'id'       => $rec_build['id'],
                        'name'     => $rec_build['name'],
                        'city'     => $rec_build['city'],
                        'address'  => $rec_build['address'],
                        'rooms'    => array(),
                        'rowspan'  => 0
                    );
                }

                if ($rec_build['room_id'] && !isset($final_results[$rec_build['id']]['rooms'][$rec_build['room_id']])) {
                    $final_results[$rec_build['id']]['rooms'][$rec_build['room_id']] = array(
                        'id'             => $rec_build['room_id'],
                        'name'           => $rec_build['room_name'],
                        'code'           => $rec_build['room_code'],
                        'working_places' => array(),
                        'rowspan'        => 0
                    );
                }

                if ($rec_build['working_place']) {
                    $final_results[$rec_build['id']]['rooms'][$rec_build['room_id']]['working_places'][] = array(
                        'id'             => $rec_build['working_place'],
                        'name'           => $rec_build['working_place_name'],
                        'employee'       => $rec_build['employee'],
                        'employee_name'  => $rec_build['employee_name']
                    );
                }
            }

            // foreach to determine the rowspan of the cells
            foreach ($final_results as $k_fr => $fr) {
                $lest_rowspan_build = (count($fr['rooms']) ? count($fr['rooms']) : 1);
                $calculated_rowspan = 0;
                foreach ($fr['rooms'] as $r_id => $room) {
                    $final_results[$k_fr]['rooms'][$r_id]['rowspan'] = (count($room['working_places']) ? count($room['working_places']) : 1);
                    $calculated_rowspan += $final_results[$k_fr]['rooms'][$r_id]['rowspan'];
                }

                $final_results[$k_fr]['rowspan'] = ($calculated_rowspan<$lest_rowspan_build ? $lest_rowspan_build : $calculated_rowspan);
            }
// trace ($final_results);
            // query if paginate is needed
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                //no pagination required return only the models
                $results = $final_results;
            }

            return $results;
        }
    }
?>