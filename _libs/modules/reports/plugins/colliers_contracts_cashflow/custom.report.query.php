<?php
    Class Colliers_Contracts_Cashflow Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            set_time_limit(300);

            if (!defined('CONTRACT_RENT_ID')) {
                define('CONTRACT_RENT_ID', 1);
            }
            if (!defined('BASE_RENT_PRICE')) {
                define('BASE_RENT_PRICE', 1034);
            }
            if (!defined('BASE_RENT_PRICE_OP')) {
                define('BASE_RENT_PRICE_OP', 1038);
            }
            if (!defined('TAX_SERVICE')) {
                define('TAX_SERVICE', 1035);
            }

            ini_set("memory_limit", '2048M');
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array(
                'rents'             => array(),
                'services'          => array(),
                'rents_totals'      => array(),
                'services_totals'   => array(),
                'full_totals'       => array(),
            );
            if (!empty($filters['from_date']) && !empty($filters['to_date'])) {
                // prepare months options
                $months_options = array();
                $months_labels = array();

                $date_from_sec = strtotime($filters['from_date']);
                $date_to_sec = strtotime($filters['to_date']);
                $month_date_from = General::strftime('%m', $date_from_sec);
                $year_date_from = General::strftime('%Y', $date_from_sec);
                $start_period_date = mktime(0, 0, 0, $month_date_from, 1, $year_date_from);

                $month_date_to = General::strftime('%m', $date_to_sec);
                $year_date_to = General::strftime('%Y', $date_to_sec);
                $first_month_date_to_sec = mktime(0, 0, 0, $month_date_to, 1, $year_date_to);
                $end_period_date = strtotime('+1 month', $first_month_date_to_sec);

                for ($j=$start_period_date; $j<$end_period_date; $j=strtotime('+1 month', $j)) {
                    $month_key = General::strftime('%Y-%m', $j);
                    $months_options[$month_key] = 0;
                    $months_labels[$month_key] = General::strftime('%B %Y', $j, true);
                }

                $extra_months_options = array();

                $final_results['rents_totals'] = $months_options;
                $final_results['rents_totals']['total'] = 0;
                $final_results['services_totals'] = $months_options;
                $final_results['services_totals']['total'] = 0;
                $final_results['full_totals'] = $months_options;
                $final_results['full_totals']['optimistic_forecast'] = 0;
                $final_results['full_totals']['pesimistic_forecast'] = 0;
                $final_results['full_totals']['total'] = 0;

                if (!empty($filters['show'])) {
                    if (!in_array('rent', $filters['show'])) {
                        unset($final_results['rents_totals']);
                        unset($final_results['rents']);
                    }
                    if (!in_array('tax_service', $filters['show'])) {
                        unset($final_results['services_totals']);
                        unset($final_results['services']);
                    }
                }

                $from_date = $filters['from_date'];
                $to_date = General::strftime('%Y-%m-%d', strtotime('+1 day', $date_to_sec));

                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $contract_params = array(
                    'date_from'         => $from_date,
                    'date_to'           => $to_date,
                    'recurrent_only'    => true,
                    'do_not_cut_period' => true,
                    'get_timeline_only' => true
                );

                // prepare tenants filter
                if (!empty($filters['renters'])) {
                    $searched_tm = array();
                    foreach ($filters['renters'] as $renter) {
                        if ($renter && !in_array($renter, $searched_tm)) {
                            $searched_tm[] = $renter;
                        }
                    }
                    if (!empty($searched_tm)) {
                        $contract_params['trademarks_ids'] = $searched_tm;
                    }
                }
                // get all the invoices that have to issued in this period
                $contracts_info = Contracts::getAmountDue($registry, $contract_params);

                // search information for contracts
                $searched_contracts_ids = array();
                foreach ($contracts_info as $parent_contract => $con_inf) {
                    foreach ($con_inf['timeline'] as $t => $act_contr) {
                        if ($act_contr) {
                            $searched_contracts_ids[] = $act_contr;
                        }
                    }
                }

                // take the customers and trademarks for the required contracts
                $contracts_customer_tm_information = array();
                if (!empty($searched_contracts_ids)) {
                    $query_contracts = 'SELECT con.id as id, con.customer as customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, con.trademark as trademark, ni18n.name as trademark_name ' . "\n" .
                                       'FROM ' . DB_TABLE_CONTRACTS . ' AS con' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                       '  ON (con.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                       '  ON (con.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                       'WHERE con.active=1 AND con.deleted_by=0 AND con.id IN (' . implode(',', $searched_contracts_ids) . ')';

                    $records_contracts_customers = $registry['db']->GetAll($query_contracts);

                    foreach ($records_contracts_customers as $rcc) {
                        if (!isset($contracts_customer_tm_information[$rcc['id']])) {
                            $contracts_customer_tm_information[$rcc['id']] = array(
                                'id'            => $rcc['id'],
                                'customer'      => $rcc['customer'],
                                'customer_name' => $rcc['customer_name'],
                                'trademark'     => $rcc['trademark'],
                                'trademark_name'=> $rcc['trademark_name']
                            );
                        }
                    }
                }

                // array for included areas
                $commercial_areas_ids = array();

                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';

                $currency_multipliers = array();
                $currency_multipliers[$filters['currency'] . '->' . $filters['currency']] = 1;

                foreach ($contracts_info as $parent_contract => $con_inf) {
                    $timeline_dates = array_keys($con_inf);
                    foreach ($con_inf['timeline'] as $date_start => $active_contract) {
                        if ($active_contract) {
                            // gets the active contract info
                            $active_contract_info = $contracts_customer_tm_information[$active_contract];
                            $current_customer_id = $active_contract_info['customer'];
                            $current_trademark_id = $active_contract_info['trademark'];

                            $key_timeline = array_search($date_start, $timeline_dates);
                            $date_end_contract = $timeline_dates[$key_timeline + 1];

                            // find all the invoice templates for this contract
                            $filters_templates = array('where' => array('fit.contract_id="' . $active_contract . '"',
                                                                        'deleted_by=0'
                                                                  ),
                                                       'sanitize' => true);
                            $records_invoices_templates = Finance_Invoices_Templates::search($registry, $filters_templates);

                            foreach ($records_invoices_templates as $rit) {
                                // if the recurrence period is 'trimester' the recurrence count and
                                //  recurrence period are changed according to trimester specifics
                                if ($rit->get('recurrence_period') == 'trimester') {
                                    $rit->set('recurrence_period_recalculated', 'month', true);
                                    $rit->set('recurrence_count_recalculated', ($rit->get('recurrence_count') * 3), true);
                                } else {
                                    $rit->set('recurrence_period_recalculated', $rit->get('recurrence_period'), true);
                                    $rit->set('recurrence_count_recalculated', $rit->get('recurrence_count'), true);
                                }

                                $current_period_start = '';
                                $current_period_end = '';
                                $current_period_issue_date = $rit->get('next_issue_date');

                                // check if this is the first invoice that should be issued
                                if ($rit->get('status') == 'opened') {
                                    $current_period_start = $rit->get('periods_start');

                                    if ($rit->get('first_period_invoice') == 'full') {
                                        $current_period_end = General::strftime('%Y-%m-%d', strtotime('+' . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($current_period_start)));
                                    } else if (preg_match('#partial#', $rit->get('first_period_invoice'))) {
                                        $index = 1;
                                        $partial_days = ' + 0 day';
                                        //partial period will be invoiced
                                        if ($rit->get('recurrence_period') == 'week') {
                                            //get the number of week day
                                            $day = intval(General::strftime('%u', strtotime($current_period_start)));
                                            if ($day != 1) {
                                                //we have to finish this partial week
                                                $index = (8 - $day) / 7;
                                                //calculate number of days to the end of the period
                                                $partial_days = ' +' . (8 - $day) . ' day';
                                            }
                                        } elseif ($rit->get('recurrence_period') == 'month') {
                                            if (!preg_match('#\-01$#', $current_period_start)) {
                                                //we have to finish this partial month
                                                //get the number of day in the month
                                                $day = intval(General::strftime('%e', strtotime($current_period_start)));
                                                //get last day of the month
                                                $total_days = preg_replace('#\-(\d{2})$#', '-01', $current_period_start);
                                                $total_days = intval(General::strftime('%e', strtotime('+1 month -1 day', strtotime($total_days))));
                                                if ($rit->get('single_period_length') > 0) {
                                                    $index = ($total_days + 1 - $day) / $rit->get('single_period_length');
                                                } else {
                                                    $index = ($total_days + 1 - $day) / $total_days;
                                                }
                                                //calculate number of days to the end of the period
                                                $partial_days = ' +' . ($total_days + 1 - $day) . ' day';
                                            }
                                        } elseif ($rit->get('recurrence_period') == 'trimester') {
                                            if (!preg_match('#\-(01|04|07|10)\-01$#', $current_period_start)) {
                                                //we have to finish this partial trimester
                                                //get the number of the month
                                                $month = intval(date('n', strtotime($current_period_start)));
                                                $year = intval(General::strftime('%Y', strtotime($current_period_start)));
                                                //get the trimester start and end dates
                                                if ($month >= 10) {
                                                    $tm_start = strtotime($year . '-10-01');
                                                    $tm_end = strtotime($year . '-12-31');
                                                } elseif ($month >= 7) {
                                                    $tm_start = strtotime($year . '-07-01');
                                                    $tm_end = strtotime($year . '-09-30');
                                                } elseif ($month >= 4) {
                                                    $tm_start = strtotime($year . '-04-01');
                                                    $tm_end = strtotime($year . '-06-30');
                                                } elseif ($month >= 1) {
                                                    $tm_start = strtotime($year . '-01-01');
                                                    $tm_end = strtotime($year . '-03-31');
                                                }

                                                //calculate the days passed from the beginning of the trimester
                                                $days_passed = round(strtotime($current_period_start) - $tm_start) / 3600 / 24;
                                                //calculate trimester length(in days)
                                                $total_days = round(($tm_end - $tm_start) / 3600 / 24) + 1;
                                                if ($rit->get('single_period_length') > 0) {
                                                    $index = ($total_days + 1 - $days_passed) / $rit->get('single_period_length');
                                                } else {
                                                    $index = ($total_days + 1 - $days_passed) / $total_days;
                                                }
                                                //calculate number of days to the end of the period
                                                $partial_days = ' +' . ($total_days + 1 - $days_passed) . ' day';
                                            }
                                        } elseif ($rit->get('recurrence_period') == 'year') {
                                            $year = intval(General::strftime('%Y', strtotime($current_period_start)));
                                            //get days passed from the start of the year
                                            $days_passed = date('z', strtotime($current_period_start));
                                            //get total days in the year (365/366)
                                            $total_days = date('z', strtotime($year . '-12-31')) + 1;
                                            if ($rit->get('single_period_length') > 0) {
                                                $index = ($total_days + 1 - $days_passed) / $rit->get('single_period_length');
                                            } else {
                                                $index = ($total_days + 1 - $days_passed) / $total_days;
                                            }
                                            //calculate number of days to the end of the period
                                            $partial_days = ' +' . ($total_days + 1 - $days_passed) . ' day';
                                        }

                                        if ($rit->get('recurrence_period') != 'day' && $index < 1) {
                                            $current_period_end = General::strftime('%Y-%m-%d', strtotime($partial_days, strtotime($current_period_start)));
                                        }

                                        if (!$current_period_end) {
                                            $current_period_end = General::strftime('%Y-%m-%d', strtotime('+' . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($current_period_start)));
                                        }

                                        if (preg_match('#full#', $rit->get('first_period_invoice')) && $index<0) {
                                            $current_period_end = General::strftime('%Y-%m-%d', strtotime('+' . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($current_period_end)));
                                        }
                                    }
                                } else {
                                    if ($rit->get('issue_date_point') == 'start') {
                                        $current_period_var = 'current_period_start';
                                        $alternative_period_var = 'current_period_end';
                                    } else {
                                        $current_period_var = 'current_period_end';
                                        $alternative_period_var = 'current_period_start';
                                    }
                                    if ($rit->get('issue_date_direction') == 'before') {
                                        $period_calculation_sign = '-';
                                    } else {
                                        $period_calculation_sign = '+';
                                    }

                                    if ($rit->get('issue_date_count')) {
                                        if ($rit->get('issue_date_period_type') == 'calendar') {
                                            // calculate calendar days
                                            $$current_period_var = General::strftime('%Y-%m-%d', strtotime(($rit->get('issue_date_period_type')=='before'?'+':'-') . $rit->get('issue_date_count') . ' ' . $rit->get('issue_date_period'), strtotime($current_period_issue_date)));
                                        } else {
                                            // calculate working days
                                            $$current_period_var = Calendars_Calendar::calcDateOnWorkingDays($registry, $current_period_issue_date, $rit->get('issue_date_count'), ($rit->get('issue_date_direction')=='before'?'after':'before'));
                                        }
                                    } else {
                                        $$current_period_var = $current_period_issue_date;
                                    }

                                    $$alternative_period_var = General::strftime('%Y-%m-%d', strtotime($period_calculation_sign . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($$current_period_var)));
                                }

                                // calculate currency multiplier
                                $currency_key = $rit->get('currency') . '->' . $filters['currency'];
                                if (!isset($currency_multipliers[$currency_key])) {
                                    $currency_multipliers[$currency_key] = Finance_Currencies::getRate($registry, $rit->get('currency'), $filters['currency']);
                                }
                                $currency_multiplier = $currency_multipliers[$currency_key];

                                $count_iterations = 0;

                                // iterate through invoiced periods until the next contract enured or
                                //until the issue invoice date is after the filter date
                                while ($current_period_issue_date < $date_end_contract && $current_period_issue_date < $to_date) {
                                    // code to prevent script to enter in endless recursion
                                    $count_iterations++;
                                    if ($count_iterations>300) {
                                        trace ($rit, 'PROBLEM INVOICE');
                                        trace ($current_period_start, 'CURRENT_PERIOD_START');
                                        trace ($current_period_end, 'CURRENT_PERIOD_END');
                                        trace ($current_period_issue_date, 'CURRENT_PERIOD_ISSUE_DATE');
                                        die();
                                    }

                                    // check if the issued period date is in the searched period
                                    if ($from_date<=$current_period_issue_date && $current_period_issue_date<$to_date) {
                                        if ($filters['show_sums_acording_to'] == 'payment') {
                                            if ($rit->get('date_of_payment_period_type') == 'calendar') {
                                                // calculate calendar days
                                                $date_for_sum = General::strftime('%Y-%m-%d', strtotime('+' . $rit->get('date_of_payment_count') . ' days', strtotime($current_period_issue_date)));
                                            } else {
                                                // calculate working days
                                                $date_for_sum = Calendars_Calendar::calcDateOnWorkingDays($registry, $current_period_issue_date, $rit->get('date_of_payment_count'), 'after');
                                            }
                                        } else {
                                            $date_for_sum = $current_period_issue_date;
                                        }

                                        $template_params = array(
                                            'get_amount_due' => true,
                                            'date_from'      => $current_period_start,
                                            'date_to'        => General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($current_period_end)))
                                        );

                                        // calculates the amounts for the required period
                                        $rit->getGT2Vars();
                                        //TODO: check this call as the function has been changed
                                        list($values) = $rit->calculateRecurrence($template_params);

                                        foreach ($values as $vals) {
                                            $included_in_table = '';
                                            $included_in_table_total = '';
                                            $all_total_var = '';
                                            if ($vals['article_id'] == BASE_RENT_PRICE || $vals['article_id'] == BASE_RENT_PRICE_OP) {
                                                $included_in_table = 'rents';
                                                $included_in_total_table = 'rents_totals';
                                            } else if ($vals['article_id'] == TAX_SERVICE) {
                                                $included_in_table = 'services';
                                                $included_in_total_table = 'services_totals';
                                            }

                                            // defines which period this invoice will be included in
                                            $month_key = General::strftime('%Y-%m', $date_for_sum);

                                            if ($included_in_table && $included_in_total_table && isset($final_results[$included_in_table])) {
                                                $area_id = $vals['free_field1'];

                                                $array_key = $current_customer_id . '_' . $current_trademark_id . '_' . $area_id;
                                                if (!isset($final_results[$included_in_table][$array_key])) {
                                                    $final_results[$included_in_table][$array_key] = array(
                                                        'customer_id'   => $active_contract_info['customer'],
                                                        'customer_name' => $active_contract_info['customer_name'],
                                                        'trademark_id'  => $active_contract_info['trademark'],
                                                        'trademark_name'=> $active_contract_info['trademark_name'],
                                                        'area_name'     => '',
                                                        'area_id'       => $area_id,
                                                        'periods'       => array(),
                                                        'months'        => $months_options,
                                                        'total'         => 0
                                                    );
                                                    asort($final_results[$included_in_table][$array_key]);

                                                    if (!in_array($area_id, $commercial_areas_ids)) {
                                                        $commercial_areas_ids[] = $area_id;
                                                    }
                                                }

                                                // period key
                                                $period_key = $rit->get('recurrence_count') . '_' . $rit->get('recurrence_period');
                                                if (!isset($final_results[$included_in_table][$array_key]['periods'][$period_key])) {
                                                    $lang_var = 'reports_recurrence_period_' . $rit->get('recurrence_period');
                                                    $final_results[$included_in_table][$array_key]['periods'][$period_key] = $rit->get('recurrence_count') . ' ' . $registry['translater']->translate($lang_var);
                                                }

                                                $calculated_sum = sprintf("%01.2f", ((($vals['price']-$vals['discount_value']) * $vals['quantity'] * 1.2) * $currency_multiplier));

                                                if (!isset($final_results[$included_in_table][$array_key]['months'][$month_key])) {
                                                    $final_results[$included_in_table][$array_key]['months'][$month_key] = 0;
                                                }

                                                if (!isset($final_results[$included_in_total_table][$month_key])) {
                                                    $final_results[$included_in_total_table][$month_key] = 0;
                                                }

                                                if (!isset($final_results['full_totals'][$month_key])) {
                                                    $final_results['full_totals'][$month_key] = 0;
                                                }

                                                if (!in_array($month_key, $extra_months_options)) {
                                                    $extra_months_options[] = $month_key;
                                                }

                                                $final_results[$included_in_table][$array_key]['months'][$month_key] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['months'][$month_key] + $calculated_sum));
                                                $final_results[$included_in_table][$array_key]['total'] = sprintf("%01.2f", ($final_results[$included_in_table][$array_key]['total'] + $calculated_sum));
                                                $final_results[$included_in_total_table][$month_key] = sprintf("%01.2f", ($final_results[$included_in_total_table][$month_key] + $calculated_sum));
                                                $final_results[$included_in_total_table]['total'] = sprintf("%01.2f", ($final_results[$included_in_total_table]['total'] + $calculated_sum));
                                                $final_results['full_totals'][$month_key] = sprintf("%01.2f", ($final_results['full_totals'][$month_key] + $calculated_sum));
                                                $final_results['full_totals']['total'] = sprintf("%01.2f", ($final_results['full_totals']['total'] + $calculated_sum));
                                            }
                                        }
                                    }

                                    // calculates when the next period starts and when the next period ends
                                    $current_period_end = General::strftime('%Y-%m-%d', strtotime('+' . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($current_period_end)));
                                    $current_period_start = General::strftime('%Y-%m-%d', strtotime('-' . $rit->get('recurrence_count_recalculated') . ' ' . $rit->get('recurrence_period_recalculated'), strtotime($current_period_end)));

                                    if ($rit->get('issue_date_point') == 'start') {
                                        $compare_date = $current_period_start;
                                    } else {
                                        $compare_date = $current_period_end;
                                    }

                                    if ($rit->get('issue_date_period_type') == 'calendar') {
                                        // calculate calendar days
                                        $current_period_issue_date = General::strftime('%Y-%m-%d', strtotime(($rit->get('issue_date_period_type')=='before'?'-':'+') . $rit->get('issue_date_count') . ' ' . $rit->get('issue_date_period'), strtotime($compare_date)));
                                    } else {
                                        // calculate working days
                                        $current_period_issue_date = Calendars_Calendar::calcDateOnWorkingDays($registry, $compare_date, $rit->get('issue_date_count'), $rit->get('issue_date_direction'));
                                    }
                                }
                            }
                        }
                    }
                }

                $new_months_added = array();
                if (!empty($extra_months_options)) {
                    sort($extra_months_options);

                    $last_extra_month_key = $extra_months_options[count($extra_months_options)-1];
                    $last_extra_month_sec = strtotime('+1 month', strtotime($last_extra_month_key . '-01'));

                    $default_month_keys = array_keys($months_options);
                    $last_default_month_key = $default_month_keys[count($default_month_keys)-1];
                    $last_default_month_sec = strtotime('+1 month', strtotime($last_default_month_key . '-01'));

                    for ($t=$last_default_month_sec; $t<$last_extra_month_sec; $t=strtotime('+1 month', $t)) {
                        $new_month_key = General::strftime('%Y-%m', $t);
                        $new_months_added[] = $new_month_key;
                        $months_labels[$new_month_key] = General::strftime('%B %Y', $t, true);
                    }
                    ksort($months_labels);
                }

                // get the names of the required commercial areas
                $commercial_areas_info = array();
                if (!empty($commercial_areas_ids)) {
                    $query_nomenclatures = 'SELECT nom.id as id_idx, ni18n.name as name' . "\n" .
                                           'FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                           '  ON (nom.id=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n" .
                                           'WHERE nom.id IN (' . implode(',', $commercial_areas_ids) . ')';

                    $commercial_areas_info = $registry['db']->GetAssoc($query_nomenclatures);
                }

                // apply the names of the found areas
                if (isset($final_results['rents'])) {
                    foreach ($final_results['rents'] as $r_key => $rent_info) {
                        $final_results['rents'][$r_key]['area_name'] = $commercial_areas_info[$rent_info['area_id']];
                        if (!empty($new_months_added)) {
                            foreach ($new_months_added as $new_month_key) {
                                if (!array_key_exists($new_month_key, $rent_info['months'])) {
                                    $final_results['rents'][$r_key]['months'][$new_month_key] = 0;
                                }
                                if (!array_key_exists($new_month_key, $final_results['rents_totals'])) {
                                    $final_results['rents_totals'][$new_month_key] = 0;
                                }
                            }
                            ksort($final_results['rents'][$r_key]['months']);
                            ksort($final_results['rents_totals']);
                        }
                    }
                }
                if (isset($final_results['services'])) {
                    foreach ($final_results['services'] as $s_key => $service_info) {
                        if (!empty($new_months_added)) {
                            $final_results['services'][$s_key]['area_name'] = $commercial_areas_info[$service_info['area_id']];
                            foreach ($new_months_added as $new_month_key) {
                                if (!array_key_exists($new_month_key, $service_info['months'])) {
                                    $final_results['services'][$s_key]['months'][$new_month_key] = 0;
                                }
                                if (!array_key_exists($new_month_key, $final_results['services_totals'])) {
                                    $final_results['services_totals'][$new_month_key] = 0;
                                }
                            }
                            ksort($final_results['services'][$s_key]['months']);
                            ksort($final_results['services_totals']);
                        }
                    }
                }

                foreach ($new_months_added as $new_month_key) {
                    if (!array_key_exists($new_month_key, $final_results['full_totals'])) {
                        $final_results['full_totals'][$new_month_key] = 0;
                    }
                }
                if (!empty($filters['optimistic_forecast'])) {
                    $final_results['full_totals']['optimistic_forecast'] = sprintf("%01.2f", (($final_results['full_totals']['total'] * $filters['optimistic_forecast'])/100));
                }
                if (!empty($filters['pesimistic_forecast'])) {
                    $final_results['full_totals']['pesimistic_forecast'] = sprintf("%01.2f", (($final_results['full_totals']['total'] * $filters['pesimistic_forecast'])/100));
                }

                $final_results['months_labels'] = $months_labels;
                $final_results['total_columns'] = 5 + count($months_labels);

            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
