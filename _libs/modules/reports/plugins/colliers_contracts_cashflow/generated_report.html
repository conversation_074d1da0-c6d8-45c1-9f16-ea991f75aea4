<table border="0" cellpadding="0" cellspacing="10">
  {if $reports_results.rents}
    <tr>
      <td>
        <h3 style="color: #000000">{#reports_option_rent#|mb_upper}</h3>
      </td>
    </tr>
    <tr>
      <td>
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row">
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 140px;">{#reports_trademark#|escape}</div></td>
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 140px;">{#reports_customer#|escape}</div></td>
            <td class="t_border" nowrap="nowrap" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_unit#|escape}</div></td>
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_invoice_period#|escape}</div></td>
            {foreach from=$reports_results.months_labels item=month_label}
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{$month_label|escape}</div></td>
            {/foreach}
            <td nowrap="nowrap" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total#|escape}</div></td>
          </tr>
          {foreach from=$reports_results.rents item=rent name=rnt}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" style="width: 144px;">
                {if $rent.trademark_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$rent.trademark_id}">{$rent.trademark_name|escape}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="width: 144px;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$rent.customer_id}">{$rent.customer_name|escape}</a>
              </td>
              <td class="t_border" style="width: 74px; vertical-align: middle;">{$rent.area_name|escape|default:"&nbsp;"}</td>
              <td class="t_border" style="text-align: center; width: 94px; vertical-align: middle;">
                {foreach from=$rent.periods item=inv_per name=in_p}
                  {$inv_per|escape|default:"&nbsp;"}{if !$smarty.foreach.in_p.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              {foreach from=$rent.months item=month}
                <td class="t_border" style="text-align:{if $month>0}right{else}center{/if}; width: 74px; vertical-align: middle;" nowrap="nowrap">{if $month>0}{$month|string_format:"%.2f"|default:"0.00"}{else}-{/if}</td>
              {/foreach}
              <td class="hright" nowrap="nowrap" style="width: 74px; vertical-align: middle;">{$rent.total|string_format:"%.2f"|default:"0.00"}</td>
            </tr>
          {foreachelse}
            <tr>
              <td class="error" colspan="{$reports_results.total_columns}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr class="row_blue">
            <td class="t_border" colspan="4" style="width: 472px;"><strong>{#reports_total#}</strong></td>
            {foreach from=$reports_results.rents_totals item=rent_total key=key_rent_total}
              {if $key_rent_total ne 'total'}
                <td class="t_border" style="width: 80px; text-align:{if $rent_total}right{else}center{/if};"><strong>{if $rent_total}{$rent_total|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong></td>
              {/if}
            {/foreach}
            <td class="hright" style="width: 80px;"><strong>{$reports_results.rents_totals.total|string_format:"%.2f"|default:"0.00"}</strong></td>
          </tr>
          <tr>
            <td class="t_footer" colspan="{$reports_results.total_columns}"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>&nbsp;</td>
    </tr>
  {/if}

  {if $reports_results.services}
    <tr>
      <td>
        <h3 style="color: #000000">{#reports_option_tax_service#|mb_upper}</h3>
      </td>
    </tr>
    <tr>
      <td>
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row">
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 140px;">{#reports_trademark#|escape}</div></td>
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 140px;">{#reports_customer#|escape}</div></td>
            <td class="t_border" nowrap="nowrap" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_unit#|escape}</div></td>
            <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 90px;">{#reports_invoice_period#|escape}</div></td>
            {foreach from=$reports_results.months_labels item=month_label}
              <td class="t_border" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{$month_label|escape}</div></td>
            {/foreach}
            <td nowrap="nowrap" style="text-align: center; vertical-align: middle;"><div style="width: 70px;">{#reports_total#|escape}</div></td>
          </tr>
          {foreach from=$reports_results.services item=service name=srv}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" style="width: 144px;">
                {if $service.trademark_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$service.trademark_id}">{$service.trademark_name|escape}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="width: 144px;">
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$service.customer_id}">{$service.customer_name|escape}</a>
              </td>
              <td class="t_border" style="width: 74px; vertical-align: middle;">{$service.area_name|escape|default:"&nbsp;"}</td>
              <td class="t_border" style="text-align: center; width: 94px; vertical-align: middle;">
                {foreach from=$service.periods item=inv_per name=in_p}
                  {$inv_per|escape|default:"&nbsp;"}{if !$smarty.foreach.in_p.last}<br />{/if}
                {foreachelse}
                  &nbsp;
                {/foreach}
              </td>
              {foreach from=$service.months item=month}
                <td class="t_border" style="text-align:{if $month>0}right{else}center{/if}; width: 74px; vertical-align: middle;" nowrap="nowrap">{if $month>0}{$month|string_format:"%.2f"|default:"0.00"}{else}-{/if}</td>
              {/foreach}
              <td class="hright" nowrap="nowrap" style="width: 74px; vertical-align: middle;">{$service.total|string_format:"%.2f"|default:"0.00"}</td>
            </tr>
          {foreachelse}
            <tr>
              <td class="error" colspan="{$reports_results.total_columns}">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr class="row_blue">
            <td class="t_border" colspan="4" style="width: 472px;"><strong>{#reports_total#}</strong></td>
            {foreach from=$reports_results.services_totals item=service_total key=key_service_total}
              {if $key_service_total ne 'total'}
                <td class="t_border" style="width: 80px; text-align:{if $service_total}right{else}center{/if};"><strong>{if $service_total}{$service_total|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong></td>
              {/if}
            {/foreach}
            <td class="hright" style="width: 80px;"><strong>{$reports_results.services_totals.total|string_format:"%.2f"|default:"0.00"}</strong></td>
          </tr>
          <tr>
            <td class="t_footer" colspan="{$reports_results.total_columns}"></td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td>&nbsp;</td>
    </tr>
  {/if}
  <tr>
    <td>
      <table cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row">
          <td class="t_border" nowrap="nowrap" style="text-align: center; background-color:#FFDE9F;border-bottom:0;"><div style="width:230px;"><strong>{#reports_pesimistic#|escape}: {if $reports_results.full_totals.pesimistic_forecast>0}{$reports_results.full_totals.pesimistic_forecast|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong></div></td>
          <td class="t_border" nowrap="nowrap" style="text-align: center; background-color:#B0DD85;border-bottom:0;"><div style="width:231px;"><strong>{#reports_optimistic#|escape}: {if $reports_results.full_totals.optimistic_forecast>0}{$reports_results.full_totals.optimistic_forecast|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong></div></td>
          {foreach from=$reports_results.months_labels item=month_label key=month_key}
            <td class="t_border" nowrap="nowrap" style="background-color:#FFFDBB;border-bottom:0;text-align:{if $reports_results.full_totals.$month_key}right{else}center{/if};"><div style="width:80px;"><strong>{if $reports_results.full_totals.$month_key}{$reports_results.full_totals.$month_key|string_format:"%.2f"|default:"0.00"}{else}-{/if}</strong></div></td>
          {/foreach}
          <td class="hright" nowrap="nowrap" style="background-color:#FFFDBB; border-bottom:0;"><div style="width:80px;"><strong>{$reports_results.full_totals.total|string_format:"%.2f"|default:"0.00"}</strong></div></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
