<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        {if $reports_results.permission}
            <th rowspan="2">{#reports_table_1_index#|escape}</th>
            <th rowspan="2">{#reports_table_1_partner#|escape}</th>
            <th rowspan="2">{#reports_table_1_region#|escape}</th>
            <th rowspan="2">{#reports_table_1_sales_agent_request#|escape}</th>
            <th rowspan="2">{#reports_table_1_object#|escape}</th>
            <th rowspan="2">{#reports_table_1_object_phase#|escape}</th>
            <th rowspan="2">{#reports_table_1_current_points#|escape}</th>

            <th colspan="6">{#reports_table_1_requested_prizes#|escape}</th>

            <th colspan="5">{#reports_table_1_requested_points#|escape}</th>

            <th colspan="5">{#reports_table_1_received_prizes#|escape}</th>
        </tr>
        <tr>
          <th>{#reports_table_1_rpr_date#|escape}</th>
          <th>{#reports_table_1_rpr_prizes#|escape}</th>
          <th>{#reports_table_1_rpr_prizes_count#|escape}</th>
          <th>{#reports_table_1_rpr_points_per_prize#|escape}</th>
          <th>{#reports_table_1_rpr_total_points#|escape}</th>
          <th>{#reports_table_1_rpr_current_quantity#|escape}</th>

          <th>{#reports_table_1_rpo_date#|escape}</th>
          <th>{#reports_table_1_rpo_product#|escape}</th>
          <th>{#reports_table_1_rpo_pallets_count#|escape}</th>
          <th>{#reports_table_1_rpo_bonus_points#|escape}</th>
          <th>{#reports_table_1_rpo_total_points#|escape}</th>

          <th>{#reports_table_1_rep_date#|escape}</th>
          <th>{#reports_table_1_rep_prizes#|escape}</th>
          <th>{#reports_table_1_rep_prizes_count#|escape}</th>
          <th>{#reports_table_1_rep_points_per_prize#|escape}</th>
          <th>{#reports_table_1_rep_total_points#|escape}</th>
        {else}
            <th rowspan="2">{#reports_table_1_index#|escape}</th>
            <th rowspan="2">{#reports_table_1_partner#|escape}</th>
            <th rowspan="2">{#reports_table_1_sales_agent_request#|escape}</th>
            <th rowspan="2">{#reports_table_1_object#|escape}</th>
            <th rowspan="2">{#reports_table_1_object_phase#|escape}</th>
            <th rowspan="2">{#reports_table_1_current_points#|escape}</th>

            <th colspan="4">{#reports_table_1_requested_prizes#|escape}</th>

            <th colspan="5">{#reports_table_1_requested_points#|escape}</th>
        </tr>
        <tr>
          <th>{#reports_table_1_rpr_date#|escape}</th>
          <th>{#reports_table_1_rpr_prizes#|escape}</th>
          <th>{#reports_table_1_rpr_prizes_count#|escape}</th>
          <th>{#reports_table_1_rpr_current_quantity#|escape}</th>

          <th>{#reports_table_1_rpo_date#|escape}</th>
          <th>{#reports_table_1_rpo_product#|escape}</th>
          <th>{#reports_table_1_rpo_pallets_count#|escape}</th>
          <th>{#reports_table_1_rpo_bonus_points#|escape}</th>
          <th>{#reports_table_1_rpo_total_points#|escape}</th>
        {/if}
      </tr>
      {if $reports_results.permission}
          {assign var=current_total_points value=0}
          {assign var=current_total_prizes value=0}
          {assign var=total_requested_points value=0}
          {assign var=total_points_spend value=0}
          {foreach from=$reports_results.results.table_1 item=partner name=partners key=pk}
              {counter name=index_1 assign=index_1}
              {cycle name='table_1' values='t_odd1 t_odd2,t_even1 t_even2' assign=table_1_row_color}
              {assign var=rows_spent_rpr value=0}
              {assign var=rows_spent_rpo value=0}
              {assign var=rows_spent_repr value=0}

              {foreach from=$partner[$partner.biggest].records item=record name=records key=rk}
                  <tr>
                    {if $smarty.foreach.records.first}
                        <td rowspan="{$partner.rowspan}">{$index_1|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.partner_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.region|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.sales_agent_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.object_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.object_phase_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.bonus_all|string_format:"%.0f"|escape|default:"&nbsp;"}</td>
                        {assign var=current_total_points value=$current_total_points+$partner.bonus_all}
                    {/if}
                    {if $partner.records_rpr.records[$rk] && $partner.rowspan_rpr != 0}
                        {assign var=lrk value=$rk-1}
                        {assign var='rec_rpr' value=$partner.records_rpr.records[$rk]}
                        {assign var='lrec_rpr' value=$partner.records_rpr.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpr.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpr.date neq $last_date}
                            <td rowspan="{$partner.records_rpr.dates[$rec_rpr.date].rowspan}">{$rec_rpr.date|escape|default:"&nbsp;"}</td>
                        {/if}
                        <td>{$rec_rpr.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpr.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpr.price|string_format:"%.0f"|escape|default:"0"}</td>
                        {assign var=current_total_prizes value=$current_total_prizes+$rec_rpr.count}

                        {if $rec_rpr.date neq $last_date}
                            <td rowspan="{$partner.records_rpr.dates[$rec_rpr.date].rowspan}">{$partner.records_rpr.dates[$rec_rpr.date].total_points|string_format:"%.0f"|escape|default:"0"}</td>
                        {/if}

                        <td>{$rec_rpr.current_points|default:"0"|string_format:"%.0f"|escape}</td>
                    {else}
                        {if $rows_spent_rpr == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpr}" colspan="6"></td>
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr+$partner.rowspan-$partner.rowspan_rpr}
                        {else}
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr-1}
                        {/if}
                    {/if}

                    {if $partner.records_rpo.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var=nrk value=$rk+1}
                        {assign var='rec_rpo' value=$partner.records_rpo.records[$rk]}
                        {assign var='lrec_rpo' value=$partner.records_rpo.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpo.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$rec_rpo.date|escape|default:"&nbsp;"}</td>
                        {/if}

                        <td>{$rec_rpo.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpo.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpo.bonus|string_format:"%.0f"|escape|default:"0"}</td>

                        {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$partner.records_rpo.dates[$rec_rpo.date].total_points|string_format:"%.0f"|escape|default:"0"}</td>
                            {assign var=total_requested_points value=$total_requested_points+$partner.records_rpo.dates[$rec_rpo.date].total_points}
                        {/if}
                    {else}
                        {if $rows_spent_rpo == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpo}" colspan="5"></td>
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo+$partner.rowspan-$partner.rowspan_rpo}
                        {else}
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo-1}
                        {/if}
                    {/if}

                    {if $partner.records_repr.records[$rk]}
                        {assign var='rec_repr' value=$partner.records_repr.records[$rk]}
                        {assign var='lrec_repr' value=$partner.records_repr.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_repr.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_repr.date neq $last_date}
                            <td rowspan="{$partner.records_repr.dates[$rec_repr.date].rowspan}">{$rec_repr.date|escape|default:"&nbsp;"}</td>
                        {/if}

                        <td>{$rec_repr.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_repr.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_repr.price|string_format:"%.0f"|escape|default:"price"}</td>

                        {if $rec_repr.date neq $last_date}
                            <td rowspan="{$partner.records_repr.dates[$rec_repr.date].rowspan}">{$partner.records_repr.dates[$rec_repr.date].total_points|string_format:"%.0f"|escape|default:"0"}</td>
                            {assign var=total_points_spend value=$total_points_spend+$partner.records_repr.dates[$rec_repr.date].total_points}
                        {/if}
                    {else}
                        {assign var=lrk value=$rk-1}
                        {assign var=llrk value=$rk-2}
                        {if $rows_spent_repr == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_repr}" colspan="5"></td>
                            {assign var='rows_spent_repr' value=$rows_spent_repr+$partner.rowspan-$partner.rowspan_repr}
                        {else}
                            {assign var='rows_spent_repr' value=$rows_spent_repr-1}
                        {/if}
                    {/if}
                  </tr>
              {/foreach}
          {foreachelse}
              {cycle name='table_1' assign='table_1_rows_color' values='t_odd1 t_odd2,t_even1 t_even2'}
              <tr>
                <td colspan="23">{#no_items_found#|escape}</td>
              </tr>
          {/foreach}
          {if $index_1 > 0}

              <tr>
                <td colspan="6">{#reports_table_1_total_current_points#|escape}</td>
                <td>{$current_total_points|escape|default:"&nbsp;"}</td>

                <td colspan="2">{#reports_table_1_rpr_total_current_prizes#|escape}</td>
                <td>{$current_total_prizes|escape|default:"&nbsp;"}</td>

                <td colspan="7">{#reports_table_1_rpo_total_requested_points#|escape}</td>
                <td>{$total_requested_points|escape|default:"&nbsp;"}</td>

                <td colspan="4">{#reports_table_1_rep_total_spend_per_period#|escape}</td>
                <td>{$total_points_spend|escape|default:"&nbsp;"}</td>
              </tr>
          {/if}
      {else}
          {assign var=current_total_points value=0}
          {assign var=current_total_prizes value=0}
          {assign var=total_requested_points value=0}
          {assign var=total_points_spend value=0}
          {assign var=last_partner value=-1}
          {foreach from=$reports_results.results.table_1 item=partner name=partners key=pk}
              {counter name=index_1 assign=index_1}
              {cycle name='table_1' values='t_odd1 t_odd2,t_even1 t_even2' assign=table_1_row_color}
              {assign var=rows_spent_rpr value=0}
              {assign var=rows_spent_rpo value=0}

              {foreach from=$partner[$partner.biggest].records item=record name=records key=rk}
                  <tr>
                    {if $smarty.foreach.records.first}
                        <td rowspan="{$partner.rowspan}">{$index_1|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.partner_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.sales_agent_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.object_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.object_phase_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.bonus_all|string_format:"%.0f"|escape|default:"&nbsp;"}</td>
                        {if $partner.partner neq $last_partner}
                            {assign var=current_total_points value=$current_total_points+$partner.bonus_all}
                            {assign var=last_partner value=$partner.partner}
                        {/if}
                    {/if}
                    {if $partner.records_rpr.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var='rec_rpr' value=$partner.records_rpr.records[$rk]}
                        {assign var='lrec_rpr' value=$partner.records_rpr.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpr.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpr.date neq $last_date}
                            <td rowspan="{$partner.records_rpr.dates[$rec_rpr.date].rowspan}">{$rec_rpr.date|escape|default:"&nbsp;"}</td>
                        {/if}
                        <td>{$rec_rpr.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpr.count|escape|default:"&nbsp;"}</td>
                        {assign var=current_total_prizes value=$current_total_prizes+$rec_rpr.count}

                        <td>{$rec_rpr.current_points|default:"0"|string_format:"%.0f"|escape}</td>
                    {else}
                        {if $rows_spent_rpr == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpr}" colspan="4"></td>
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr+$partner.rowspan-$partner.rowspan_rpr}
                        {else}
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr-1}
                        {/if}
                    {/if}

                    {if $partner.records_rpo.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var=nrk value=$rk+1}
                        {assign var='rec_rpo' value=$partner.records_rpo.records[$rk]}
                        {assign var='lrec_rpo' value=$partner.records_rpo.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpo.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$rec_rpo.date|escape|default:"&nbsp;"}</td>
                        {/if}

                        <td>{$rec_rpo.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpo.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpo.bonus|string_format:"%.0f"|escape|default:"0"}</td>

                        {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$rec_rpo.total_points|string_format:"%.0f"|escape|default:"0"}</td>
                            {assign var=total_requested_points value=$total_requested_points+$rec_rpo.total_points}
                        {/if}
                    {else}
                        {if $rows_spent_rpo == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpo}" colspan="5"></td>
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo+$partner.rowspan-$partner.rowspan_rpo}
                        {else}
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo-1}
                        {/if}
                    {/if}
                  </tr>
              {/foreach}
          {foreachelse}
              {cycle name='table_1' assign='table_1_rows_color' values='t_odd1 t_odd2,t_even1 t_even2'}
              <tr>
                <td colspan="23">{#no_items_found#|escape}</td>
              </tr>
          {/foreach}
          {if $index_1 > 0}

              <tr>
                <td colspan="5">{#reports_table_1_total_current_points#|escape}</td>
                <td>{$current_total_points|string_format:"%.0f"|escape|default:"&nbsp;"}</td>

                <td colspan="2">{#reports_table_1_rpr_total_current_prizes#|escape}</td>
                <td>{$current_total_prizes|string_format:"%.0f"|escape|default:"&nbsp;"}</td>

                <td colspan="5">{#reports_table_1_rpo_total_requested_points#|escape}</td>
                <td>{$total_requested_points|string_format:"%.0f"|escape|default:"&nbsp;"}</td>
              </tr>
          {/if}
      {/if}
    </table>
  </td>
</tr>
<tr>
  <td><br /></td>
</tr>
<tr>
  <td><br /></td>
</tr>
<tr>
  <td>
    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        {if $reports_results.permission}
            <th rowspan="2">{#reports_table_2_index#|escape}</th>
            <th rowspan="2">{#reports_table_2_partner#|escape}</th>
            <th rowspan="2">{#reports_table_2_region#|escape}</th>

            <th colspan="5">{#reports_table_2_realized_products#|escape}</th>

            <th colspan="5">{#reports_table_2_realized_prizes#|escape}</th>
        </tr>
        <tr>
          <th>{#reports_table_2_rep_date#|escape}</th>
          <th>{#reports_table_2_rep_product#|escape}</th>
          <th>{#reports_table_2_rep_pallets_count#|escape}</th>
          <th>{#reports_table_2_rep_bonus_points#|escape}</th>
          <th>{#reports_table_2_rep_program#|escape}</th>

          <th>{#reports_table_2_rpr_date#|escape}</th>
          <th>{#reports_table_2_rpr_prize#|escape}</th>
          <th>{#reports_table_2_rpr_current_quantity#|escape}</th>
          <th>{#reports_table_2_rpr_avarage_price#|escape}</th>
          <th>{#reports_table_2_rpr_price#|escape}</th>
        {else}
            <th rowspan="2">{#reports_table_2_index#|escape}</th>
            <th rowspan="2">{#reports_table_2_partner#|escape}</th>

            <th colspan="3">{#reports_table_2_realized_products#|escape}</th>

            <th colspan="4">{#reports_table_2_realized_prizes#|escape}</th>
        </tr>
        <tr>
          <th>{#reports_table_2_rep_date#|escape}</th>
          <th>{#reports_table_2_rep_product#|escape}</th>
          <th>{#reports_table_2_rep_pallets_count#|escape}</th>

          <th>{#reports_table_2_rpr_date#|escape}</th>
          <th>{#reports_table_1_rep_prizes#|escape}</th>
          <th>{#reports_table_2_rpr_count#|escape}</th>
          <th>{#reports_table_1_rep_points_per_prize#|escape}</th>
        {/if}
      </tr>
      {if $reports_results.permission}
          {foreach from=$reports_results.results.table_2 item=partner name=partners key=pk}
              {counter name=index_2 assign=index_2}
              {cycle name='table_2' values='t_odd1 t_odd2,t_even1 t_even2' assign=table_2_row_color}
              {assign var=rows_spent_rpr value=0}
              {assign var=rows_spent_rpo value=0}

              {foreach from=$partner[$partner.biggest].records item=record name=records key=rk}
                  <tr>
                    {if $smarty.foreach.records.first}
                        <td rowspan="{$partner.rowspan}">{$index_2|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.partner_name|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.region|escape|default:"&nbsp;"}</td>
                    {/if}
                    {if $partner.records_rpo.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var='rec_rpo' value=$partner.records_rpo.records[$rk]}
                        {assign var='lrec_rpo' value=$partner.records_rpo.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpo.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$rec_rpo.date|escape|default:"&nbsp;"}</td>
                        {/if}
                        {*                                    {$partner.records_rpo.records|trace}*}
                        <td>{$rec_rpo.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpo.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpo.rpo_bonus|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpo.rpo_program_name|escape|default:"&nbsp;"}</td>
                    {else}
                        {assign var=lrk value=$rk-1}
                        {if $rows_spent_rpo == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpo}" colspan="5"></td>
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo+$partner.rowspan-$partner.rowspan_rpo}
                        {else}
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo-1}
                        {/if}
                    {/if}

                    {if $partner.records_rpr.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var=nrk value=$rk+1}
                        {assign var='rec_rpr' value=$partner.records_rpr.records[$rk]}
                        {assign var='lrec_rpr' value=$partner.records_rpr.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpr.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpr.date neq $last_date}
                            <td rowspan="{$partner.records_rpr.dates[$rec_rpr.date].rowspan}">{$rec_rpr.date|escape|default:"&nbsp;"}</td>
                        {/if}

                        <td>{$rec_rpr.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpr.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td>{$rec_rpr.rpr_reward_price|string_format:"%.0f"|escape|default:"0"}</td>
                        <td style="mso-number-format: '0\.00';">{$rec_rpr.rpr_price_amount|string_format:"%.2f"|escape|default:"0"}</td>

                    {else}
                        {assign var=lrk value=$rk-1}
                        {if $rows_spent_rpr == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpr}" colspan="5"></td>
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr+$partner.rowspan-$partner.rowspan_rpr}
                        {else}
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr-1}
                        {/if}
                    {/if}
                  </tr>
              {/foreach}
          {foreachelse}
              {cycle name='table_2' assign='table_2_rows_color' values='t_odd1 t_odd2,t_even1 t_even2'}
              <tr>
                <td colspan="13">{#no_items_found#|escape}</td>
              </tr>
          {/foreach}
      {else}

          {foreach from=$reports_results.results.table_2 item=partner name=partners key=pk}
              {counter name=index_2 assign=index_2}
              {cycle name='table_2' values='t_odd1 t_odd2,t_even1 t_even2' assign=table_2_row_color}
              {assign var=rows_spent_rpr value=0}
              {assign var=rows_spent_rpo value=0}

              {foreach from=$partner[$partner.biggest].records item=record name=records key=rk}
                  <tr>
                    {if $smarty.foreach.records.first}
                        <td rowspan="{$partner.rowspan}">{$index_2|escape|default:"&nbsp;"}</td>
                        <td rowspan="{$partner.rowspan}">{$partner.partner_name|escape|default:"&nbsp;"}</td>
                        {/if}
                        {if $partner.records_rpo.records[$rk]}
                            {assign var=lrk value=$rk-1}
                            {assign var='rec_rpo' value=$partner.records_rpo.records[$rk]}
                            {assign var='lrec_rpo' value=$partner.records_rpo.records[$lrk]}
                            {if $rk != 0}
                                {assign var='last_date' value=$lrec_rpo.date}
                            {else}
                                {assign var='last_date' value=''}
                            {/if}
                            {if $rec_rpo.date neq $last_date}
                            <td rowspan="{$partner.records_rpo.dates[$rec_rpo.date].rowspan}">{$rec_rpo.date|escape|default:"&nbsp;"}</td>
                        {/if}
                        <td>{$rec_rpo.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpo.count|string_format:"%.0f"|escape|default:"0"}</td>
                    {else}
                        {assign var=lrk value=$rk-1}
                        {if $rows_spent_rpo == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpo}" colspan="3"></td>
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo+$partner.rowspan-$partner.rowspan_rpo}
                        {else}
                            {assign var='rows_spent_rpo' value=$rows_spent_rpo-1}
                        {/if}
                    {/if}
                    {if $partner.records_rpr.records[$rk]}
                        {assign var=lrk value=$rk-1}
                        {assign var=nrk value=$rk+1}
                        {assign var='rec_rpr' value=$partner.records_rpr.records[$rk]}
                        {assign var='lrec_rpr' value=$partner.records_rpr.records[$lrk]}
                        {if $rk != 0}
                            {assign var='last_date' value=$lrec_rpr.date}
                        {else}
                            {assign var='last_date' value=''}
                        {/if}
                        {if $rec_rpr.date neq $last_date}
                            <td rowspan="{$partner.records_rpr.dates[$rec_rpr.date].rowspan}">{$rec_rpr.date|escape|default:"&nbsp;"}</td>
                        {/if}

                        <td>{$rec_rpr.name|escape|default:"&nbsp;"}</td>
                        <td>{$rec_rpr.count|string_format:"%.0f"|escape|default:"0"}</td>
                        <td style="mso-number-format: '0\.00';">{$rec_rpr.price|string_format:"%.2f"|escape|default:"0"}</td>

                    {else}
                        {assign var=lrk value=$rk-1}
                        {if $rows_spent_rpr == 0}
                            <td rowspan="{$partner.rowspan-$partner.rowspan_rpr}" colspan="4"></td>
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr+$partner.rowspan-$partner.rowspan_rpr}
                        {else}
                            {assign var='rows_spent_rpr' value=$rows_spent_rpr-1}
                        {/if}
                    {/if}
                  </tr>
              {/foreach}
          {foreachelse}
              {cycle name='table_2' assign='table_2_rows_color' values='t_odd1 t_odd2,t_even1 t_even2'}
              <tr>
                <td colspan="9">{#no_items_found#|escape}</td>
              </tr>
          {/foreach}
      {/if}
    </table>
  </body>
</html>
