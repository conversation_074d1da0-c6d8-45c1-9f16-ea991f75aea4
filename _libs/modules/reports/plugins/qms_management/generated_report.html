<script type="text/javascript">
  // Set the color of the doc_type field
  var doc_type = $('doc_type');
  if (doc_type.value != '') {ldelim}
      doc_type.style.backgroundColor = '#{$smarty.const.DOC_TYPE_COLOR}';
  {rdelim}

  //Set the color of the nomenclature field
  var nomenclature = $('nomenclature_autocomplete');
  if (nomenclature.value != '') {ldelim}
      nomenclature.style.backgroundColor = '#{$smarty.const.NOMENCLATURE_COLOR}';
  {rdelim}
</script>

{literal}
<script type="text/javascript">
  /**
   * Show/Hide the children elements (the children ul tag)
   *   and change (show/hide) the plus/minus button image
   *
   * @param element - DOM img object
   */
  function childrenShowHide(element) {
      // Get the children ul tag
      var ul  = element.parentNode.parentNode.parentNode.getElementsByTagName('ul');
      // Get the img tags for the plus/minus buttons
      var img = element.parentNode.getElementsByTagName('img');

      // If the children elements (the children ul tag) are expanded
      if (ul[0].style.display != 'none') {
          // Collapse the children (the children ul tag)
          ul[0].style.display = 'none';

          // Show/Hide the plus/minus buttons
          if (img[1].name == 'minus') {
              // Hide the minus button
              img[1].style.display = 'none';
              // Show the plus button
              img[0].style.display = '';
          } else if (img[0].name == 'minus') {
              // Hide the minus button
              img[0].style.display = 'none';
              // Show the plus button
              img[1].style.display = '';
          }
      } else {
          // Expand the children (the children ul tag)
          ul[0].style.display = 'block';

          // Show/Hide the plus/minus buttons
          if (img[0].name == 'plus') {
              // Hide the plus button
              img[0].style.display = 'none';
              // Show the minus button
              img[1].style.display = '';
          } else if (img[1].name == 'plus') {
              // Hide the plus button
              img[1].style.display = 'none';
              // Show the minus button
              img[0].style.display = '';
          }
      }
  }
</script>

<style type="text/css">
  #results_container ul {
    display: block;
    margin:  0px 0px 0px 28px;
    padding: 0px;
    }
  #results_container li {
    display: block;
    margin:  0px;
    padding: 0px;
    }
  #results_container div {
    margin:  0px 0px 0px 4px;
    padding: 0px;
    float:   left;
    }
</style>
{/literal}

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td id="results_container">
      {if $reports_results.nomenclatures}
        <ul style="margin-left: 0px !important;">
          {include file="`$templatesDirPlugin`/`$report_type`/tree_branches.html" branches=$reports_results.nomenclatures_tree}
        </ul>
      {else}
        <span style="color: red;">{#no_items_found#|escape}</span>
      {/if}
    </td>
  </tr>
</table>

{literal}
<script type="text/javascript">
  function expander(e) {
      // Display the ul
      e.parentNode.style.display = 'block';
      // Get the parent li tag
      var parent_parent = e.parentNode.parentNode;
      // If the id is not results_container, continue with expanding
      if (parent_parent.id != 'results_container') {
          var plus_minus = parent_parent.getElementsByTagName('img');
          if (plus_minus[0].name == 'plus') {
              plus_minus[0].style.display = 'none';
              plus_minus[1].style.display = '';
          } else {
              plus_minus[0].style.display = '';
              plus_minus[1].style.display = 'none';
          }

          // Expand the next element
          expander(parent_parent);
      } else {
          // The td tag with id results_container is reached soo stop the expander
          return true;
      }
  }

  var marked_elements = document.getElementsByClassName('marked');
  for (var i = 0; i < marked_elements.length; i++) {
      expander(marked_elements[i]);
  }
</script>
{/literal}
