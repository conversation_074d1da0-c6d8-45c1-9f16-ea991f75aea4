<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        parent::execute();
    }

    /**
     * A custom function to create a model (in this case: a finance expenses documents)
     *
     * @param array $settings - the report settings
     * @return                - redirects back to the report
     */
    public function customCreateModel($settings) {
        // Prepare some basics
        $registry = &$this->registry;
        $messages = &$registry['messages'];
        $db       = &$registry['db'];

        // Get  the selected items from the request
        $selected_items = $registry['request']->get('items');

        // If there are any selected items
        if (!empty($selected_items)) {
            // Check if a sheets count and price per sheet are used
            $use_sheets = false;
            if (isset($settings['field_doc_translation_request_actual_sheets']) && $settings['field_doc_translation_request_actual_sheets'] != ''
                    && isset($settings['field_doc_translation_request_price_per_sheet']) && $settings['field_doc_translation_request_price_per_sheet'] != '') {
                $use_sheets = true;
            }

            // Get the finance document type: Honorarium sheet
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $finance_documents_type_filters = array(
                'where'      => array('fdt.id = ' . $settings['finance_expenses_type_honorarium_sheet']),
                'model_lang' => $registry['lang']);
            $finance_documents_type = Finance_Documents_Types::searchOne($registry, $finance_documents_type_filters);

            // If we found a finance document type model
            if ($finance_documents_type) {
                // Prepare some basics for the new finance documents
                $properties = array(
                    'type'         => $settings['finance_expenses_type_honorarium_sheet'],
                    'name'         => $finance_documents_type->get('name'),
                    'issue_date'   => date('Y-m-d'),
                    'company_data' => $settings['company_data'],
                    'currency'     => $settings['report_honorarium_sheet_currency'],
                    'active'       => '1',
                    'is_portal'    => '0');

                // Get te default vat from the type
                $finance_documents_type->getTypesVatOptionsExpenses();
                $default_vat = $finance_documents_type->get('default_VAT');

                // Include some required files
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.audit.php';
                $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini');
                $registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_expenses_reasons.ini');

                // Prepare for success/failed message
                $hs_success = array();
                $hs_failed  = array();

                // Go through each selected item
                foreach ($selected_items as $selected_item) {
                    // Decode the encoded data
                    $selected_item = (array)json_decode(base64_decode($selected_item));

                    // Group items by customers
                    $customers_items[$selected_item['translator_customer_id']][] = $selected_item;
                }

                // Go through each customer items
                foreach ($customers_items as $customer_id => $customer_items) {
                    // Start a transaction
                    $db->StartTrans();

                    // Build a new finance expenses document model
                    $model = new Finance_Expenses_Reason($registry, $properties);

                    // Set the customer (i.e. the translator)
                    $model->set('customer', $customer_id, true);

                    // define group and department (default settings could be specific values and not ids)
                    $model->setGroup(false, $finance_documents_type->get('default_group'));
                    $model->setDepartment(false, $finance_documents_type->get('default_department'));

                    // Get the GT2
                    $get_old_vars = $registry->get('get_old_vars');
                    $registry->set('get_old_vars', true, true);
                    $model->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);
                    $vars       = $model->get('vars');
                    $gt2        = '';
                    $gt2_var_id = '';
                    foreach ($vars as $var_id => $var) {
                        if ($var['type'] == 'gt2') {
                            $gt2        = $var;
                            $gt2_var_id = $var_id;
                            break;
                        }
                    }

                    /*
                     * Fill the GT2
                     */
                    // Fill some main GT2 fields
                    $gt2['plain_values']['currency']       = $settings['report_honorarium_sheet_currency'];
                    $gt2['plain_values']['total_vat_rate'] = $default_vat;

                    // Set the GT2 rows
                    $hs_requests = array();
                    $customer_name = '';
                    foreach ($customer_items as $customer_item) {
                        // Set the GT2 row
                        $gt2['values'][] = array(
                            'quantity'             => ($use_sheets ? $customer_item['request_actual_sheets'] : 1),
                            'article_measure_name' => $settings['translation_measure'],
                            'article_id'           => $customer_item['request_language_id'],
                            'article_name'         => $customer_item['request_language'],
                            'price'                => ($use_sheets ? $customer_item['request_price_per_sheet'] : $customer_item['request_final_price']),
                            'free_field1'          => $customer_item['request_order_id'],
                            'free_text1'           => $customer_item['request_order_num']);

                        // Collect the request ids
                        $hs_requests[] = $customer_item['request_id'];

                        // Get the customer name
                        if (empty($customer_name) && !empty($customer_item['translator_customer_name'])) {
                            $customer_name = $customer_item['translator_customer_name'];
                        }
                    }

                    // Set the GT2 back into the model
                    $model->set('grouping_table_2', $gt2, true);
                    $model->calculateGT2();
                    $model->set('table_values_are_set', true, true);

                    // Try to add the finance expenses document
                    if ($model->save()) {
                        // Write history
                        Finance_Expenses_Reasons_History::saveData(
                            $registry,
                            array(
                                'action_type' => 'add',
                                'model'       => $model,
                                'new_model'   => $model,
                                'old_model'   => new Finance_Expenses_Reason($registry, array())
                            )
                        );

                        // If there are any honorarium sheet requests
                        if (!empty($hs_requests)) {
                            /*
                             * Relate the honorarium sheet to the requests
                             */
                            // Build the query to make the relations
                            $query = "
                                INSERT INTO `" . DB_TABLE_FINANCE_REASONS_RELATIVES . "` (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`) VALUES";
                            $hs_id = $model->get('id');
                            $insert_values = array();
                            foreach ($hs_requests as $request_id) {
                                $insert_values[] = "('{$hs_id}', 'Finance_Expenses_Reason', '{$request_id}', 'Document')";
                            }
                            $query .= "
                                  " . implode(",
                                  ", $insert_values);

                            // Try to execute the SQL query
                            if (!$db->Execute($query)) {
                                // If the execution of the SQL query has failed then fail the transacion
                                $db->FailTrans();
                            }
                        } else {
                            // Fail the transacion
                            $db->FailTrans();
                        }
                    } else {
                        // Fail the transacion
                        $db->FailTrans();
                    }

                    // If the transacion has failed
                    if ($db->HasFailedTrans()) {
                        $hs_failed[] = $customer_name;
                    } else {
                        // Add the current request to the success list
                        $hs_success[$hs_id] = $customer_name;
                    }

                    // Complete the transacion
                    $db->CompleteTrans();
                }

                // Show success/failed message
                if (!empty($hs_failed)) {
                    $messages->setError($this->i18n('error_reports_add_honorarium_sheet_failed'));
                    foreach ($hs_failed as $customer_name) {
                        $messages->setError(sprintf($this->i18n('reports_honorarium_sheet_for_translator'), $customer_name));
                    }
                }
                if (!empty($hs_success)) {
                    $messages->setMessage($this->i18n('message_reports_add_honorarium_sheet_success'));
                    foreach ($hs_success as $honorarium_sheet_id => $customer_name) {
                        $messages->setMessage(sprintf(
                            '<a target="_blank" href="%s?%s=finance&amp;controller=expenses_reasons&amp;expenses_reasons=view&amp;view=%s">' .
                                sprintf($this->i18n('reports_honorarium_sheet_for_translator'), $customer_name) .
                            '</a>',
                            $_SERVER['PHP_SELF'], $registry['module_param'],
                            $honorarium_sheet_id));
                    }
                }
            } else {
                // Show error
                $messages->setError(sprintf($this->i18n('error_reports_finance_expenses_type_honorarium_sheet_not_found', $settings['finance_expenses_type_honorarium_sheet'])));
            }
        } else {
            // Show error
            $messages->setError($this->i18n('error_reports_no_selected_items'));
        }

        // Set the messages into the session
        $messages->insertInSession($registry);

        // Redirect back to the report
        $report_type = $this->getReportType();
        $this->redirect($this->module, '', array('report_type' => $report_type['name']), '');
    }
}

?>
