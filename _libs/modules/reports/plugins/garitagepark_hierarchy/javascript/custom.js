var tree_top_position = 0;
has_scrolled = false;
is_floating = false;
document.onscroll = function() { has_scrolled = true; };
window.setInterval(
    function () {
        if (has_scrolled) {
            if (window.pageYOffset < (tree_top_position-10)) {
                if (is_floating) {
                    is_floating = false;
                    removeClass($('nom_additional_info'), 'floating_panel');
                }
            } else {
                if (!is_floating) {
                    is_floating = true;
                    addClass($('nom_additional_info'), 'floating_panel');
                }
            }
            has_scrolled = false;
        }
    },
    50
);

/**
 * custom javascript messages
 */
if (env.current_lang == 'bg') {
    i18n['messages']['error_complete_required_data'] = 'Попълнете Файл и поне едно от полетата Категория или Подкатегория за всеки от избраните редове!';
} else {
    i18n['messages']['error_complete_required_data'] = 'Complete File and at least one of the files Category ot Subcategory for all selected rows!';
}

/*
 * Function to perform action for showing the side panel
 */
var startUpTreeLoad = function() {
    $('gariragepark_nom_tree').style.visibility = 'visible';
    tree_top_position = $('complexes_tree_container').offsetTop;
    $('nom_additional_info').style.left = (22 + left_position) + 'px';
    Effect.Fade('loading');
    if (selected_element) {
        element_details_data = selected_element.split('_');
        if (element_details_data[0] && element_details_data[1] && elements_tree_index_relations[element_details_data[0]]) {
            node = Zapatec.Tree.all['tree_complexes'].getNode(elements_tree_index_relations[element_details_data[0]]).labelContainer;
            loadNomDetailedInfo(element_details_data[0], element_details_data[1]);
            new Effect.ScrollTo(node);
        }
    }
}

var collapseTreeLoad = function() {
    $('gariragepark_nom_tree').style.width=$('gariragepark_nom_tree').clientWidth + 'px';
    Zapatec.Tree.all['tree_complexes'].collapseAll();
    if (collapse_elements) {
        collapse_elements.each(function(el1) {
            path_data = el1.split('_');
            path_data.each(function(el2) {
                if (elements_tree_index_relations[el2]) {
                    Zapatec.Tree.all['tree_complexes'].toggleItem(elements_tree_index_relations[el2], true);
                }
            });
        });
    }
}

/*
 * Function to load the nomenclature data
 */
function loadNomDetailedInfo(nom_id, nom_type) {
    if ($('nom_data_loaded') && $('nom_data_loaded').value==nom_id) {
        return false;
    }

    $$('.filtered').each(function (e) {
        removeClass(e, 'filtered');
    });

    let skip_info_loading = false;
    node = Zapatec.Tree.all['tree_complexes'].getNode(elements_tree_index_relations[nom_id]).labelContainer;
    if (node) {
        $(node).select('.nom_div_tree_element').each(function (e) {
            addClass(e, 'filtered');
            if (!e.className.match(/info_available/g)) {
                skip_info_loading = true;
            }
        });
    }

    if (skip_info_loading) {
        return false;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=load_nom_additional_info&report_type=' + $('report_type').value + '&nom_id=' + nom_id + '&nom_type=' + nom_type;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            $('nom_additional_info').innerHTML = t.responseText;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to load the desired owner/renter
 */
function loadOwnerRenterData(nom_id, owner_renter_id, source_panel) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=load_owner_renter_info&report_type=' + $('report_type').value + '&nom_id=' + nom_id + '&owner_renter_id=' + owner_renter_id + '&source_panel=' + source_panel;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            eval('var owner_renter = ' + t.responseText + ';');

            $(source_panel + '_panel').innerHTML = owner_renter.template;
            if (owner_renter.is_company) {
                loadContactPersonData(owner_renter_id, 0, source_panel, 0);
            } else {
                $(source_panel + '_contact_person_panel').innerHTML = '';
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to load the desired contact person
 */
function loadContactPersonData(parent_customer, contact_id, source_panel, after_add) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=load_contact_person_info&report_type=' + $('report_type').value + '&parent_customer=' + parent_customer + '&contact_id=' + contact_id + '&source_panel=' + source_panel + '&after_add=' + after_add;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            $(source_panel + '_contact_person_panel').innerHTML = t.responseText;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to load the files tree for the selected nomenclature
 */
function loadFilesTree(nom_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=load_files_tree&report_type=' + $('report_type').value + '&nomenclature_id=' + nom_id;
    if ($('eq_files_panel_' + nom_id)) {
        div_id = 'eq_files_panel_' + nom_id;
    } else {
        div_id = 'nom_additional_info';
    }

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            $(div_id).innerHTML = t.responseText;
            var scripts = $(div_id).getElementsByTagName('SCRIPT');
            for (var k = 0; k < scripts.length; k++) {
                ajaxLoadJS(scripts[k]);
            }
            if (div_id != 'nom_additional_info') {
                $$('#' + div_id + '>div').each(function(el) {
                    el.style.border = '';
                });
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to load the files tree for the selected nomenclature
 */
function openContactPersonEditForm(parent_customer_id, contact_person_id, source_panel) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var action = 'ajax_add';
    var additional_data = '';
    if (contact_person_id) {
        action = 'ajax_edit';
        additional_data = '&ajax_edit=' + contact_person_id;
    }
    var url = env.base_url + '?' + env.module_param + '=customers&controller=contactpersons&contactpersons=' + action + '&parent_customer_id=' + parent_customer_id + additional_data;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            // Activate the lightbox
            lb = new lightbox({
                content: result.data,
                title:   result.title,
                width:   '660px'});
            lb.activate();

            // edit the table template
            tables = $$('#lightbox form>table');
            for (i=0; i<tables.length; i++) {
                tables[i].style.width="";
            }

            table_titles = $$('#lightbox form>table .t_caption');
            for (j=0; j<table_titles.length; j++) {
                current_element = table_titles[j];
                while (current_element.tagName != 'TR') {
                    current_element = current_element.parentNode;
                }
                current_element.parentNode.removeChild(current_element);
            }

            // set default value for branch and hide the row
            $('parent_branch').value = $('parent_branch').options[1].value;
            current_element = $('parent_branch').parentNode;
            while (current_element.tagName != 'TR') {
                current_element = current_element.parentNode;
            }
            current_element.style.display = 'none';

            // update the function that the button will trigger
            $$('button[name=saveButton1]')[0].onclick = function() {saveContactPersonData(this, action, parent_customer_id, contact_person_id, source_panel);};

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to save the contact person data
 */
function saveContactPersonData(element, action, parent_customer_id, contact_person_id, source_panel) {
    form = element.form;
    if ($('lastname').value == '' || $('parent_branch').value == '') {
        alert(i18n.messages['alert_empty_field']);
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            eval(t.responseText);
            var div = document.createElement('div');

            div.id = 'garitage_heirarchy_contact_person_messages';
            errors = false;
            for (i in result) {
                if (i == 'errors') {
                    errors = true;
                    div.innerHTML = result[i];
                }
            }

            if (errors) {
                messages = '';
                messages_included = div.getElementsBySelector('li a');
                for (p=0; p<messages_included.length; p++) {
                    messages += messages_included[p].innerHTML + '\n';
                }
                alert(messages);
            } else {
                if (action == 'ajax_add') {
                    after_add = 1;
                } else {
                    after_add = 0;
                }
                loadContactPersonData(parent_customer_id, contact_person_id, source_panel, after_add);
                lb.deactivate();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=customers&controller=contactpersons&contactpersons=' + action + '&parent_customer_id=' + parent_customer_id;
    if (action == 'ajax_edit') {
        url += '&ajax_edit=' + contact_person_id;
    }
    new Ajax.Request(url, opt);

    return false;
}

/*
 * Function to edit the file
 */
function garitageEditFile(nomenclature_id, row_id, parent_category, reload_noms_tree) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_edit_file&report_type=' + $('report_type').value + '&nomenclature_id=' + nomenclature_id + '&row=' + row_id + '&category=' + parent_category + '&reload_noms_tree=' + reload_noms_tree;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText + ';');

            // Activate the lightbox
            lb = new lightbox({
                content: result.content,
                title:   result.title,
                width:   result.width + 'px'});
            lb.activate();

            $$('.lb_content');

            $$('.lb_content').each(function(el) {
                el.select('span.group_table_tow_num').each(function(el1) {
                    el1.removeAttribute('onClick');
                })
            });

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to delete the file
 */
function garitageDeleteFile(nomenclature_id, row_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_delete_file&report_type=' + $('report_type').value + '&nomenclature_id=' + nomenclature_id + '&row=' + row_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText + ';');

            if (result.result) {
                // reload the tree
                loadFilesTree(nomenclature_id);
                loadNomsIcon(nomenclature_id);
                Effect.Fade('loading');
            } else {
                alert(result.message);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to check if there are available rows to remove
 */
function checkAvailableRowsToRemove(table_id) {
    var check_result = false;
    rows = $$('#' + table_id + ' tr[id^="var_group_"]');
    for (i=0; i<rows.length; i++) {
        if (rows[i].style.display != 'none') {
            check_result = true;
        }
    }
    $$('#' + table_id + ' tr[id^="var_group_"]').each(function(el) {
        if (el.style.display != 'none') {
            check_result = true;
        }
    });

    return check_result;
}

/*
 * Function to activate the actual add/edit process of the grouping table
 */
function saveNewGTRows(element, reload_noms_tree) {
    // validate the data
    valid = true;
    category_fileds = $$('input[id^="category_id_"]');
    category_fileds.each(function(el) {
        row_id = el.id.replace(/category_id_/, '');
        if (!$('category_id_' + row_id).disabled && !$('category_id_' + row_id).value && !$('subcategory_id_' + row_id).value) {
            valid = false;
        }
        if (!$('category_id_' + row_id).disabled && !$('category_file_' + row_id).value && !($('dbid_category_file_' + row_id) && $('dbid_category_file_' + row_id).value)) {
            valid = false;
        }
    });

    if (!valid) {
        alert(i18n['messages']['error_complete_required_data']);
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    form = element.form;

    // get the current form options
    var current_form_target = form.target;
    var current_form_action = form.action;
    var current_form_id = form.id;

    iframe_target = $('garitagepark_upload_target');
    iframe_target.onload = function () {processGTTableEdit(this, reload_noms_tree);};
    form.target = iframe_target.id;

    // prepare the action URL for the form
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_save_file&report_type=' + $('report_type').value;
    form.action = url;
    form.submit();

    // return the previous options to the current form
    form.target = current_form_target;
    form.action = current_form_action;
    form.id = current_form_id;
    return false;
}

function processGTTableEdit(element, reload_noms_tree) {
    var scripts = element.contentDocument.getElementsByTagName('SCRIPT');
    eval(scripts[0].text);

    element.onload = function () {return false;};
    element.contentDocument.innerHTML = '';

    if (operation_result.result) {
        nom_id = $('nomenclature_id').value;
        lb.deactivate();
        Effect.Fade('loading');
        if (reload_noms_tree) {
            $('nom_additional_info').innerHTML = '';
            loadNomsIcon(nom_id);
        }
        loadFilesTree(nom_id);
    } else {
        alert(operation_result.message);
        Effect.Fade('loading');
    }
}

/*
 * Function to reload the nomenclatures tree
 */
function loadNomsIcon(nom_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_reload_noms_tree_icon&report_type=' + $('report_type').value + '&nomenclature_id=' + nom_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            related_element = $('edit_files_button_' + nom_id);
            related_element.insert( {after: t.responseText} );
            related_element.parentNode.removeChild(related_element);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to reload the nomenclatures tree
 */
function loadEquipment(nom_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=load_equipment&report_type=' + $('report_type').value + '&nomenclature_id=' + nom_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            $('nom_additional_info').innerHTML = t.responseText;
            var scripts = $('nom_additional_info').getElementsByTagName('SCRIPT');
            for (var k = 0; k < scripts.length; k++) {
                ajaxLoadJS(scripts[k]);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to toggle extended info
 */
function toggleExtendedEquipmentInfo(equipment_id, type, num) {
    if ($('eqp_extended_info_' + num)) {
        if ($('eqp_extended_info_' + num).style.display == 'none') {
            $('eqp_extended_info_' + num).style.display = 'table-row';
        } else {
            $('eqp_extended_info_' + num).style.display = 'none';
        }
    } else {
        Effect.Center('loading');
        Effect.Appear('loading');

        // get the row element
        parent_row = $('eq_main_row_' + num);
        new_row = $('eq_table').insertRow(parent_row.rowIndex + 1);
        new_row.id = 'eqp_extended_info_' + num;
        cell = new_row.insertCell(0);
        cell.colSpan = "2";
        cell.style.padding = "0";
        cell.style.backgroundColor = '#F1F1F1';

        var url = env.base_url + '?' + env.module_param + '=reports&reports=load_nom_additional_info&report_type=' + $('report_type').value + '&nom_id=' + equipment_id + '&nom_type=' + type;

        var opt = {
            asynchronous: false,
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }

                div_nest = document.createElement("DIV");
                div_nest.style.display = 'flex';
                div_nest.style.flexDirection = 'row';
                div1 = document.createElement("DIV");

                div1.innerHTML = t.responseText;
                div1.style.float = 'left';
                div_nest.appendChild(div1);

                var url = env.base_url + '?' + env.module_param + '=reports&reports=load_files_tree&report_type=' + $('report_type').value + '&nomenclature_id=' + equipment_id;
                var opt = {
                    asynchronous: false,
                    method: 'get',
                    onSuccess: function (t) {
                        if (!checkAjaxResponse(t.responseText)) {
                            return;
                        }

                        div2 = document.createElement("DIV");
                        div2.innerHTML = t.responseText;
                        div2.style.float = 'left';
                        div2.id = 'eq_files_panel_' + equipment_id;
                        div_nest.appendChild(div2);
                        cell.appendChild(div_nest);

                        $$('#' + div2.id + '>div').each(function(el) {
                            el.style.border = '';
                        });

                        var scripts = div2.getElementsByTagName('SCRIPT');
                        for (var k = 0; k < scripts.length; k++) {
                            ajaxLoadJS(scripts[k]);
                        }
                        Effect.Fade('loading');
                    },
                    on404: function (t) {
                        alert('Error 404: location "' + t.statusText + '" was not found.');
                    },
                    onFailure: function (t) {
                        alert('Error ' + t.status + ' -- ' + t.statusText);
                    }
                };

                new Ajax.Request(url, opt);
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    }
}

/*
 * Function to prepare and load custom search of equipment
 */
function loadFullEquipmentList(nom_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_prepare_custom_nom_search&report_type=' + $('report_type').value + '&nom_id=' + nom_id;

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            window.open(t.responseText, '_blank');
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}