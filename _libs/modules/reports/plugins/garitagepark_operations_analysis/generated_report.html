{literal}
  <style type="text/css">

  </style>
{/literal}


<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list" style="width: 1600px;table-layout: fixed;background-color: #ffffff;margin-bottom: 10px">
    <tr class="reports_title_row">
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_customer#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_object#}</th>
        <th style="text-align: center; vertical-align: middle;" width="70" class="t_border">{#reports_task#}</th>
        <th style="text-align: center; vertical-align: middle; word-break: break-all" width="80" class="t_border">{#reports_priority#}</th>
        <th style="text-align: center; vertical-align: middle;" width="60" class="t_border">{#reports_date#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_service#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_subservice#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_variant_subservice#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_filter_supervisor_technician#}</th>
        <th style="text-align: center; vertical-align: middle;" width="40" class="t_border">{#reports_time#}</th>
        <th style="text-align: center; vertical-align: middle;"  width="150" class="t_border">{#reports_date_report#}</th>
        <th style="text-align: center; vertical-align: middle;" width="40" class="t_border">{#reports_total_time#}</th>
        <th style="text-align: center; vertical-align: middle;" class="t_border">{#reports_description#}</th>
    </tr>
    {foreach from=$reports_results key=customer_id item=customer name='customers'}
        {assign var='documensCount' value=$customer|@count}
        {foreach from=$customer item=document key=document_id name='documents'}
            {if $document.timesheet_doneby_name|@is_array}
             {assign var='documentRowSpan' value=$document.timesheet_doneby_name|@count}

            {/if}

            {foreach from=$document.timesheet_doneby_id key=num item=doneby_id name='technicians'}
            <tr>
                {if  $smarty.foreach.documents.first
                && $smarty.foreach.technicians.first}
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$reports_additional_options.customers.$customer_id.rowspan}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$customer_id}" target="_blank">{$reports_additional_options.customers.$customer_id.name}</a></td>
                {/if}

                {if $smarty.foreach.technicians.first}
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$document.object_id.1}">{$document.object_name.1}</a></td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$document_id}">{$document.full_num}</a></td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.priority_service_name}</td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.document_date|date_format:#date_short#}</td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.service_name.1}</td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.subservice_other_name.1}</td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.sub_option.1}</td>
                {/if}

                <td style="text-align: left;white-space: nowrap;" class="t_v_border t_border" >{$document.timesheet_doneby_name.$num}</td>
                <td style="text-align: left;width: 50px;" class="t_v_border t_border" >{$document.timesheet_time.$num}</td>

                <td style="text-align: left;white-space: nowrap;" class="t_v_border t_border" >{$document.timesheet_from.$num|date_format:#date_short#} ({$document.timesheet_from.$num|date_format:"%R"} - {$document.timesheet_to.$num|date_format:"%R"})</td>

                {if $smarty.foreach.technicians.first}
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.total_timesheet_time}</td>
                <td style="text-align: left;" class="t_v_border t_border" rowspan="{$documentRowSpan}">{$document.description_service.1}</td>
                {/if}
            </tr>
            {/foreach}
        {/foreach}
    <tr style="background-color: #85B5FC; font-weight: bold;">
        <td colspan="13" style="text-align: left;" class="t_v_border">{#reports_total_time#}: {$reports_additional_options.customers.$customer_id.total_time}  {#reports_total_orders#}: {$documensCount}</td>
    </tr>

    {foreachelse}
    <tr>
        <td class="error" colspan="13">{#no_items_found#|escape}</td>
    </tr>
    {/foreach}
    <tr style="background-color: #FFF3C8; font-weight: bold;">
        <td colspan="13" style="text-align: left;" class="t_v_border">{#reports_total_time#}: {$reports_additional_options.total_time}  {#reports_total_orders#}: {$reports_additional_options.total_orders}</td>
    </tr>


</table>
