<?php
    class Custom_Report_Filters extends Report_Filters {

        private static $registry;

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            define('SERVICE_NOMENCLATURE_ID', 5);

            self::$registry = $registry;
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name'      => 'from_date',
                'type'      => 'date',
                'required'  => 1,
                'label'     => $this->i18n('reports_from_date'),
                'help'      => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name'      => 'to_date',
                'type'      => 'date',
                'required'  => 1,
                'label'     => $this->i18n('reports_to_date'),
                'help'      => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE NOMENCLATURES FILTER
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $nom_filters = array('model_lang' => $registry['lang'],
                                 'sanitize'   => true,
                                 'where'      => array('n.active = 1',
                                                       'n.type = ' . SERVICE_NOMENCLATURE_ID),
                                 'check_module_permissions' => 'nomenclatures');
            $nomenclatures = Nomenclatures::search($registry, $nom_filters);

            $options_nomenclatures = array();
            foreach($nomenclatures as $nom) {
                $options_nomenclatures[] = array(
                    'label'         => $nom->get('name'),
                    'option_value'  => $nom->get('id')
                );
            }

            $filter = array (
                'custom_id' => 'nomenclature',
                'name'      => 'nomenclature',
                'type'      => 'dropdown',
                'required'  => 1,
                'options'   => $options_nomenclatures,
                'label'     => $this->i18n('reports_nomenclature'),
                'help'      => $this->i18n('reports_nomenclature')
            );
            $filters['nomenclature'] = $filter;

            //DEFINE SHOW FILTER
            $options_show = array(
                array(
                    'label'         => $this->i18n('reports_activities_no_comments'),
                    'option_value'  => 'activities_no_comments'
                ),
                array(
                    'label'         => $this->i18n('reports_activities_problems'),
                    'option_value'  => 'reports_activities_problems'
                ),
                array(
                    'label'         => $this->i18n('reports_activities_not_completed'),
                    'option_value'  => 'activities_not_completed',
                    'on_click'      => "activateRow(this, 'show_partial_weeks')",
                )
            );
            $filter = array (
                'custom_id'         => 'show_table',
                'name'              => 'show_table',
                'type'              => 'custom_filter',
                'custom_template'   => PH_MODULES_DIR . 'reports/plugins/' . $registry['report_type']['name'] . '/show_options_filter.html',
                'options'           => $options_show,
                'label'             => $this->i18n('reports_show_table'),
                'help'              => $this->i18n('reports_show_table')
            );
            $filters['show_table'] = $filter;

            //DEFINE SHOW PARTIAL WEEKS
            //prepare filters
            $filter = array (
                'custom_id'     => 'show_partial_weeks',
                'name'          => 'show_partial_weeks',
                'type'          => 'checkbox',
                'option_value'  => 1,
                'label'         => $this->i18n('reports_show_partial_weeks'),
                'help'          => $this->i18n('reports_show_partial_weeks_help'),
                'disabled'      => true,
                'hidden'        => true
            );
            $filters['show_partial_weeks'] = $filter;

            return $filters;
        }

        function processDependentFilters(&$filters) {
            if (is_array($filters['show_table']['value']) && in_array('activities_not_completed', $filters['show_table']['value'])) {
                $filters['show_partial_weeks']['hidden'] = false;
                $filters['show_partial_weeks']['disabled'] = false;
            }

            return $filters;
        }
    }
?>