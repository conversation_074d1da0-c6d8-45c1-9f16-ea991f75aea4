{if !$reports_additional_options.failed}
{literal}
<style type="text/css">
    td > span.green::before, td > span.red::before {
      font-size: 14px;
      font-weight: bolder;
      padding: 0 2px;
    }
    td > span.green::before {
      content: "\2191";
    }
    td > span.red::before {
      content: "\2193";
    }
</style>
{/literal}
<table class="reports_table nowrap">
  <tr class="reports_title_row">
    {capture assign='grouping_colspan'}{if is_array($reports_results.group_by)}{$reports_results.group_by|@count}{else}0{/if}{/capture}
    {if $reports_results.group_by.customer}
    <th width="200" rowspan="2" style="width: 200px;">{#reports_th_customer#|escape}</th>
    {/if}
    {if $reports_results.group_by.article}
    <th width="200" rowspan="2" style="width: 200px;">{#reports_th_article_name#|escape}</th>
    {/if}
    {capture assign='period_length'}{if is_array($reports_results.period1.months)}{$reports_results.period1.months|@count}{else}0{/if}{/capture}
    {math assign='period_colspan' equation='a+2' a=$period_length}
    {math assign='period_col_width' equation='80*a' a=$period_colspan}
    <th colspan="{$period_colspan}" style="background-color: #d9d9d9; width: {$period_col_width}px;">{$reports_results.period1.year_caption|escape}</th>
    <th colspan="{$period_colspan}" style="background-color: #a6a6a6; width: {$period_col_width}px;">{$reports_results.period2.year_caption|escape}</th>
    <th width="80" rowspan="2">%</th>
  </tr>
  <tr class="reports_title_row">
    {foreach from=$reports_results.period1.months item='month'}
    <th width="80">{$month|escape}</th>
    {/foreach}
    <th width="80">{#reports_th_subtotal_with_discount#|escape}</th>
    <th width="80">{#reports_th_subtotal_with_vat_with_discount#|escape}</th>
    {foreach from=$reports_results.period2.months item='month'}
    <th width="80">{$month|escape}</th>
    {/foreach}
    <th width="80">{#reports_th_subtotal_with_discount#|escape}</th>
    <th width="80">{#reports_th_subtotal_with_vat_with_discount#|escape}</th>
  </tr>
  {foreach from=$reports_results.data item='record' key='rk' name='ri'}
    <tr>
      {if $reports_results.group_by.customer}
      <td style="white-space: normal;"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$record.customer}" target="_blank" title="{#view#|escape}: {$record.customer_name|escape}">{$record.customer_name|escape}</a></td>
      {/if}
      {if $reports_results.group_by.article}
      <td style="white-space: normal;">{if !empty($record.gt2_article_name)}<span class="help" {help label_content=#reports_th_gt2_article_name# text_content=$record.gt2_article_name|escape|mb_truncate:500|nl2br popup_only=true}>&nbsp;</span>{/if}{$record.article_name|escape}</td>
      {/if}
      {foreach from=$record.period1.months item='month_amount'}
      <td class="hright">{$month_amount|number_format:2:".":" "|escape|default:"0.00"}</td>
      {/foreach}
      <td class="hright">{$record.period1.subtotal_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
      <td class="hright">{$record.period1.subtotal_with_vat_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
      {foreach from=$record.period2.months item='month_amount'}
      <td class="hright">{$month_amount|number_format:2:".":" "|escape|default:"0.00"}</td>
      {/foreach}
      <td class="hright">{$record.period2.subtotal_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
      <td class="hright">{$record.period2.subtotal_with_vat_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
      <td class="hcenter strong">
        {capture assign='difference_class'}{if $record.difference gt 0}green{elseif $record.difference lt 0}red{/if}{/capture}
        <span class="{$difference_class}">{if $record.difference === ''}-{else}{$record.difference|@abs|number_format:0:".":" "|escape|default:"0.00"}{/if}</span>
      </td>
    </tr>
  {foreachelse}
  <tr class="t_odd">
    <td class="error" colspan="{math equation='a+2*b+1' a=$grouping_colspan b=$period_colspan}">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
  <tr class="reports_title_row">
    {if $grouping_colspan}
    <td colspan="{$grouping_colspan}">{#reports_th_total#|escape}</td>
    {/if}
    {foreach from=$reports_results.totals.period1.months item='month_amount'}
    <td class="hright">{$month_amount|number_format:2:".":" "|escape|default:"0.00"}</td>
    {/foreach}
    <td class="hright">{$reports_results.totals.period1.subtotal_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
    <td class="hright">{$reports_results.totals.period1.subtotal_with_vat_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
    {foreach from=$reports_results.totals.period2.months item='month_amount'}
    <td class="hright">{$month_amount|number_format:2:".":" "|escape|default:"0.00"}</td>
    {/foreach}
    <td class="hright">{$reports_results.totals.period2.subtotal_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
    <td class="hright">{$reports_results.totals.period2.subtotal_with_vat_with_discount|number_format:2:".":" "|escape|default:"0.00"}</td>
    <td class="hcenter">
      {capture assign='difference_class'}{if $reports_results.totals.difference gt 0}green{elseif $reports_results.totals.difference lt 0}red{/if}{/capture}
      <span class="{$difference_class}">{if $reports_results.totals.difference === ''}-{else}{$reports_results.totals.difference|@abs|number_format:0:".":" "|escape|default:"0.00"}{/if}</span>
    </td>
  </tr>
</table>
{if $reports_additional_options.chart_1}
  {include file="chart.html" chart=$reports_additional_options.chart_1}
{/if}
{/if}
