<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <table border="1">
    {if $reports_additional_options.table eq 'trademark'}
      <tr>
        <th>{#reports_trademark_th_trademark#}</th>
        <th>{#reports_trademark_th_model#}</th>
        <th>{#reports_trademark_th_equipment#}</th>
        <th>{#reports_trademark_th_orders_count#}</th>
      </tr>
      {foreach from=$reports_results.trademark item='record'}
        <tr>
          <td>{$record.tm_name|escape|default:"&nbsp;"}</td>
          <td>{$record.product_name|escape|default:"&nbsp;"}</td>
          <td>{$record.equipment_name|escape|default:"&nbsp;"}</td>
          <td>{$record.orders_count|escape|default:"&nbsp;"}</td>
        </tr>
      {/foreach}
    {elseif $reports_additional_options.table eq 'equipment'}
      <tr>
        <th>{#reports_equipment_th_equipment#}</th>
        <th>{#reports_equipment_th_orders_count#}</th>
      </tr>
      {foreach from=$reports_results.equipment item='record'}
        <tr>
          <td>{$record.equipment_name|escape|default:"&nbsp;"}</td>
          <td>{$record.orders_count|escape|default:"&nbsp;"}</td>
        </tr>
      {/foreach}
    {elseif $reports_additional_options.table eq 'client'}
      <tr>
        <th>{#reports_client_th_client#}</th>
        <th>{#reports_client_th_orders_count#}</th>
      </tr>
      {foreach from=$reports_results.client item='record'}
        <tr>
          <td>{$record.customer_name|escape|default:"&nbsp;"}</td>
          <td>{$record.orders_count|escape|default:"&nbsp;"}</td>
        </tr>
      {/foreach}
    {elseif $reports_additional_options.table eq 'equipment_group'}
      <tr>
        <th>{#reports_equipment_group_th_count_warranty#}</th>
        <td>{$reports_additional_options.equipment_group_stat.count_repair_garan_yes|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <th>{#reports_equipment_group_th_count_no_warranty#}</th>
        <td>{$reports_additional_options.equipment_group_stat.count_repair_garan_no|escape|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <th>{#reports_equipment_group_th_count_replaced#}</th>
        <td>{$reports_additional_options.equipment_group_stat.count_replaced|escape|default:"&nbsp;"}</td>
      </tr>
{*
      <tr>
        <th>{#reports_equipment_group_th_most_common_tm_name#}</th>
        <td>
          {foreach from=$reports_additional_options.equipment_group_stat.most_common_tm key='tm_id' item='tm' name='most_common_tm'}
            {if $tm_id}
              {if $smarty.foreach.most_common_tm.first}
                {assign var='first_tm_count' value=$tm.count}
              {elseif $first_tm_count eq $tm.count}
                <br />
              {else}
                {php}break;{/php}
              {/if}
              {$tm.name|escape|default:"&nbsp;"}
            {/if}
          {/foreach}
        </td>
      </tr>
*}
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <th>{#reports_equipment_group_th_order_num#}</th>
        <th>{#reports_equipment_group_th_group#}</th>
        <th>{#reports_equipment_group_th_date_acceptance#}</th>
        <th>{#reports_equipment_group_th_status_date#}</th>
        <th>{#reports_equipment_group_th_status_name#}</th>
        <th>{#reports_equipment_group_th_equipment#}</th>
        <th>{#reports_equipment_group_th_type_equipment#}</th>
        <th>{#reports_equipment_group_th_trademark#}</th>
        <th>{#reports_equipment_group_th_problem_client#}</th>
        <th>{#reports_equipment_group_th_problem_technician#}</th>
      </tr>
      {foreach from=$reports_results.equipment_group key='order_id' item='record'}
        <tr>
          <td>{$record.order_num|escape|default:"&nbsp;"}</td>
          <td>{$record.group_name|escape|default:"&nbsp;"}</td>
          <td>{$record.date_acceptance|date_format:#date_short#|escape|default:"&nbsp;"}</td>
          <td>{$record.substatus_modified|date_format:#date_short#|escape|default:"&nbsp;"}</td>
          <td {if $reports_additional_options.order_statuses_groups[$record.substatus]} title="{$reports_additional_options.order_statuses_groups[$record.substatus]}"{/if}>
            {$record.substatus_name|escape|default:"&nbsp;"}
          </td>
          <td>{$record.equipment_name|escape|default:"&nbsp;"}</td>
          <td>{$record.product_type_name|escape|default:"&nbsp;"}</td>
          <td>{$record.tm_name|escape|default:"&nbsp;"}</td>
          <td>
            {if !empty($record.why_repair_client)}
              {assign var='record_why_repair_client' value="|"|explode:$record.why_repair_client}
              {foreach from=$record_why_repair_client item='wrc' name='wrc'}
                {$reports_additional_options.why_repair_client[$wrc]}{if !$smarty.foreach.wrc.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td>
            {if !empty($record.why_repair_priemna)}
              {assign var='record_why_repair_priemna' value="|"|explode:$record.why_repair_priemna}
              {foreach from=$record_why_repair_priemna item='wrp' name='wrp'}
                {$reports_additional_options.why_repair_priemna[$wrp]}{if !$smarty.foreach.wrp.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
        </tr>
      {/foreach}
    {elseif $reports_additional_options.table eq 'incomes_expenses'}
      <tr>
        <th>{#reports_incomes_expenses_th_incomes_expenses#}</th>
        <th>{#reports_incomes_expenses_th_total#}</th>
      </tr>
      <tr>
        <th>{#reports_incomes_expenses_th_total_incomes_no_vat#}</th>
        <td>{$reports_additional_options.incomes_expenses_totals.incomes_no_vat|string_format:"%.2f"|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <th>{#reports_incomes_expenses_th_total_incomes_with_vat#}</th>
        <td>{$reports_additional_options.incomes_expenses_totals.incomes_with_vat|string_format:"%.2f"|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <th>{#reports_incomes_expenses_th_total_expenses_no_vat#}</th>
        <td>{$reports_additional_options.incomes_expenses_totals.expenses_no_vat|string_format:"%.2f"|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <th>{#reports_incomes_expenses_th_total_expenses_with_vat#}</th>
        <td>{$reports_additional_options.incomes_expenses_totals.expenses_with_vat|string_format:"%.2f"|default:"&nbsp;"}</td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <th>{#reports_type_incomes_expenses_th_kind_income_expense#}</th>
        <th>{#reports_type_incomes_expenses_th_total_quantity#}</th>
        <th>{#reports_type_incomes_expenses_th_total_subtotal#}</th>
        <th>{#reports_type_incomes_expenses_th_average_price#}</th>
      </tr>
      {foreach from=$reports_results.type_incomes_expenses key='nom_id' item='record'}
        <tr>
          <td>{$record.nom_name|escape|default:"&nbsp;"}</td>
          <td>{$record.total_quantity|string_format:"%.0f"|default:"0"}</td>
          <td>{$record.total_subtotal|string_format:"%.2f"|default:"0.00"}</td>
          <td>{$record.average_price|string_format:"%.2f"|default:"0.00"}</td>
        </tr>
      {/foreach}
    {elseif $reports_additional_options.table eq 'repairs'}
      <tr>
        <th>{#reports_total_orders_unfinished#}</th>
        <td>{$reports_results.repairs.total_orders.unfinished|escape|default:"&nbsp;"} {#reports_pcs#}</td>
      </tr>
      <tr>
        <th>{#reports_total_orders_successfully_repaird#}</th>
        <td>{$reports_results.repairs.total_orders.successfully_repaird|escape|default:"&nbsp;"} {#reports_pcs#}</td>
      </tr>
      <tr>
        <th>{#reports_total_orders_unsuccessfully_repaird#}</th>
        <td>{$reports_results.repairs.total_orders.unsuccessfully_repaird|escape|default:"&nbsp;"} {#reports_pcs#}</td>
      </tr>
      <tr>
        <th>{#reports_total_orders_replaced#}</th>
        <td>{$reports_results.repairs.total_orders.replaced|escape|default:"&nbsp;"} {#reports_pcs#}</td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      {assign var='have_record' value=true}
      {if empty($reports_results.repairs.most.shortest_successfully_repaird.order_id)}
        {assign var='have_record' value=false}
      {/if}
      <tr>
        <th>{#reports_shortest_successfully_repaird#}</th>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.shortest_successfully_repaird.days|escape|default:"&nbsp;"}
            {if $reports_results.repairs.most.shortest_successfully_repaird.days eq 1}
              {#reports_day#}
            {else}
              {#reports_days#}
            {/if}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {foreach from=$reports_results.repairs.most.shortest_successfully_repaird.technicians key='technician_id' item='technician_name' name='technician'}
              {$technician_name|escape|default:"&nbsp;"}
              {if !$smarty.foreach.technician.last}
                <br />
              {/if}
            {/foreach}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.shortest_successfully_repaird.equipment_name|escape|default:"&nbsp;"}
          {else}
            -
          {/if}
        </td>
      </tr>
      {assign var='have_record' value=true}
      {if empty($reports_results.repairs.most.longest_successfully_repaird.order_id)}
        {assign var='have_record' value=false}
      {/if}
      <tr>
        <th>{#reports_longest_successfully_repaird#}</th>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.longest_successfully_repaird.days|escape|default:"&nbsp;"}
            {if $reports_results.repairs.most.longest_successfully_repaird.days eq 1}
              {#reports_day#}
            {else}
              {#reports_days#}
            {/if}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {foreach from=$reports_results.repairs.most.longest_successfully_repaird.technicians key='technician_id' item='technician_name' name='technician'}
              {$technician_name|escape|default:"&nbsp;"}
              {if !$smarty.foreach.technician.last}
                <br />
              {/if}
            {/foreach}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.longest_successfully_repaird.equipment_name|escape|default:"&nbsp;"}
          {else}
            -
          {/if}
        </td>
      </tr>
      {assign var='have_record' value=true}
      {if empty($reports_results.repairs.most.shortest_unsuccessfully_repaird.order_id)}
        {assign var='have_record' value=false}
      {/if}
      <tr>
        <th>{#reports_shortest_unsuccessfully_repaird#}</th>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.shortest_unsuccessfully_repaird.days|escape|default:"&nbsp;"}
            {if $reports_results.repairs.most.shortest_unsuccessfully_repaird.days eq 1}
              {#reports_day#}
            {else}
              {#reports_days#}
            {/if}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {foreach from=$reports_results.repairs.most.shortest_unsuccessfully_repaird.technicians key='technician_id' item='technician_name' name='technician'}
              {$technician_name|escape|default:"&nbsp;"}
              {if !$smarty.foreach.technician.last}
                <br />
              {/if}
            {/foreach}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.shortest_unsuccessfully_repaird.equipment_name|escape|default:"&nbsp;"}
          {else}
            -
          {/if}
        </td>
      </tr>
      {assign var='have_record' value=true}
      {if empty($reports_results.repairs.most.longest_unsuccessfully_repaird.order_id)}
        {assign var='have_record' value=false}
      {/if}
      <tr>
        <th>{#reports_longest_unsuccessfully_repaird#}</th>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.longest_unsuccessfully_repaird.days|escape|default:"&nbsp;"}
            {if $reports_results.repairs.most.longest_unsuccessfully_repaird.days eq 1}
              {#reports_day#}
            {else}
              {#reports_days#}
            {/if}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {foreach from=$reports_results.repairs.most.longest_unsuccessfully_repaird.technicians key='technician_id' item='technician_name' name='technician'}
              {$technician_name|escape|default:"&nbsp;"}
              {if !$smarty.foreach.technician.last}
                <br />
              {/if}
            {/foreach}
          {else}
            -
          {/if}
        </td>
        <td>
          {if $have_record}
            {$reports_results.repairs.most.longest_unsuccessfully_repaird.equipment_name|escape|default:"&nbsp;"}
          {else}
            -
          {/if}
        </td>
      </tr>
      <tr>
        <td>&nbsp;</td>
      </tr>
      <tr>
        <th>{#reports_technicians#}</th>
        <th>{#reports_technicians_unfinished#}</th>
        <th>{#reports_technicians_successfully_repaird#}</th>
        <th>{#reports_technicians_unsuccessfully_repaird#}</th>
      </tr>
      {foreach from=$reports_results.repairs.technicians item='technician'}
        <tr>
          {capture assign='temp_count1'}{if is_array($technician.unfinished_equipments)}{$technician.unfinished_equipments|@count}{else}0{/if}{/capture}
          {capture assign='temp_count2'}{if is_array($technician.successfully_repaird_equipments)}{$technician.successfully_repaird_equipments|@count}{else}0{/if}{/capture}
          {capture assign='temp_count3'}{if is_array($technician.unsuccessfully_repaird_equipments)}{$technician.unsuccessfully_repaird_equipments|@count}{else}0{/if}{/capture}
          {capture assign='max_technician_equipments_count'}{math equation="max(x, y, z)" x=$temp_count1" y=$temp_count2 z=$temp_count3}{/capture}
          <td {if $max_technician_equipments_count} rowspan="{$max_technician_equipments_count+1}"{/if}>{$technician.name}</td>
          <td>{$technician.unfinished} {#reports_technicians_equipments_count_total#|escape}</td>
          <td>{$technician.successfully_repaird} {#reports_technicians_equipments_count_total#|escape}</td>
          <td>{$technician.unsuccessfully_repaird} {#reports_technicians_equipments_count_total#|escape}</td>
        </tr>
        {section name='technician_equipment' start=0 loop=$max_technician_equipments_count step=1}
          <tr>
            <td>
              {if $technician.unfinished_equipments[$smarty.section.technician_equipment.index].orders_count}
                {$technician.unfinished_equipments[$smarty.section.technician_equipment.index].orders_count|escape} {#reports_technicians_equipments_count#|escape} {$technician.unfinished_equipments[$smarty.section.technician_equipment.index].name|escape}
              {/if}
            </td>
            <td>
              {if $technician.successfully_repaird_equipments[$smarty.section.technician_equipment.index].orders_count}
                {$technician.successfully_repaird_equipments[$smarty.section.technician_equipment.index].orders_count|escape} {#reports_technicians_equipments_count#|escape} {$technician.successfully_repaird_equipments[$smarty.section.technician_equipment.index].name|escape}
              {/if}
            </td>
            <td>
              {if $technician.unsuccessfully_repaird_equipments[$smarty.section.technician_equipment.index].orders_count}
                {$technician.unsuccessfully_repaird_equipments[$smarty.section.technician_equipment.index].orders_count|escape} {#reports_technicians_equipments_count#|escape} {$technician.unsuccessfully_repaird_equipments[$smarty.section.technician_equipment.index].name|escape}
              {/if}
            </td>
          </tr>
        {/section}
      {/foreach}
    {/if}
  </table>
</body>
</html>