<?php
    Class Quick_Search Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $sql['select']  =   'SELECT d.id, d.full_num, d.custom_num, d.active, d.deleted_by, di18n.description, di18n.notes, ' . "\n" .
                                '  "' . $model_lang . '" as model_lang, dti18n.name as type_name, ' . "\n" .
                                '  dt.direction as direction, DATE_FORMAT(d.added, "%Y-%m-%d") as date_added, ' . "\n" .
                                '  CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name ' . "\n";

            //from clause
            $sql['from'] =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                            'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                            '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                            '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                            //relate to document types
                            'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                            '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                            //relate to customers
                            'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n";

            $where = array();

            $where[] = 'd.deleted_by=0';
            $where[] = 'd.active=1';
            if (! empty($filters['document_num'])) {
                $where[] = 'd.full_num LIKE "%' . $filters['document_num'] . '%"';
            }
            if (! empty($filters['document_custom_num'])) {
                $where[] = 'd.custom_num LIKE "%' . $filters['document_custom_num'] . '%"';
            }
            if (! empty($filters['customer'])) {
                $where[] = 'd.customer="' . $filters['customer'] . '"';
            }
            if (! empty($filters['document_type'])) {
                $where[] = 'd.type="' . $filters['document_type'] . '"';
            }
            if (! empty($filters['description'])) {
                $all_words_description = explode(" ", $filters['description']);
                $description_search = array();
                foreach($all_words_description as $word){
                    if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                        $wrapped_word = \Nzoom\Db\DbCapabilities::mySqlWordRegExpWrap($word);
                        $description_search[] = '(di18n.description REGEXP \'' . General::slashesEscape($wrapped_word) . '\')';
                    }
                }
                if (! empty($description_search)) {
                    $where[] = '(' . implode(' OR ', $description_search) . ')';
                }
            }
            if (! empty($filters['notes'])) {
                $all_words_notes = explode(" ", $filters['notes']);
                $notes_search = array();
                foreach($all_words_notes as $word) {
                    if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                        $wrapped_word = \Nzoom\Db\DbCapabilities::mySqlWordRegExpWrap($word);
                        $notes_search[] = '(di18n.notes REGEXP \'' . General::slashesEscape($wrapped_word) . '\')';
                    }
                }
                if (! empty($notes_search)) {
                    $where[] = '(' . implode(' OR ', $notes_search) . ')';
                }
            }

            $sql['where'] = 'WHERE ' . implode(' AND ', $where);

            $union_queries = array();
            $archive_query = $sql;
            $sql['select'] .= ', 0 as archive' . "\n";
            $archive_query['select'] .= ', 1 as archive' . "\n";

            //from clause
            $archive_query['from'] = 'FROM ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                     '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                     '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                                     //relate to document types
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                     '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                     //relate to customers
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                     '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n";

            //search basic details with current lang parameters
            $union_queries[] = implode("\n", $sql);
            $union_queries[] = implode("\n", $archive_query);

            $general_query = '(' . implode(') UNION (', $union_queries) . ') ORDER BY id DESC';
            $records = $registry['db']->GetAll($general_query);

            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                //no pagination required return only the models
                $results = $records;
            }

            return $results;
        }
    }
?>
