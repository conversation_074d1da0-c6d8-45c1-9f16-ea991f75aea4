<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller
{

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute()
    {
        switch ($this->action) {
            case 'ajax_load_pagination':
                $this->_loadPagination();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Function to prepare the main report settings
     */
    public function prepareReportSettings() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        $this->report = $report;

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);

        return true;
    }

    /*
     * AJAX function to paginate the tenders
     */
    private function _loadPagination() {
        $this->prepareReportSettings();

        $params = array();
        $params['nomenclature_id'] = $this->registry['request']->get('nomenclature_id');
        $params['market_id'] = $this->registry['request']->get('market_id');
        $params['region_id'] = $this->registry['request']->get('region_id');
        $params['customer_id'] = $this->registry['request']->get('customer_id');
        $params['competitor_id'] = $this->registry['request']->get('competitor_id');

        if ($this->registry['request']->get('page')) {
            $page = $this->registry['request']->get('page');
        } else {
            $page = 1;
        }

        $call_function = '';
        $template = '';
        $template_var_name = '';

        if ($this->registry['request']->get('records_type_pagination') == 'tenders') {
            $call_function = 'getProductTenders';
            $template = '_tenders_list.html';
            $template_var_name = 'tenders_list';
        } elseif ($this->registry['request']->get('records_type_pagination') == 'offers') {
            $call_function = 'getProductOffers';
            $template = '_offers_list.html';
            $template_var_name = 'offers_list';
        } elseif ($this->registry['request']->get('records_type_pagination') == 'contracts') {
            $call_function = 'getProductContracts';
            $template = '_contracts_list.html';
            $template_var_name = 'contracts_list';
        }

        // get the data which have to be refreshed
        if ($call_function) {
            $related_report = 'optix_products';
            require_once PH_MODULES_DIR . 'reports/plugins/' . $related_report . '/custom.report.query.php';
            Reports::getReportSettings($this->registry, $related_report);

            $i18n_file = sprintf('%s%s%s%s%s%s',
                PH_MODULES_DIR,
                'reports/plugins/',
                $related_report,
                '/i18n/',
                $this->registry['lang'],
                '/reports.ini');

            $report_name_elements = explode('_', $related_report);
            foreach ($report_name_elements as $key => $element) {
                $report_name_elements[$key] = ucfirst($element);
            }
            $report_class_name = implode ('_', $report_name_elements);

            // prepare the table which have to be reloaded
            list($records_list, $pagination) = $report_class_name::$call_function($this->registry, $params, $page);

            $viewer = new Viewer($this->registry);
            $viewer->setFrameset('frameset_blank.html');
            $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $related_report . '/';
            $viewer->template = $template;
            $viewer->loadCustomI18NFiles(array($this->report_lang_file, $i18n_file));
            $viewer->data[$template_var_name] = $records_list;
            $viewer->data['templatesDirPlugin'] = PH_MODULES_DIR . "reports/plugins/";
            $viewer->data['pagination'] = $pagination;
            $viewer->data['report_type'] = $this->report;
            $viewer->data['report_type_source'] = $related_report;
            $viewer->data['product_id'] = $params['nomenclature_id'];
            $viewer->data['market_id'] = $params['market_id'];
            $viewer->data['region_id'] = $params['region_id'];
            $viewer->data['customer_id'] = $params['customer_id'];
            $viewer->data['competitor_id'] = $params['competitor_id'];
            $template = $viewer->fetch();

            print $template;
        } else {
            print '';
        }

        exit;
    }
}
?>
