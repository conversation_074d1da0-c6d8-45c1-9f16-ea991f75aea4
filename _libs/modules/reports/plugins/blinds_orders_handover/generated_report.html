<table class="reports_table" style="min-width: 1000px;">
  <tr class="reports_title_row" style="white-space: nowrap;">
    <th>{#reports_filter_customer#|escape}</th>
    {counter name='total_orders' assign='total_orders' start=0}
    {foreach from=$reports_results item='order'}
      {if !$order.complete}{counter name='total_orders' assign='total_orders'}{/if}
      {if $order.type eq $smarty.const.ORDER_TYPE_WINDOWS && $order.gift_blinds_num_remaining gt 0}
        {counter name='total_orders' assign='total_orders'}
      {/if}
    {/foreach}
    <th>
      {if $total_orders}
        <div class="floatl hright">
          {assign var='selected_items' value=$smarty.session.selected_items[$pagination.session_param]}
          {if $report_filters.customer.value}
            {include file="`$theme->templatesDir`_select_items.html"
                     pages=1
                     total=$total_orders
                     session_param=$session_param|default:$pagination.session_param
                     onclick='toggleOrderSelected(this);'
            }
          {/if}
          {assign var='selected_items_count' value='0'}
          {if $selected_items.ids}
            {capture assign='selected_items_count'}{if is_array($selected_items.ids)}{$selected_items.ids|@count}{else}0{/if}{/capture}
          {elseif $selected_items.select_all}
            {capture assign='ignore_ids'}{if is_array($selected_items.ignore_ids)}{$selected_items.ignore_ids|@count}{else}0{/if}{/capture}
            {math equation="all - ignored" all=$total_orders ignored=$ignore_ids assign='selected_items_count'}
          {/if}
          <span id="selectedItemsCount_1" class="selected_items_span{if $selected_items_count} green{/if}">{$selected_items_count}</span>/{$total_orders}
        </div>
      {/if}
      {#reports_filter_order_num#|escape}
    </th>
    <th>{#reports_filter_custom_num#|escape}</th>
    <th>{#reports_filter_date#|escape}</th>
    <th>{#reports_filter_deadline#|escape}</th>
    <th>{#reports_filter_kind_order#|escape}</th>
    <th>{#reports_order_quantity#|escape}</th>
    <th>{#reports_order_number#|escape}</th>
    <th>{#reports_accessories#|escape}</th>
    <th>{#reports_rest_total#|escape}</th>
  </tr>
  {if $total_orders}
  <tr class="reports_title_row">
    <th colspan="10" class="hcenter nopadding">
      <button class="button pointer reports_export_button{if !$selected_items_count} hidden{/if}" style="width: 400px; margin: 5px;" type="button" onclick="showAddHandoverForm(this);" id="handover_btn">{#reports_add_handover#|escape}</button>
    </th>
  </tr>
  {/if}

  {array assign='opt_full' option_value='full' label=#reports_accessories_full#}
  {array assign='opt_partial' option_value='partial' label=#reports_accessories_partial#}
  {array assign='accessories_options' 1=$opt_full 2=$opt_partial}
  {assign var='order_customer' value=''}
  {assign var='gift_kind' value=TYPE_OPT_`$smarty.const.ORDER_TYPE_BLINDS`}

  {foreach from=$reports_results item='order' key='id' name='ri'}
    {capture assign='order_selected'}{if !$order.complete && (!empty($selected_items.ids) && in_array(strval($id), $selected_items.ids) || (!empty($selected_items.select_all) && @!in_array($id, $selected_items.ignore_ids)))}1{else}0{/if}{/capture}
    {if $order_selected}{assign var='order_customer' value=$order.customer}{/if}
    {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign='row_class'}
  <tr class="{$row_class}{if $order_selected} selected{/if}">
    <td>
      <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.customer}">
        {$order.customer_name|escape}
      </a>
    </td>
    <td>
      {if $total_orders}
      <div class="floatl" style="width: 30px;">
        {if !$order.complete}
        <input
            onclick="if (toggleOrderSelected(this)) sendIds(params = {ldelim}
                the_element: this,
                module: '{$module}',
                controller: 'reports',
                action: '{$action}',
                session_param: '{$session_param|default:$pagination.session_param}',
                total: {$total_orders}
               {rdelim});"
            type="checkbox"
            name="items[]"
            id="items_{$id}"
            value="{$id}"
            class="{$order.customer}"
            style="margin: 0 4px;"
            title="{#check_to_include#|escape}"
            {if $order_selected}checked="checked"{/if}
        />
        {else}
          &nbsp;
        {/if}
      </div>
      {/if}
      <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$id}">
        {$order.full_num|escape}
      </a>
    </td>
    <td>{$order.custom_num|escape|default:'&nbsp;'}</td>
    <td>{$order.date|date_format:#date_short#|escape}</td>
    <td>{$order.deadline|date_format:#date_short#|escape}</td>
    <td>{$order.name|default:$order.type_name|escape}</td>
    <td class="hright">
      {$order.name_order|default:'&nbsp;'}
    </td>
    <td class="hright">
      {capture assign='field_disabled'}{if !$order_selected}1{/if}{/capture}
      {capture assign='disabled_class_name'}{if !$order_selected}input_inactive{/if}{/capture}
      {if $order.num_total === '' || $order.num_remaining lte 0}
        {$order.num_total|default:'&nbsp;'}
      {else}
        {include file='input_hidden.html'
                 standalone=true
                 name='trademark'
                 custom_id='order_trademark'
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.trademark
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_ORDER_VAR
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.full_num
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_ORDER_ID_VAR
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$id
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_CUSTOM_NUM_VAR
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.custom_num
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_KIND_VAR
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.kind
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_QUANTITY_VAR
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.name_order|default:0
        }
        {include file='input_text.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_NUMBER_VAR
                 custom_id='num_total'
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.num_remaining|default:0
                 custom_class="short hright `$disabled_class_name`"
                 restrict='insertOnlyFloats'
                 onblur='validateHandoverQuantity(this);'
                 label=#reports_order_number#
        }
        {include file='input_hidden.html'
                 standalone=true
                 name="max_`$smarty.const.HANDOVER_NUMBER_VAR`"
                 index=$id
                 disabled=true
                 eq_indexes=true
                 value=$order.num_remaining|default:0
                 custom_class='system_disabled'
        }
      {/if}
    </td>
    <td>
      {if !$order.num_accessories}
        {#reports_accessories_none#|escape}
      {elseif $order.quantity_accessories lte 0}
        {#reports_filter_handover_status_full#|escape}
      {else}
        {include file='input_dropdown.html'
                 standalone=true
                 name='accessories'
                 index=$id
                 eq_indexes=true
                 disabled=$field_disabled
                 value='full'
                 custom_class="`$disabled_class_name`"
                 required=true
                 options=$accessories_options
                 onchange='toggleAccessories(this);'
                 label=#reports_accessories#
        }
      {/if}
    </td>
    <td class="hright">{$order.rest_total|default:'&nbsp;'}</td>
  </tr>
  {if $order.type eq $smarty.const.ORDER_TYPE_WINDOWS && $order.gift_blinds_name_order gt 0}
    {capture assign='order_gift_id'}{$id}_gift{/capture}
    {capture assign='order_selected'}{if $order.gift_blinds_num_remaining gt 0 && (!empty($selected_items.ids) && in_array($order_gift_id, $selected_items.ids) || (!empty($selected_items.select_all) && @!in_array($order_gift_id, $selected_items.ignore_ids)))}1{else}0{/if}{/capture}
    {if $order_selected}{assign var='order_customer' value=$order.customer}{/if}
  <tr class="{$row_class}{if $order_selected} selected{/if}">
    <td>
      <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.customer}">
        {$order.customer_name|escape}
      </a>
    </td>
    <td>
      {if $total_orders}
      <div class="floatl" style="width: 30px;">
        {if $order.gift_blinds_num_remaining gt 0}
        <input
            onclick="if (toggleOrderSelected(this)) sendIds(params = {ldelim}
                the_element: this,
                module: '{$module}',
                controller: 'reports',
                action: '{$action}',
                session_param: '{$session_param|default:$pagination.session_param}',
                total: {$total_orders}
               {rdelim});"
            type="checkbox"
            name="items[]"
            id="items_{$order_gift_id}"
            value="{$order_gift_id}"
            class="{$order.customer}"
            style="margin: 0 4px;"
            title="{#check_to_include#|escape}"
            {if $order_selected}checked="checked"{/if}
        />
        {else}
          &nbsp;
        {/if}
      </div>
      {/if}
      <a target="_blank" href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$id}">
        {$order.gift_blinds_full_num|escape}
      </a>
    </td>
    <td>{$order.custom_num|escape|default:'&nbsp;'}</td>
    <td>{$order.date|date_format:#date_short#|escape}</td>
    <td>{$order.deadline|date_format:#date_short#|escape}</td>
    <td>{#reports_horizontal_blinds_gift#|escape}</td>
    <td class="hright">
      {$order.gift_blinds_name_order|default:'&nbsp;'}
    </td>
    <td class="hright">
      {capture assign='field_disabled'}{if !$order_selected}1{/if}{/capture}
      {capture assign='disabled_class_name'}{if !$order_selected}input_inactive{/if}{/capture}
      {if $order.gift_blinds_num_total === '' || $order.gift_blinds_num_remaining lte 0}
        {$order.gift_blinds_num_total|default:'&nbsp;'}
      {else}
        {include file='input_hidden.html'
                 standalone=true
                 name='trademark'
                 custom_id='order_trademark'
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.trademark
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_ORDER_VAR
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.gift_blinds_full_num
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_ORDER_ID_VAR
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$id
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_CUSTOM_NUM_VAR
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.custom_num
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_KIND_VAR
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$smarty.const.$gift_kind
        }
        {include file='input_hidden.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_QUANTITY_VAR
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.gift_blinds_name_order|default:0
        }
        {include file='input_text.html'
                 standalone=true
                 name=$smarty.const.HANDOVER_NUMBER_VAR
                 custom_id='num_total'
                 index=$order_gift_id
                 eq_indexes=true
                 disabled=$field_disabled
                 value=$order.gift_blinds_num_remaining|default:0
                 custom_class="short hright `$disabled_class_name`"
                 restrict='insertOnlyFloats'
                 onblur='validateHandoverQuantity(this);'
                 label=#reports_order_number#
        }
        {include file='input_hidden.html'
                 standalone=true
                 name="max_`$smarty.const.HANDOVER_NUMBER_VAR`"
                 index=$order_gift_id
                 disabled=true
                 eq_indexes=true
                 value=$order.gift_blinds_num_remaining|default:0
                 custom_class='system_disabled'
        }
      {/if}
    </td>
    <td>{#reports_accessories_none#|escape}</td>
    <td class="hright">&nbsp;</td>
  </tr>
  {/if}
  {foreachelse}
  <tr class="t_odd1 t_odd2">
    <td class="error" colspan="10">{#no_items_found#|escape}</td>
  </tr>
  {/foreach}
  {if is_array($reports_results) && $reports_results|@count}
  <tr>
    <td colspan="6" class="hright strong">{#total#|escape}:</td>
    <td class="hright strong">{$reports_additional_options.totals.name_order|default:'&nbsp;'}</td>
    <td class="hright">
      <span class="floatl">&nbsp;</span> <span class="strong">{$reports_additional_options.totals.num_total|default:'&nbsp;'}</span><br />
      <span class="floatl">{#reports_filter_handover_status_full#|mb_ucfirst|escape}:&nbsp;</span> <span class="strong">{$reports_additional_options.totals.num_delivered|default:'&nbsp;'}</span><br />
      <span class="floatl">{#reports_filter_handover_status_not#|mb_ucfirst|escape}:&nbsp;</span> <span class="strong">{$reports_additional_options.totals.num_remaining|default:'&nbsp;'}</span><br />
    </td>
    <td colspan="2">&nbsp;</td>
  </tr>
  {/if}
</table>
<br />
{if $total_orders}
  {include file='input_hidden.html'
           standalone=true
           name='customer'
           custom_id='order_customer'
           value=$order_customer
  }
{/if}
