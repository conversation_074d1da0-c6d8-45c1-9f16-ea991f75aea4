<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array ();

            //DEFINE ACTIVE FILTER
            $filter = array(
                'custom_id'     => 'active_customers',
                'name'          => 'active_customers',
                'type'          => 'checkbox_group',
                'required'      => 1,
                'label'         => $this->i18n('reports_active_customers'),
                'help'          => $this->i18n('reports_active_customers'),
                'options'       => array(
                    array(
                        'label'         => $this->i18n('reports_active_customers_active'),
                        'option_value'  => '1'
                    ),
                    array(
                        'label'         => $this->i18n('reports_active_customers_inactive'),
                        'option_value'  => '0'
                    )
                )
            );
            $filters['active_customers'] = $filter;

            //DEFINE SHOW ALL FILTER
            // Check if the current user have access to view the leave requests of all users
            $allow_show_all = (!defined('SHOW_ALL_USERS') || in_array($registry['currentUser']->get('id'), preg_split('/\s*,\s*/', constant('SHOW_ALL_USERS'))));
            if ($allow_show_all) {
                $filter = array(
                    'custom_id'     => 'show_all',
                    'name'          => 'show_all',
                    'type'          => 'checkbox_group',
                    'label'         => $this->i18n('reports_show_all'),
                    'help'          => $this->i18n('reports_show_all'),
                    'options'       => array(
                        array(
                            'label'         => '',
                            'option_value'  => '1'
                        )
                    )
                );
                $filters['show_all'] = $filter;
            }

            return $filters;
        }

        function processDependentFilters(&$filters) {
            $registry = &$GLOBALS['registry'];

            if (empty($filters['active_customers']['value'])) {
                $filters['active_customers']['value'] = array('1');
            }

            return $filters;
        }
    }

?>
