<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      {assign var=total_colspan value=12}
      {if $reports_additional_options.include_discount}
        {math equation='2+x' x=$total_colspan assign='total_colspan'}
      {/if}
      <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
        <tr class="reports_title_row hcenter">
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_manager#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insurer_branch#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_client#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_policy_num#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_payment_num#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_deadline#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_bonus#|escape}</div></td>
          {if $reports_additional_options.include_discount}
            <td class="t_border" style="vertical-align: middle;"><div>{#reports_discount#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div>{#reports_bonus_with_discount#|escape}</div></td>
          {/if}
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_tax#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_bruto_premium#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div>{#reports_balance#|escape}</div></td>
          <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_commission_percentage#|escape}</div></td>
          <td style="vertical-align: middle;"><div style="">{#reports_commission#|escape}</div></td>
        </tr>
        {foreach from=$reports_results item=result name=results}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="t_border">
              {$result.contact_person_name|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {if $result.insurer_name}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.insurer}">{$result.insurer_name|escape|default:"&nbsp;"}</a>{if $result.branch}/{$result.branch_name|escape|default:"&nbsp;"}{/if}
              {else}
                &nbsp;
              {/if}
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.customer}">{$result.customer_name|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=viewtopic&amp;viewtopic={$result.id}" target="_blank">{$result.custom_num|escape|default:"&nbsp;"}</a>
            </td>
            <td class="t_border hright">
              {$result.payment_num|escape|default:"&nbsp;"}
            </td>
            <td class="t_border">
              {$result.deadline_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright">
              {$result.bonus|escape|default:"0.00"|string_format:"%.2f"}
            </td>
            {if $reports_additional_options.include_discount}
              <td class="t_border hright">
                {$result.discount|escape|default:"0.00"|string_format:"%.2f"}
              </td>
              <td class="t_border hright">
                {$result.bonus_with_discount|escape|default:"0.00"|string_format:"%.2f"}
              </td>
            {/if}
            <td class="t_border hright">
              {$result.tax|escape|default:"0.00"|string_format:"%.2f"}
            </td>
            <td class="t_border hright">
              {$result.bruto_premium|escape|default:"0.00"|string_format:"%.2f"}
            </td>
            <td class="t_border hright">
              {$result.balance|escape}
            </td>
            <td class="t_border hright">
              {$result.discount_percentage|escape|default:"0.00"}
            </td>
            <td class="hright">
              {$result.commission|escape|default:"0.00"}
            </td>
          </tr>
        {foreachelse}
          <tr class="{cycle values='t_odd,t_even'}">
            <td class="error" colspan="{$total_colspan}">{#no_items_found#|escape}</td>
          </tr>
        {/foreach}
        <tr>
          <td class="t_footer" colspan="{$total_colspan}"></td>
        </tr>
      </table>
    </td>
  </tr>
</table>
