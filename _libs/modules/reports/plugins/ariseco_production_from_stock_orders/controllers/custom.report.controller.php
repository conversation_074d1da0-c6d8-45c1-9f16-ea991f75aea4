<?php
require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    /**
     * Setttings of the report
     */
    public $settings = array();

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'add_orders':
                $this->_addOrders();
                break;
            default:
                parent::execute();
        }
    }

    public function _addOrders() {
        // Get some basics
        $result = false;
        $registry = $this->registry;
        $messages = $registry['messages'];
        $request = $registry['request'];
        $lang = $registry['lang'];
        $db = $registry['db'];

        // Get the report type
        $report_type = $this->getReportModel()->get('type');

        // Get the report settings
        require_once PH_MODULES_DIR . "reports/plugins/{$report_type}/custom.report.query.php";
        $settings = Ariseco_Production_From_Stock_Orders::getSettings($registry);
        $this->settings = $settings;

        // Load the report's language file
        $registry['translater']->loadFile(PH_MODULES_DIR . "reports/plugins/{$report_type}/i18n/{$lang}/reports.ini");

        // There are no checks if the request data is empty,
        // because it's ensured from the front-end.
        // It is possible to be invalid only if the client is hacking it,
        // but we stick to the GIGO principle
        $request_data = $request->getAll();

        /*
         * Process request data
         */
        $sewings_and_cuts = [
            'sewing'       => [],
            'sent_for_cut' => []
        ];
        $has_ateliers = false;
        $ateliers = [];
        $atelier_external_producers_ids = [];
        if (!empty($request_data['sewing'])) {
            foreach ($request_data['sewing'] as $child_sku_stock_order_index => $sewing) {
                $sewings_and_cuts['sewing'][$child_sku_stock_order_index] = $sewing;
                $atelier_id = $request_data['atelier_external_producer'][$child_sku_stock_order_index];
                $atelier_external_producers_ids[$atelier_id] = $atelier_id;
            }
        }
        if (!empty($request_data['sent_for_cut'])) {
            foreach ($request_data['sent_for_cut'] as $child_sku_stock_order_index => $sent_for_cut) {
                $sewings_and_cuts['sent_for_cut'][$child_sku_stock_order_index] = $sent_for_cut;
            }
        }
        if (!empty($sewings_and_cuts)) {
            $child_sku_ids_list = implode(', ', array_keys($request_data['child_sku_code']));
            $query = "
                SELECT n.id AS idx, n.id
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (n.id IN ({$child_sku_ids_list})
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = 'indication_sets')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.num = 1
                      AND nc.lang = ''
                      AND nc.value = '2')";
            $child_skus_with_indication_sets = $db->GetAssoc($query);

            /*
             * Get which ateliers are third party
             */
            $third_party_ateliers = [];
            if ($atelier_external_producers_ids) {
                $atelier_external_producers_ids_list = implode(', ', $atelier_external_producers_ids);
                $query = "
                    SELECT c.id AS idx, c.id
                      FROM " . DB_TABLE_CUSTOMERS . " AS c
                      JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                        ON (c.id IN ({$atelier_external_producers_ids_list})
                          AND tm.model = 'Customer'
                          AND tm.model_id = c.id
                          AND tm.tag_id = {$settings['cus_atelier_tag_third_party_producers']})";
                $third_party_ateliers = $db->GetAssoc($query);
            }

            // Collect all Stock orders IDs
            $stock_orders_ids = [];

            foreach ($sewings_and_cuts as $quantities_type => $quantities) {
                foreach ($quantities as $child_sku_stock_order_index => $quantity) {
                    preg_match('/^child_sku_(\d+)_stock_order_(\d+)$/', $child_sku_stock_order_index, $matches);
                    $child_sku_id = $matches[1];
                    $stock_order_id = $matches[2];
                    $production_deadline = $request_data['production_deadline'][$child_sku_stock_order_index];

                    $atelier_types = [];
                    if ($quantities_type == 'sent_for_cut') {
                        // Note: sent_for_cut are NOT being sent for Child SKUs which design is tagged with tag "3rd party"
                        $atelier_id = $request_data['atelier_cut'][$child_sku_stock_order_index];
                        $atelier_types[] = 'cut';
                    } else {
                        $atelier_id = $request_data['atelier_external_producer'][$child_sku_stock_order_index];
                        if (array_key_exists($atelier_id, $third_party_ateliers)) {
                            $atelier_types[] = 'third_party';
                        } else {
                            $atelier_types[] = 'production';
                        }

                        if (array_key_exists($child_sku_id, $child_skus_with_indication_sets)) {
                            $atelier_types[] = 'create_sets';
                        }
                    }

                    foreach ($atelier_types as $atelier_type) {
                        if (!array_key_exists($atelier_type, $ateliers) || !array_key_exists($atelier_id, $ateliers[$atelier_type])) {
                            $has_ateliers = true;
                            $ateliers[$atelier_type][$atelier_id] = [
                                'deadlines' => [],
                            ];
                        }

                        if (!array_key_exists($production_deadline, $ateliers[$atelier_type][$atelier_id]['deadlines'])) {
                            $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline] = [
                                'child_skus'       => [],
                                'stock_orders_ids' => [],
                                'project'          => '',
                            ];
                        }
                        if (!array_key_exists($child_sku_id, $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['child_skus'])) {
                            $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['child_skus'][$child_sku_id] = [
                                'code'            => $request_data['child_sku_code'][$child_sku_id],
                                'name'            => $request_data['child_sku_name'][$child_sku_id],
                                'product_measure' => $request_data['child_sku_product_measure'][$child_sku_id],
                                'upc'             => $request_data['child_sku_upc'][$child_sku_id],
                                'design_id'       => $request_data['design_id'][$child_sku_id],
                                'parent_sku_id'   => $request_data['parent_sku_id'][$child_sku_id],
                                'size_position'   => $request_data['size_position'][$child_sku_id],
                                'stock_orders'    => [],
                            ];
                            if (!empty($request_data['cut_sku_ids'][$child_sku_id])) {
                                foreach ($request_data['cut_sku_ids'][$child_sku_id] as $cut_sku_index => $cut_sku_id) {
                                    $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['child_skus'][$child_sku_id]['cut_skus'][$cut_sku_id] = [
                                        'id'              => $cut_sku_id,
                                        'code'            => $request_data['cut_sku_codes'][$child_sku_id][$cut_sku_index],
                                        'name'            => $request_data['cut_sku_names'][$child_sku_id][$cut_sku_index],
                                        'product_measure' => $request_data['cut_sku_product_measures'][$child_sku_id][$cut_sku_index],
                                    ];
                                }
                            }
                        }

                        $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['child_skus'][$child_sku_id]['stock_orders'][$stock_order_id] = [
                            // Quantity (sewing or sent for cut)
                            'gt2_row_id'             => $request_data['gt2_row_id'][$child_sku_stock_order_index],
                            'quantity'               => $quantity,
                            'production_deadline'    => $production_deadline,
                            'special_comments_notes' => $request_data['special_comments_notes'][$child_sku_stock_order_index],
                            'order_date'             => $request_data['order_date'][$child_sku_stock_order_index],
                            'price'                  => $request_data['price'][$child_sku_stock_order_index],
                        ];
                        if (empty($ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['project']) && !empty($request_data['order_project'][$child_sku_stock_order_index])) {
                            $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['project'] = $request_data['order_project'][$child_sku_stock_order_index];
                        }
                        $ateliers[$atelier_type][$atelier_id]['deadlines'][$production_deadline]['stock_orders_ids'][$stock_order_id] = $stock_order_id;
                        $stock_orders_ids[$stock_order_id] = $stock_order_id;
                    }
                }
            }
        }

        // Get Stock orders full nums
        $stock_orders_full_nums = [];
        if ($stock_orders_ids) {
            $stock_orders_ids_list = implode(', ', $stock_orders_ids);
            $query = "
                SELECT id, full_num
                  FROM " . DB_TABLE_DOCUMENTS . "
                  WHERE id IN ({$stock_orders_ids_list})";
            $stock_orders_full_nums = $db->GetAssoc($query);
        }

        /*
         * Add orders
         */
        if ($has_ateliers) {
            // Include documents language file
            $registry['translater']->loadFile(PH_MODULES_DIR . "documents/i18n/{$lang}/documents.ini");

            // Start a transaction
            $db->StartTrans();

            $get_old_vars = $registry->get('get_old_vars');
            $registry->set('get_old_vars', true, true);

            $successfully_added_orders_types = [];

            $current_date = General::strftime('%Y-%m-%d');

            foreach ($ateliers as $ateliers_type => $ateliers_data) {
                // Get the document types
                switch ($ateliers_type) {
                    case 'cut':
                        $document_type_id = $settings['doc_type_cut_order'];
                        break;
                    case 'third_party':
                        $document_type_id = $settings['doc_type_third_party_products_delivery_order'];
                        break;
                    case 'create_sets':
                        $document_type_id = $settings['doc_type_create_sets_order'];
                        break;
                    default:
                        $document_type_id = $settings['doc_type_production_order'];
                        break;
                }
                $document_type_filters = [
                    'where'      => ["dt.id = {$document_type_id}"],
                    'model_lang' => $lang
                ];
                $document_type = Documents_Types::searchOne($registry, $document_type_filters);

                // Add document for each atelier from the current type
                // and for each deadline
                foreach ($ateliers_data as $atelier_id => $atelier_data) {
                    foreach ($atelier_data['deadlines'] as $deadline => $deadline_data) {
                        $stock_orders_full_nums_list = implode(', ', array_intersect_key($stock_orders_full_nums, $deadline_data['stock_orders_ids']));
                        $document_params = [
                            'type'        => $document_type->get('id'),
                            'name'        => sprintf($this->i18n('orders_default_name'), $stock_orders_full_nums_list),
                            'description' => $stock_orders_full_nums_list,
                            'active'      => 1,
                            'department'  => $document_type->getDefaultDepartment(),
                            'group'       => $document_type->getDefaultGroup(),
                            'date'        => $current_date,
                            'deadline'    => $deadline . ' 23:59:59',
                            'customer'    => $atelier_id,
                            'employee'    => $registry['currentUser']->get('employee'),
                            'project'     => $deadline_data['project'],
                        ];
                        $document = new Document($registry, $document_params);
                        if ($document->save()) {
                            $new_document_filters = [
                                'where'                  => ["d.id = {$document->get('id')}"],
                                'skip_assignments'       => true,
                                'skip_permissions_check' => true
                            ];
                            $new_document = Documents::searchOne($registry, $new_document_filters);
                            $old_document = new Document($registry, ['type' => $document->get('type')]);
                            $old_document->sanitize();
                            $audit_id = Documents_History::saveData($registry, [
                                'action_type' => 'add',
                                'model'       => $document,
                                'new_model'   => $new_document,
                                'old_model'   => $old_document
                            ]);
                            if ($audit_id) {
                                // Make relation between the new document and the stock orders
                                $stock_orders_relatives_sql = "('{$document->get('id')}', 'Document', '" . implode("', 'Document', 'inherited'), ('{$document->get('id')}', 'Document', '", $deadline_data['stock_orders_ids']) . "', 'Document', 'inherited')";
                                $query = "
                                    INSERT IGNORE INTO `" . DB_TABLE_DOCUMENTS_RELATIVES . "` (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`, `origin`) VALUES
                                      " . $stock_orders_relatives_sql;
                                $db->Execute($query);
                            }
                            if ($audit_id && !$db->ErrorMsg()) {
                                if (!array_key_exists($document_type->get('id'), $successfully_added_orders_types)) {
                                    $successfully_added_orders_types[$document_type->get('id')] = [
                                        'name' => $document_type->get('name_plural'),
                                        'html' => []
                                    ];
                                }
                                $successfully_added_orders_types[$document_type->get('id')]['html'][] = sprintf('<a href="%s?%s=documents&documents=view&view=%d" target="_blank">%s</a>',
                                    $_SERVER['PHP_SELF'],
                                    $this->registry['module_param'],
                                    $document->get('id'),
                                    $document->get('full_num')
                                );
                                $document = clone $new_document;
                                unset($new_document);
                                $document->getVars();
                                $old_document = clone $document;
                                $old_document->sanitize();
                                $gt2_row_num = 1;

                                $stock_orders_list = array();
                                $gt2_values = [];
                                foreach ($deadline_data['child_skus'] as $child_sku_id => $child_sku) {
                                    foreach ($child_sku['stock_orders'] as $stock_order_id => $stock_order) {
                                        $stock_orders_list[] = $stock_order_id;
                                        if ($document_type_id == $settings['doc_type_cut_order']) {
                                            $article_height = 0;
                                        } else {
                                            $article_height = $stock_order['price'];
                                        }
                                        $gt2_row = [
                                            'free_field5'           => $stock_order_id,
                                            'free_field1'           => ($document->get('full_num') . '-' . $gt2_row_num++),
                                            'article_second_code'   => $stock_order['order_date'],
                                            'article_delivery_code' => $stock_order['production_deadline'],
                                            'free_text3'            => $stock_order['special_comments_notes'],
                                            'quantity'              => $stock_order['quantity'],
                                            'article_height'        => $article_height,
                                            // IMPORTANT: Temporary (used only for sorting)
                                            'design_id'             => $child_sku['design_id'],
                                            'parent_sku_id'         => $child_sku['parent_sku_id'],
                                            'size_position'         => $child_sku['size_position'],
                                            'gt2_row_id'            => $stock_order['gt2_row_id'],
                                        ];
                                        if ($document->get('type') == $settings['doc_type_cut_order']) {
                                            foreach ($child_sku['cut_skus'] as $cut_sku) {
                                                $gt2_row['article_id']           = $cut_sku['id'];
                                                $gt2_row['article_code']         = $cut_sku['code'];
                                                $gt2_row['article_name']         = $cut_sku['name'];
                                                $gt2_row['article_measure_name'] = $cut_sku['product_measure'];
                                                $gt2_row['free_field3']          = $gt2_row['quantity'];
                                                $gt2_values[] = $gt2_row;
                                            }
                                        } else {
                                            $gt2_row['article_id']           = $child_sku_id;
                                            $gt2_row['article_code']         = $child_sku['code'];
                                            $gt2_row['article_name']         = $child_sku['name'];
                                            $gt2_row['article_measure_name'] = $child_sku['product_measure'];
                                            if ($document->get('type') == $settings['doc_type_create_sets_order']) {
                                                $gt2_row['article_barcode'] = $child_sku['upc'];
                                            }
                                            $gt2_values[] = $gt2_row;
                                        }
                                    }
                                }

                                // Sort GT2 rows the same way they are sorted into the buildQuery() (there are two sorts there)
                                uasort($gt2_values,
                                    function ($a, $b) {
                                        // Sort by stock order date
                                        if ($a['article_second_code'] < $b['article_second_code']) {
                                            return -1;
                                        } elseif ($a['article_second_code'] === $b['article_second_code']) {
                                            // Then sort by stock order id
                                            if ($a['free_field5'] < $b['free_field5']) {
                                                return -1;
                                            } elseif ($a['free_field5'] === $b['free_field5']) {
                                                // Then sort by design
                                                if ($a['design_id'] < $b['design_id']) {
                                                    return -1;
                                                } elseif ($a['design_id'] === $b['design_id']) {
                                                    // Then sort by parent SKU
                                                    if ($a['parent_sku_id'] < $b['parent_sku_id']) {
                                                        return -1;
                                                    } elseif ($a['parent_sku_id'] === $b['parent_sku_id']) {
                                                        // Then sort by size position
                                                        if ($a['size_position'] < $b['size_position']) {
                                                            return -1;
                                                        } elseif ($a['size_position'] === $b['size_position']) {
                                                            // Then finally sort by stock order GT2 row ID
                                                            if ($a['gt2_row_id'] < $b['gt2_row_id']) {
                                                                return -1;
                                                            } elseif ($a['gt2_row_id'] === $b['gt2_row_id']) {
                                                                return 0;
                                                            } else {
                                                                return 1;
                                                            }
                                                        } else {
                                                            return 1;
                                                        }
                                                    } else {
                                                        return 1;
                                                    }
                                                } else {
                                                    return 1;
                                                }
                                            } else {
                                                return 1;
                                            }
                                        } else {
                                            return 1;
                                        }
                                    }
                                );

                                $gt2 = $document->getGT2Vars();
                                $gt2['values'] = [];
                                foreach ($gt2_values as $gt2_value) {
                                    // Remove temporary fields used only for sorting
                                    unset($gt2_value['design_id'], $gt2_value['parent_sku_id'], $gt2_value['size_position'], $gt2_value['gt2_row_id']);
                                    $gt2['values'][] = $gt2_value;
                                }

                                $gt2['plain_values']['currency'] = Finance_Currencies::getMain($registry);

                                $document->set('table_values_are_set', true, true);
                                if ($document->saveGT2Vars($gt2)) {
                                    $new_document_filters = [
                                        'where'                  => ["d.id = {$document->get('id')}"],
                                        'model_lang'             => $document->get('model_lang'),
                                        'skip_assignments'       => true,
                                        'skip_permissions_check' => true
                                    ];
                                    $new_document = Documents::searchOne($registry, $new_document_filters);
                                    $audit_id = Documents_History::saveData($registry, [
                                        'action_type' => 'edit',
                                        'model'       => $document,
                                        'new_model'   => $new_document,
                                        'old_model'   => $old_document
                                    ]);
                                    if ($audit_id) {
                                        $status = '';
                                        $substatus = '';
                                        if ($document->get('type') == $settings['doc_type_cut_order']) {
                                            if (!empty($settings['cut_order_add_status'])) {
                                                $status = $settings['cut_order_add_status'];
                                                if (!empty($settings['cut_order_add_substatus'])) {
                                                    $substatus = $settings['cut_order_add_substatus'];
                                                }
                                            }
                                        } else if ($document->get('type') == $settings['doc_type_third_party_products_delivery_order']) {
                                            if (!empty($settings['third_party_products_delivery_order_add_status'])) {
                                                $status = $settings['third_party_products_delivery_order_add_status'];
                                                if (!empty($settings['third_party_products_delivery_order_add_substatus'])) {
                                                    $substatus = $settings['third_party_products_delivery_order_add_substatus'];
                                                }
                                            }
                                        } else {
                                            if (!empty($settings['production_order_add_status'])) {
                                                $status = $settings['production_order_add_status'];
                                                if (!empty($settings['production_order_add_substatus'])) {
                                                    $substatus = $settings['production_order_add_substatus'];
                                                }
                                            }
                                        }
                                        if (!empty($status)) {
                                            $document = clone $new_document;
                                            unset($new_document);
                                            $old_document = clone $document;
                                            $document->set('status', $status, true);
                                            if (!empty($substatus)) {
                                                $document->set('substatus', "{$status}_{$substatus}", true);
                                            }
                                            if ($document->setStatus()) {
                                                $new_document_filters = [
                                                    'where'                  => ["d.id = {$document->get('id')}"],
                                                    'model_lang'             => $document->get('model_lang'),
                                                    'skip_assignments'       => true,
                                                    'skip_permissions_check' => true
                                                ];
                                                $new_document = Documents::searchOne($registry, $new_document_filters);
                                                $audit_id = Documents_History::saveData($registry, [
                                                    'action_type' => 'status',
                                                    'old_model'   => $old_document,
                                                    'model'       => $document,
                                                    'new_model'   => $new_document
                                                ]);
                                                if (!$audit_id) {
                                                    $db->FailTrans();
                                                }
                                            } else {
                                                $db->FailTrans();
                                            }
                                        }

                                        if (!empty($stock_orders_list)) {
                                            if (!$this->changeStockOrdersStatus($stock_orders_list)) {
                                                $db->FailTrans();
                                            }
                                        }

                                        // Execute action type automations
                                        if (!$this->executeActionAutomations($old_document, $new_document, 'add')) {
                                            $db->FailTrans();
                                        }
                                    } else {
                                        $db->FailTrans();
                                    }
                                } else {
                                    $db->FailTrans();
                                }
                            } else {
                                $db->FailTrans();
                            }
                        } else {
                            $db->FailTrans();
                        }

                        // If something has failed
                        if ($db->HasFailedTrans()) {
                            // Don't continue
                            break 3;
                        }
                    }
                }
            }

            $registry->set('get_old_vars', $get_old_vars, true);

            if ($messages->getErrors()) {
                $db->FailTrans();
            }

            if (!$db->HasFailedTrans() && !empty($successfully_added_orders_types)) {
                $messages->setMessage($this->i18n('successfully_added_orders'));
                foreach ($successfully_added_orders_types as $orders_type) {
                    $messages->setMessage(sprintf($this->i18n('successfully_added_orders_type'),
                        $orders_type['name'],
                        implode(', ', $orders_type['html'])
                    ));
                }
                $result = true;
            }
            $db->CompleteTrans();
        }

        if (!$result) {
            $messages->setError($this->i18n('failed_to_add_orders'));
        }

        // Load messages into the session
        $messages->insertInSession($registry);

        // Redirect back to the report
        $this->redirect($this->module, '', ['report_type' => $report_type]);
    }

    /*
     * Function to change the status of related stock orders if the required setting is set
     */
    public function changeStockOrdersStatus($stock_orders) {
        $result = true;
        $stock_orders = array_filter($stock_orders);
        if (empty($stock_orders) || empty($this->settings['stock_order_new_status'])) {
            return $result;
        }

        // define the status elements
        $document_filters = [
            'where'                  => ["d.id IN (\"" . implode('","', $stock_orders) . "\")"],
            'model_lang'             => $this->registry['lang'],
            'skip_assignments'       => true,
            'skip_permissions_check' => true
        ];
        $documents = Documents::search($this->registry, $document_filters);
        foreach ($documents as $doc) {
            $old_document = clone $doc;
            $doc->set('status', $this->settings['stock_order_new_status'], true);
            if (!empty($this->settings['stock_order_new_substatus'])) {
                $doc->set('substatus', "{$this->settings['stock_order_new_status']}_{$this->settings['stock_order_new_substatus']}", true);
            }

            if ($doc->setStatus()) {
                $new_document_filters = [
                    'where'                  => ["d.id = {$doc->get('id')}"],
                    'model_lang'             => $doc->get('model_lang'),
                    'skip_assignments'       => true,
                    'skip_permissions_check' => true
                ];
                $new_document = Documents::searchOne($this->registry, $new_document_filters);
                $audit_id = Documents_History::saveData($this->registry, [
                    'action_type' => 'status',
                    'old_model'   => $old_document,
                    'model'       => $doc,
                    'new_model'   => $new_document
                ]);
                if (!$audit_id) {
                    $result = false;
                }
            } else {
                $result = false;
            }
        }

        return $result;
    }
}
