<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  <style>
  {literal}
    table, td {
        vertical-align: top;
        border-collapse: collapse;
    }
    th {
        font-weight: bold;
        text-align: left;
    }
  {/literal}
  </style>
</head>
<body>
  <i>{$reports_results.period_title|escape}</i><br />
  <table border="0" cellpadding="0" cellspacing="0">
    <tr>
      <td>
      {if $reports_results.subreport_type eq $smarty.const.DOC_TYPE_BUDGET_YEAR}
        <table border="1">
          <tr>
            <td rowspan="2"><strong>{#num#|escape}</strong></td>
            <td rowspan="2"><strong>{#reports_code#|escape}</strong></td>
            <td rowspan="2"><strong>{#reports_items#|escape}</strong></td>
            {foreach name='month' from=$reports_results.months key='month' item='month_name'}
              <td colspan="{if $reports_results.include_initial_budgets}3{else}2{/if}"><strong>{$month_name}</strong></td>
            {/foreach}
            <td colspan="2"><strong>{#reports_total#|escape}</strong></td>
            <td colspan="2"><strong>{#reports_average#|escape}</strong></td>
          </tr>
          <tr>
            {foreach name='month' from=$reports_results.months key='month' item='month_name'}
              {if $reports_results.include_initial_budgets}
                <td><strong>{#reports_initial_budget#|escape}</strong></td>
              {/if}
              <td><strong>{#reports_budget#|escape}</strong></td>
              <td><strong>{#reports_spent#|escape}</strong></td>
            {/foreach}
            <td><strong>{#reports_budget#|escape}</strong></td>
            <td><strong>{#reports_spent#|escape}</strong></td>
            <td><strong>{#reports_budget#|escape}</strong></td>
            <td><strong>{#reports_spent#|escape}</strong></td>
          </tr>
          {foreach from=$reports_results.items item='item' name=results}
            <tr{if $item.category} style="background-color: orange; font-weight: bold"{/if}>
              <td style="text-align: right; vertical-align: middle;">{if $item.category}-{else}{counter name='item_counter' print=true}{/if}</td>
              <td>{$item.code}</td>
              <td>{$item.name}</td>
              {foreach name='month' from=$reports_results.months key='m' item='not_important'}
                {assign var='month' value=$item.months.$m}
                {if $reports_results.include_initial_budgets}
                  <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$month.initial|number_format:"2":".":" "}</td>
                {/if}
                <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$month.budget|number_format:"2":".":" "}</td>
                <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $month.color}color: {$month.color}{/if}">{$month.spent|number_format:"2":".":" "}</td>
              {/foreach}
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$item.budget_total|number_format:"2":".":" "}</td>
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $item.total_color}color: {$item.total_color}{/if}">{$item.spent_total|number_format:"2":".":" "}</td>
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$item.budget_average|number_format:"2":".":" "}</td>
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $item.average_color}color: {$item.average_color}{/if}">{$item.spent_average|number_format:"2":".":" "}</td>
            </tr>
          {/foreach}
        </table>
      {elseif $reports_results.subreport_type eq $smarty.const.DOC_TYPE_BUDGET_PERIOD}
        <table border="1">
          <tr>
            <td rowspan="2"><strong>{#num#|escape}</strong></td>
            <td rowspan="2"><strong>{#reports_code#|escape}</strong></td>
            <td rowspan="2"><strong>{#reports_items#|escape}</strong></td>
            {foreach name='month' from=$reports_results.months key='month' item='month_name'}
              <td rowspan="2"><strong>{$month_name}</strong></td>
            {/foreach}
            <td colspan="{if $reports_results.include_initial_budgets}5{else}4{/if}"><strong>{#reports_budget_total#|escape}</strong></td>
          </tr>
          <tr>
            {if $reports_results.include_initial_budgets}
              <td><strong>{#reports_initial_budget#|escape}</strong></td>
            {/if}
            <td><strong>{#reports_budget#|escape}</strong></td>
            <td><strong>{#reports_spent#|escape}</strong></td>
            <td><strong>{#reports_progress_execution#|escape}</strong></td>
            <td><strong>{#reports_percent_execution#|escape}</strong></td>
          </tr>
          {foreach from=$reports_results.items item='item' name=results}
            <tr{if $item.category} style="background-color: orange; font-weight: bold"{/if}>
              <td style="text-align: right; vertical-align: middle;">{if $item.category}-{else}{counter name='item_counter' print=true}{/if}</td>
              <td style="mso-number-format:\@">{$item.code|default:"&nbsp"}</td>
              <td style="mso-number-format:\@">{$item.name}</td>
              {foreach name='month' from=$reports_results.months key='m' item='not_important'}
                {assign var='month' value=$item.months.$m}
                <td style="mso-number-format:'0\.00';">{$month.spent|number_format:"2":".":" "}</td>
              {/foreach}
              {if $reports_results.include_initial_budgets}
                <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$item.initial_total|number_format:"2":".":" "}</td>
              {/if}
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';">{$item.budget_total|number_format:"2":".":" "}</td>
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $item.color}color: {$item.color}{/if}">{$item.spent_total|number_format:"2":".":" "}</td>
              <td style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $item.progress_execution < 0} color: red;{/if}">{$item.progress_execution|number_format:"2":".":" "}</td>
              <td  style="text-align: right; vertical-align: middle; mso-number-format:'0\.00';{if $item.percent_execution > 100} color: red;{/if}">
                {if !$item.category}{$item.percent_execution|number_format:"2":".":" "}{else}&nbsp;{/if}
              </td>
            </tr>
          {/foreach}
        </table>
      {/if}
      </td>
    </tr>
  </table>
</body>
</html>
