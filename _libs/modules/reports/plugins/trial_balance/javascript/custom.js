// set custom i18n messages
if (env.current_lang == 'bg') {
    i18n['messages']['error_no_report_type'] = 'Възникна грешка при работата на спраката! Моля, свържете се с администратор на nZoom!';
} else {
    i18n['messages']['error_no_report_type'] = 'An error occured! Please, contact nZoom support team!';
}

/**
 * Function to change cashboxes, bank accounts and offices depending on the selected company
 *
 * @param string fields_list - the ids of the fields, separated with commas
 * @param string css_class - the name of the CSS class used for the table rows
 */
function changeCashboxesBankAccountsAndOffices(element, element_to_change) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var report_type = '';
    current_form = element.form;
    if (current_form) {
        report_type_element = current_form.select('[name="report_type"]');
        if (report_type_element && report_type_element[0]) {
            report_type = report_type_element[0].value;
        }
    }

    if (!report_type) {
        alert(i18n['messages']['error_no_report_type']);
        Effect.Fade('loading');
        return false;
    }
    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_change_cashboxes_bank_account_office&report_type=' + report_type;
    if (element.id == 'company') {
        url += '&company=' + element.value;
    } else {
        url += '&office=' + element.value;
        if ($('company').value) {
            url += '&company=' + $('company').value;
        }
    }

    var select_obj = $(element_to_change);
    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            eval(t.responseText);
            completeCashboxesBankAccountsOptions(select_obj, result.containers);
            if (element.id == 'company') {
                completeOfficeOptions(result.offices);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/*
 * Function to complete the cashboxes and bank accounts in the respected field
 */
function completeCashboxesBankAccountsOptions(element_source, new_options) {
    var filter_cell = '';

    var parent_element = element_source.parentNode;
    while (!filter_cell && parent_element) {
        if (parent_element.id=='' && (parent_element.tagName == 'TR')) {
            filter_cell = parent_element;
        } else {
            parent_element = parent_element.parentNode;
        }
    }

    if (filter_cell) {
        filter_cell.innerHTML = new_options;
    }

    return;
}

/*
 * Function to complete the offices in the office filter
 */
function completeOfficeOptions(offices) {
    var office = $('office');
    office.options.length = 0;

    if (offices.length) {
        removeClass(office, 'missing_records');
        office.options[0] = new Option(i18n['labels']['please_select'].toLowerCase(), '', false, true);
        addClass(office.options[0], 'undefined');

        for (var j = 0; j < offices.length; j++) {
            office.options[j+1] = new Option(offices[j]['label'], offices[j]['option_value'], false, false);
        }
        toggleUndefined(office);
    } else {
        office.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
        addClass(office, 'missing_records');
    }
}