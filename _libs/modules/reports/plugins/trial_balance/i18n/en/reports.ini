reports_from = Period (date from)
reports_to = Period (date to)
reports_own_company = Company
reports_office = Office
reports_cashbox_bank_account = Cashbox/Bank account
reports_currency = Currency
reports_include_transfers = Include trasfers

reports_cashboxes = Cashboxes
reports_bank_accounts = Bank accounts

reports_cashbox = cashboxes
reports_bank_account = bank account

reports_date = Date
reports_document_num = Document num
reports_bank_account_cashbox = Cashbox/BA
reports_customer = Customer
reports_income = Income
reports_expense = Expense
reports_currency_sum = Currency sum
reports_description = Description
reports_employee = Employee
reports_paid_document = Paid document
reports_unallocated_payment = Unallocated payment

reports_no_paid_documents = no paid documents

reports_total = Total
reports_count_unallocated_payments = Unallocated payments

reports_period_result = Period result

reports_company = Company
reports_from_cashbox_bank_account = From cashbox/BA
reports_to_cashbox_bank_account = To cashbox/BA
reports_sum = Sum

reports_availability_beggining_period = Availability in the beginning of period
reports_total_income = Total income
reports_total_expense = Total expense
reports_transfers = Transfers
reports_current_availability = Current availability

reports_no_bank_accounts_for_company = no bank accounts for this company
reports_no_cashboxes_for_company = no cashboxes this company
reports_no_bank_accounts_cashboxes_for_company = no cashboxes and/or bank accounts for this company

error_complete_required_filters = Please, complete the required filters!
error_reports_no_available_companies_bank_accounts_cashboxes = No companies, cashboxes and/ot bank accounts to show in the results!
