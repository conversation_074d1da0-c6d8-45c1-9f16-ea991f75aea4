<?php
    class Custom_Report_Filters extends Report_Filters {

        /**
         * Defining filters for the certain type report
         */
        function defineFilters(&$registry) {
            // $filters - array containing description of all filters
            $filters = array();

            //DEFINE DATE FILTER FROM
            $filter = array (
                'custom_id' => 'from_date',
                'name' => 'from_date',
                'type' => 'date',
                'label' => $this->i18n('reports_from_date'),
                'help' => $this->i18n('reports_from_date')
            );
            $filters['from_date'] = $filter;

            //DEFINE DATE FILTER TO
            $filter = array (
                'custom_id' => 'to_date',
                'name' => 'to_date',
                'type' => 'date',
                'label' => $this->i18n('reports_to_date'),
                'help' => $this->i18n('reports_to_date')
            );
            $filters['to_date'] = $filter;

            //DEFINE DOCUMENTS TYPE FILTER
            //get offices
            require_once PH_MODULES_DIR . 'customers/models/customers.types.factory.php';
            $filters_customers_types = array('model_lang' => $registry['lang'],
                                             'sanitize' => true,
                                             'where' => array('ct.active = 1'));
            $customers_types = Customers_Types::search($registry, $filters_customers_types);
            $options_customers_types = array();
            foreach($customers_types as $customers_type) {
                $options_customers_types[] = array(
                    'label'         => $customers_type->get('name'),
                    'option_value'  => $customers_type->get('id'));
            }
            //prepare filters
            $filter = array (
                'custom_id' => 'customer_type',
                'name' => 'customer_type',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_customer_type'),
                'help' => $this->i18n('reports_customer_type'),
                'options' => $options_customers_types,
            );
            $filters['customer_type'] = $filter;

            //DEFINE OFFICE FILTER
            //get offices
            require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
            $filters_offices = array('model_lang' => $registry['lang'],
                                     'sanitize' => true,
                                     'where' => array('o.active = 1'));
            $offices = Offices::search($registry, $filters_offices);
            foreach($offices as $office) {
                $options_offices[] = array(
                    'label' => $office->get('name'),
                    'option_value' => $office->get('id'));
            }

            //prepare filters
            $filter = array (
                'custom_id' => 'offices',
                'name' => 'offices',
                'type' => 'dropdown',
                'label' => $this->i18n('reports_office_name'),
                'help' => $this->i18n('reports_office_name'),
                'options' => $options_offices,
            );
            $filters['offices'] = $filter;

            //DEFINE EMPLOYEE FILTER
            //get employees' options
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_employees = array('model_lang'    => $registry['lang'],
                                       'sanitize'      => true,
                                       'sort'          => array('ci18n.name', 'ci18n.lastname'),
                                       'where'         => array('c.type = ' . PH_CUSTOMER_EMPLOYEE,
                                                                'c.active = 1'));
            $employees = Customers::search($registry, $filters_employees);

            //prepare documents' types groups
            $options_employees = array();

            foreach($employees as $employee) {
                $options_employees[] = array(
                    'label'         => $employee->get('name') . ($employee->get('lastname') ? (' ' . $employee->get('lastname')) : ''),
                    'option_value'  => $employee->get('id')
                );
            }

            //prepare filter
            $filter = array (
                'custom_id' => 'employee',
                'name'      => 'employee',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_employee'),
                'help'      => $this->i18n('reports_employee'),
                'options'   => $options_employees,
            );
            $filters['employee'] = $filter;

            //DEFINE TOP FILTER
            $options_top = array();

            $options_top = array(
                array (
                    'label'         => 5,
                    'option_value'  => "5"
                ),
                array (
                    'label'         => 10,
                    'option_value'  => "10"
                ),
                array (
                    'label'         => 20,
                    'option_value'  => "20"
                ),
                array (
                    'label'         => 50,
                    'option_value'  => "50"
                )
            );

            //prepare filter
            $filter = array (
                'custom_id' => 'top',
                'name'      => 'top',
                'type'      => 'dropdown',
                'label'     => $this->i18n('reports_top'),
                'help'      => $this->i18n('reports_top'),
                'options'   => $options_top,
            );
            $filters['top'] = $filter;

            return $filters;
        }
    }
?>