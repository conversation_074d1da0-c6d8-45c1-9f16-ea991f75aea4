{assign var=total_colspan value=21}
{if $reports_additional_options.include_discount}
  {math equation='2+x' x=$total_colspan assign='total_colspan'}
{/if}
{if $reports_additional_options.include_agents}
  {math equation='6+x' x=$total_colspan assign='total_colspan'}
{/if}
{if $reports_additional_options.show_commission_distribution_fields}
  {math equation='2+x' x=$total_colspan assign='total_colspan'}
{/if}
{if $reports_additional_options.show_commission_damage_fields}
  {math equation='2+x' x=$total_colspan assign='total_colspan'}
{/if}
<table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
  <tr class="reports_title_row hcenter">
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_policy_num#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_type_insurance#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insurer#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_leasing_company#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insured#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_status#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_insurance_value#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_start_date#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_end_date#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_deadline#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_installment_num#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_installment_value#|escape}</div></td>
    {if $reports_additional_options.include_discount}
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_discount#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_installment_value_with_discount#|escape}</div></td>
    {/if}
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_tax#|escape}</div></td>
    {if $reports_additional_options.show_commission_distribution_fields}
      <td class="t_border" style="vertical-align: middle;"><div style="width: 55px;">{#reports_commission_distribution_percent#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 55px;">{#reports_commission_distribution_value#|escape}</div></td>
    {/if}
    {if $reports_additional_options.show_commission_damage_fields}
      <td class="t_border" style="vertical-align: middle;"><div style="width: 55px;">{#reports_commission_damage_percent#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div style="width: 55px;">{#reports_commission_damage_value#|escape}</div></td>
    {/if}
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_commision_percent#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_commision_bgn#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_fee#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_total_sum#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_balance#|escape}</div></td>
    {if $reports_additional_options.include_agents}
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_agent_name#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_agent_percent#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_agent_value#|escape}</div></td>
    {/if}
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_automobile_code#|escape}</div></td>
    <td class="t_border" style="vertical-align: middle;"><div style="">{#reports_automobile_rama#|escape}</div></td>
    <td {if $reports_additional_options.include_agents}class="t_border" {/if}style="vertical-align: middle;"><div style="">{#reports_automobile_model#|escape}</div></td>
    {if $reports_additional_options.include_agents}
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_property_address#|escape}</div></td>
      <td class="t_border" style="vertical-align: middle;"><div>{#reports_property_description#|escape}</div></td>
      <td style="vertical-align: middle;"><div>{#reports_property_notes#|escape}</div></td>
    {/if}
  </tr>
  {foreach from=$reports_results item=installment name=inst}
    {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
    {foreach from=$installment.cars item=car name=ca}
      <tr class="{$current_row_class}">
        {if $smarty.foreach.ca.first}
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=viewtopic&amp;viewtopic={$installment.id}">{$installment.num|escape|default:"&nbsp;"}</a>
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.insurance_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.insurer_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.leasing_company_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.insured_name|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.status|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.insurance_value|string_format:"%.2f"|default:"0.00"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.date_start|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.date_end|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
            {$installment.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.instalment_num|escape|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.value|string_format:"%.2f"|default:"0.00"}
          </td>
          {if $reports_additional_options.include_discount}
            <td class="t_border vmiddle hright" rowspan="{$installment.rowspan}">
              {$installment.discount|escape|default:"0.00"|string_format:"%.2f"}
            </td>
            <td class="t_border vmiddle hright" rowspan="{$installment.rowspan}">
              {$installment.value_with_discount|escape|default:"0.00"|string_format:"%.2f"}
            </td>
          {/if}
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.tax|string_format:"%.2f"|default:"0.00"}
          </td>
          {if $reports_additional_options.show_commission_distribution_fields}
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {$installment.commission_distribution_percent|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {$installment.commission_distribution_value|string_format:"%.2f"|default:"0.00"}
            </td>
          {/if}
          {if $reports_additional_options.show_commission_damage_fields}
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {$installment.commission_damage_percent|string_format:"%.2f"|default:"0.00"}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {$installment.commission_damage_value|string_format:"%.2f"|default:"0.00"}
            </td>
          {/if}
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.commission_percent|string_format:"%.2f"|default:"0.00"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.commission_bgn|string_format:"%.2f"|default:"0.00"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.fee|default:"&nbsp;"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.premium|string_format:"%.2f"|default:"0.00"}
          </td>
          <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
            {$installment.balance|default:"&nbsp;"}
          </td>
          {if $reports_additional_options.include_agents}
            <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
              {$installment.agent|escape|default:"&nbsp;"}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {if $installment.percent}
                {$installment.percent|string_format:"%.2f"|default:"&nbsp;"}
              {else}
                &nbsp;
              {/if}
            </td>
            <td class="t_border hright vmiddle" rowspan="{$installment.rowspan}">
              {if $installment.agent_commission}
                {$installment.agent_commission|string_format:"%.2f"|default:"&nbsp;"}
              {else}
                &nbsp;
              {/if}
            </td>
          {/if}
        {/if}
        <td class="t_border vmiddle">
          {$car.code|escape|default:"&nbsp;"}
        </td>
        <td class="t_border vmiddle">
          {$car.rama|escape|default:"&nbsp;"}
        </td>
        <td class="{if $reports_additional_options.include_agents}t_border {/if}vmiddle">
          {$car.name|escape|default:"&nbsp;"}
        </td>
        {if $reports_additional_options.include_agents}
          {if $smarty.foreach.ca.first}
            <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
              {$installment.property_address|escape|default:"&nbsp;"}
            </td>
            <td class="t_border vmiddle" rowspan="{$installment.rowspan}">
              {$installment.property_description|escape|default:"&nbsp;"}
            </td>
            <td class="vmiddle" rowspan="{$installment.rowspan}">
              {$installment.notes|escape|nl2br|default:"&nbsp;"}
            </td>
          {/if}
        {/if}
      </tr>
    {/foreach}
  {foreachelse}
    <tr class="{cycle values='t_odd,t_even'}">
      <td class="error" colspan="{$total_colspan}">{#no_items_found#|escape}</td>
    </tr>
  {/foreach}
  <tr>
    <td class="t_footer" colspan="{$total_colspan}"></td>
  </tr>
</table>
