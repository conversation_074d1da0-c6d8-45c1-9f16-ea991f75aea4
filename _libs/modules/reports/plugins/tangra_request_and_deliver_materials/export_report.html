<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
  <body>
    <h2>{#reports_request_deliver_materials#}</h2>
    <h3>{#reports_request_deliver_materials_subtitle#}</h3>
    <table border="1">
      <tr>
        <td nowrap="nowrap"><strong>{#num#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_document_num_total#|escape}</strong></td>
        <td style="width: 80px;"><strong>{#reports_order_date#|escape}</strong></td>
        <td nowrap="nowrap" style="width: 350px;"><strong>{#reports_product#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_direction_total#|escape}</strong></td>
        <td nowrap="nowrap"><strong>{#reports_employee_total#|escape}</strong></td>
        <td style="width: 80px;"><strong>{#reports_acceptance_date_total#|escape}</strong></td>
        <td style="width: 80px;"><strong>{#reports_expected_delivey_total#|escape}</strong></td>
        <td nowrap="nowrap" style="width: 150px;"><strong>{#reports_deliverer_total#|escape}</strong></td>
        <td nowrap="nowrap" style="width: 350px;"><strong>{#reports_notes1_total#|escape}</strong></td>
        <td style="width: 100px;"><strong>{#reports_receive_doc_num_total#|escape}</strong></td>
        <td style="width: 80px;"><strong>{#reports_delivery_date_total#|escape}</strong></td>
        <td nowrap="nowrap" style="width: 350px;"><strong>{#reports_notes2_total#|escape}</strong></td>
      </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach from=$reports_results item=result name=results}
        <tr>
          <td align="right" nowrap="nowrap" rowspan="{$result.rows}">
            {counter name='item_counter' print=true}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}" class="mso-number-format:\@;">
            {$result.full_num|numerate:$result.direction|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.date_added|date_format:#date_short#|default:"&nbsp;"}
          </td>
          <td>
            {if $result.products.0}
              {$result.products.0|escape|default:"&nbsp;"}
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {foreach from=$result.directions item=direction name=direct}
              {$direction|escape|default:"&nbsp;"}
              {if ! $smarty.foreach.direct.last}
                <br />
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td rowspan="{$result.rows}">
            {$result.requested_by_name|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.acceptance_date|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.expected_delivery_date|date_format:#date_short#|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.deliverer|escape|default:"&nbsp;"}
          </td>
          <td rowspan="{$result.rows}">
            {$result.notes|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.num_receive_doc|escape|default:"&nbsp;"}
          </td>
          <td nowrap="nowrap" rowspan="{$result.rows}">
            {$result.delivery_date|default:"&nbsp;"}
          </td>
          <td rowspan="{$result.rows}">
            {$result.notes2|escape|default:"&nbsp;"}
          </td>
          {foreach from=$result.products item=product name=prod}
            {if !$smarty.foreach.prod.first}
              <tr>
                <td>
                  {$product|escape|default:"&nbsp;"}
                </td>
              </tr>
            {/if}
          {/foreach}
        </tr>
      {foreachelse}
        <tr>
          <td colspan="13">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>