<table cellpadding="5" cellspacing="0" class="customer_base_information" style="width: 456px;">
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_principal#|escape}</td>
    <td class="customer_base_information_data_light hright">{$sums_to_pay.principal|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_interest#|escape}</td>
    <td class="customer_base_information_data_dark hright">{$sums_to_pay.interest|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_penalty#|escape}</td>
    <td class="customer_base_information_data_light hright">{$sums_to_pay.penalty|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_warranty#|escape}</td>
    <td class="customer_base_information_data_dark hright">{$sums_to_pay.warranty|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_tax_ang#|escape}</td>
    <td class="customer_base_information_data_light hright">{$sums_to_pay.tax_ang|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_tax_management#|escape}</td>
    <td class="customer_base_information_data_dark hright">{$sums_to_pay.tax_mng|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_lpg#|escape}</td>
    <td class="customer_base_information_data_light hright">{$sums_to_pay.lpg|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_tax_advance_cover#|escape}</td>
    <td class="customer_base_information_data_dark hright">{$sums_to_pay.extra|string_format:"%.2f"|escape|default:"0.00"}</td>
  </tr>
  <tr>
    <td class="customer_base_information_label">{#reports_repayment_plan_sum_total#|escape} {$repayment_date|date_format:#date_short#|escape}</td>
    <td class="customer_base_information_data_light hright"><strong>{$sums_to_pay.total|string_format:"%.2f"|escape|default:"0.00"}</strong></td>
  </tr>
</table>
<div style="margin-top: 7px;">
  <button type="button" class="button" onclick="if (confirm('{#reports_confirm_advanced_loan_cover#}')) credilinkАdvancedLoanCover('{$repayment_plan}', '{$repayment_date}'); return false;" style="float: left;">{#reports_repayment_plan_advanced_full_cover#|escape}</button>
  <button type="button" class="button" onclick="lb.deactivate(); return false;" style="float: right;">{#close#|escape}</button>
</div>
