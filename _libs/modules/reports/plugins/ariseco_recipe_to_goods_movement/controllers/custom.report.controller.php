<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {
    public function execute() {
        switch ($this->action) {
            case 'redirect':
                $this->redirectToGoodsMovement();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Prepare filters and redirect to report goods_movement
     */
    private function redirectToGoodsMovement() {
        $settings = Reports::getReportSettings($this->registry, $this->getReportType()['name']);
        $fromDateMonthsBefore = $settings['goods_movement_filter_from_date_months_before']??'6';
        $fromDate = General::strftime('%Y-%m-%d', strtotime("-{$fromDateMonthsBefore} month"));
        $fromDateFormatted = General::strftime('%d.%m.%Y', strtotime($fromDate));
        $redirectParams = [
            'report_type'                   => 'goods_movement',
            'report_for'                    => 'deliveries',
            'warehouse'                     => '',
            'from_date_formatted'           => $fromDateFormatted,
            'from_date'                     => $fromDate,
            'to_date_formatted'             => '++.++.++++',
            'to_date'                       => '',
            'key_words'                     => '',
            'report_based_on_article'       => 'article_checked',
            'batch'                         => '',
            'customer'                      => '',
        ];
        if ($recipeId = $this->registry['request']->get('recipe_id')) {
            $query = "
                SELECT n1.id, n1.code, ni.name
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (n.id = {$recipeId}
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = 'product_id')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.lang = '')
                  JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                    ON (n1.id = nc.value)
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                    ON (ni.parent_id = n1.id
                      AND ni.lang = '{$this->registry['lang']}')";
            $materials = $this->registry['db']->GetAssoc($query);
            $materialIndex = 0;
            foreach ($materials as $materialId => $material) {
                $material['name'] = str_replace(["\r\n", "\r", "\n"], ' ', $material['name']);
                $redirectParams["nomenclature[{$materialIndex}]"] = $materialId;
                $redirectParams["nomenclature_autocomplete[{$materialIndex}]"] = "[{$material['code']}] {$material['name']}";
                $materialIndex++;
                $redirectParams["nomenclature_oldvalue_{$materialIndex}"] = "[{$material['code']}] {$material['name']}";
            }
        }
        $this->redirect($this->module, 'generate_report', $redirectParams);
    }
}
