<?php
    Class Dermavita_Turnovers Extends Reports {
        private static $included_sheets_ids = array();
        private static $available_procedures = array();
        private static $available_companies = array();
        private static $available_cashboxes = array();
        private static $sheet_tags_paid = array();
        private static $sheet_tags_partial = array();
        private static $sheet_tags_unpaid = array();
        private static $additional_vars_visit = array();
        private static $sheet_dates = array();
        private static $medics_nurses = array();
        private static $total_with_vat_id = '';
        private static $additional_vars_payment = array();
        private static $payments_ids = array();

        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();
            $error_flag = false;

            $first_table = array();
            $fourth_table = array();
            $second_table = array(
                'companies'    => array(),
                'total_start'  => 0,
                'total_end'    => 0
            );
            $third_table = array(
                'companies'           => array(),
                'last_column_rowspan' => 0,
                'total_paid'          => 0,
                'total_partial'       => 0,
                'total_unpaid'        => 0
            );

            self::$sheet_tags_paid = array_filter(preg_split('/\s*,\s*/', SHEET_TAG_PAID));
            self::$sheet_tags_partial = array_filter(preg_split('/\s*,\s*/', SHEET_TAG_PARTIAL));
            self::$sheet_tags_unpaid = array_filter(preg_split('/\s*,\s*/', SHEET_TAG_UNPAID));

            if (!empty($filters['companies'])) {
                $sql = 'SELECT n.id as idx, n.id as id, ni18n.name as name, comp.value as company, CONCAT(comp_name.name, " ", comp_name.lastname) as company_name' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       '  ON (n.id=ni18n.parent_id AND ni18n.lang="' . $registry['lang'] .'")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                       '  ON (fm.model="Nomenclature" AND fm.model_type="' . NOM_CASHBOX_BANK . '" AND fm.name="' . NOM_CONTAINER_COMPANY . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS comp' . "\n" .
                       '  ON (comp.model_id=n.id AND comp.var_id=fm.id AND comp.value IN ("' . implode('","', $filters['companies']) . '"))' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS comp_name' . "\n" .
                       '  ON (comp.value=comp_name.parent_id AND comp_name.lang="' . $registry['lang'] .'")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                       '  ON (tm.model="Nomenclature" AND tm.model_id=n.id)' . "\n" .
                       'WHERE n.type="' . NOM_CASHBOX_BANK . '" AND n.active="1" AND n.deleted_by=0 AND (tm.tag_id IS NULL OR tm.tag_id != "' . TAG_SYSTEM_CONTAINER . '")' . "\n" .
                       'GROUP BY n.id';
                self::$available_cashboxes = $registry['db']->GetAssoc($sql);

                foreach (self::$available_cashboxes as $key_avb_cash => $cashbox_data) {
                    if (!empty($filters['cashbox_bank']) && !in_array($key_avb_cash, $filters['cashbox_bank'])) {
                        unset(self::$available_cashboxes[$key_avb_cash]);
                    } else {
                        if (!isset($third_table['companies'][$cashbox_data['company']])) {
                            self::$available_companies[] = $cashbox_data['company'];
                            $third_table['companies'][$cashbox_data['company']] = array(
                                'id'   => $cashbox_data['company'],
                                'name' => $cashbox_data['company_name'],
                                'containers' => array()
                            );
                            $third_table['last_column_rowspan']++;

                            $second_table['companies'][$cashbox_data['company']] = array(
                                'id'   => $cashbox_data['company'],
                                'name' => $cashbox_data['company_name'],
                                'containers' => array()
                            );
                        }
                        $third_table['companies'][$cashbox_data['company']]['containers'][$cashbox_data['id']] = array(
                            'id'      => $cashbox_data['id'],
                            'name'    => $cashbox_data['name'],
                            'paid'    => 0,
                            'partial' => 0
                        );
                        $third_table['last_column_rowspan']++;

                        $second_table['companies'][$cashbox_data['company']]['containers'][$cashbox_data['id']] = array(
                            'id'    => $cashbox_data['id'],
                            'name'  => $cashbox_data['name'],
                            'start' => 0,
                            'end'   => 0
                        );
                    }
                }
            }

            $roles_full_rights = preg_split('/\s*,\s*/', ROLES_FULL_RIGHTS);
            $roles_full_rights = array_filter($roles_full_rights);
            $readonly_filters_check = false;
            if (!in_array($registry['currentUser']->get('role'), $roles_full_rights)) {
                $readonly_filters_check = ($filters['from_date']==date('Y-m-d') && $filters['to_date']==date('Y-m-d'));
            } else {
                $readonly_filters_check = true;
            }

            if (!empty($filters['from_date']) && !empty($filters['to_date']) && !empty(self::$available_companies) && $readonly_filters_check) {
                if (count(array_filter($filters['procedure']))) {
                    $sql = sprintf('SELECT id FROM %s WHERE `type`="%d" AND `id` IN ("%s")',
                        DB_TABLE_NOMENCLATURES,
                        PROCEDURE_TYPES_CATEGORIES,
                        implode('","', array_filter($filters['procedure']))
                    );
                    $categories_nomenclatures = $registry['db']->GetCol($sql);
                    self::$available_procedures = array_diff(array_filter($filters['procedure']), $categories_nomenclatures);

                    if (!empty($categories_nomenclatures)) {
                        $sql = 'SELECT n.id' . "\n" .
                            'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                            'INNER JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                            '  ON (fm.model="Nomenclature" AND fm.model_type=n.type AND fm.name="' . NOM_PROCEDURE_CATEGORY . '")' . "\n" .
                            'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS cat_nom' . "\n" .
                            '  ON (cat_nom.model_id=n.id AND cat_nom.var_id=fm.id AND cat_nom.value IN ("' . implode('","', $categories_nomenclatures) . '"))' . "\n" .
                            'WHERE n.type="' . PROCEDURE_TYPES . '" AND n.active=1 AND n.deleted_by="0"' . "\n";
                        $nom_in_cat = $registry['db']->GetCol($sql);

                        self::$available_procedures = array_merge(self::$available_procedures, $nom_in_cat);
                        unset($nom_in_cat);
                        self::$available_procedures = array_unique(self::$available_procedures);
                    }
                }

                self::$medics_nurses = array_filter($filters['medic_nurse']);
                self::$additional_vars_visit = array(DOCUMENT_VISIT_START_HOUR, 'total_with_vat');

                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENT_VISIT . '" AND `name` IN ("' . implode('","', self::$additional_vars_visit) . '")';
                self::$additional_vars_visit = $registry['db']->GetAssoc($sql);
                $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENT_SHEET . '" AND `name`="total_with_vat"';
                self::$total_with_vat_id = $registry['db']->GetOne($sql);

                $sheets_list = self::getSheets($registry, $filters, 'sheet');

                // process the sheets for the first table
                $first_table = self::processSheets($sheets_list, true);
                unset($sheets_list);

                // get the payments
                self::$included_sheets_ids = array_filter(array_unique(self::$included_sheets_ids));

                self::$additional_vars_payment = array(DOCUMENT_PAYMENT_SHEET_ID, DOCUMENT_PAYMENT_CONTAINER, DOCUMENT_PAYMENT_VALUE, DOCUMENT_PAYMENT_COMPANY);
                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOCUMENT_PAYMENT . '" AND `name` IN ("' . implode('","', self::$additional_vars_payment) . '")';
                self::$additional_vars_payment = $registry['db']->GetAssoc($sql);

                $payments_list = self::getPayments($registry, $filters, 'sheet');
                $first_table = self::rearrangePayments($first_table, $payments_list, $filters, 'sheet');
                self::$payments_ids = array_values(array_unique(self::$payments_ids));


                // GET THE DATA FOR THE FOURTH TABLE
                $extra_payment_data = self::getPayments($registry, $filters, 'payments', self::$payments_ids);

                $extra_sheet_data = array();
                foreach ($extra_payment_data as $epd) {
                    $extra_sheet_data[] = $epd['related_sheet'];
                }
                $extra_sheet_data = array_unique($extra_sheet_data);

                if (!empty($extra_sheet_data)) {
                    $extra_sheet_data = self::getSheets($registry, $filters, 'payment', $extra_sheet_data);
                }

                $fourth_table = self::processSheets($extra_sheet_data);
                $fourth_table = self::rearrangePayments($fourth_table, $extra_payment_data, $filters, 'payment');

                // define second table start amount
                $sql = 'SELECT n.id, SUM(d_cstm_value.value) as payment_amount' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_container' . "\n" .
                       '  ON (d_cstm_container.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER] : '') . '" AND d_cstm_container.value=n.id)' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' d' . "\n" .
                       '  ON (d_cstm_container.model_id=d.id AND d.type="' . DOCUMENT_PAYMENT . '" AND DATE_FORMAT(d.date, "%Y-%m-%d")<"' . $filters['from_date'] . '" AND d.active=1 AND d.deleted_by=0 AND d.substatus!="' . DOCUMENT_PAYMENT_SUBSTATUS_ANNULLED . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_value' . "\n" .
                       '  ON (d_cstm_value.model_id=d_cstm_container.model_id AND d_cstm_value.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE] : '') . '" AND d_cstm_value.num=d_cstm_container.num)' . "\n" .
                       'WHERE n.id IN ("' . implode('","', array_keys(self::$available_cashboxes)) . '")' . "\n" .
                       'GROUP BY n.id' . "\n";
                $before_balance = $registry['db']->GetAssoc($sql);

                // define second table start amount
                $sql = 'SELECT n.id, SUM(d_cstm_value.value) as payment_amount' . "\n" .
                       'FROM ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_container' . "\n" .
                       '  ON (d_cstm_container.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER] : '') . '" AND d_cstm_container.value=n.id)' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' d' . "\n" .
                       '  ON (d_cstm_container.model_id=d.id AND d.type="' . DOCUMENT_PAYMENT . '" AND DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '" AND DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '" AND d.active=1 AND d.deleted_by=0 AND d.substatus!="' . DOCUMENT_PAYMENT_SUBSTATUS_ANNULLED . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_value' . "\n" .
                       '  ON (d_cstm_value.model_id=d_cstm_container.model_id AND d_cstm_value.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE] : '') . '" AND d_cstm_value.num=d_cstm_container.num)' . "\n" .
                       'WHERE n.id IN ("' . implode('","', array_keys(self::$available_cashboxes)) . '")' . "\n" .
                       'GROUP BY n.id' . "\n";
                $during_balance = $registry['db']->GetAssoc($sql);

                foreach ($second_table['companies'] as $key => $company) {
                    foreach ($company['containers'] as $cont_k => $container) {
                        $second_table['companies'][$key]['containers'][$cont_k]['start'] = (isset($before_balance[$cont_k]) ? $before_balance[$cont_k] : 0);
                        $second_table['companies'][$key]['containers'][$cont_k]['end'] = $second_table['companies'][$key]['containers'][$cont_k]['start'] + (isset($during_balance[$cont_k]) ? $during_balance[$cont_k] : 0);
                        $second_table['total_start'] += $second_table['companies'][$key]['containers'][$cont_k]['start'];
                        $second_table['total_end'] += $second_table['companies'][$key]['containers'][$cont_k]['end'];
                    }
                }

                foreach ($first_table as $ft_date => $date_data) {
                    foreach ($date_data['sheets'] as $sheet_id => $sheet_data) {
                        $first_table[$ft_date]['sheets'][$sheet_id]['rowspan'] = count($sheet_data['procedures']);
                        $first_table[$ft_date]['rowspan'] += count($sheet_data['procedures']);

                        // complete the data for the second table
                        if (in_array($sheet_data['payment_status'], self::$sheet_tags_paid)) {
                            foreach ($sheet_data['payments'] as $paym) {
                                // third table completion
                                $third_table['companies'][$paym['company']]['containers'][$paym['container']]['paid'] += $paym['sum'];
                                $third_table['total_paid'] += $paym['sum'];
                            }
                        } elseif (in_array($sheet_data['payment_status'], self::$sheet_tags_partial)) {
                            foreach ($sheet_data['payments'] as $paym) {
                                // third table completion
                                $third_table['companies'][$paym['company']]['containers'][$paym['container']]['partial'] += $paym['sum'];
                                $third_table['total_partial'] += $paym['sum'];
                            }
                        } elseif (in_array($sheet_data['payment_status'], self::$sheet_tags_unpaid)) {
                            $third_table['total_unpaid'] += $sheet_data['total_sheet_value'];
                        }
                    }
                }

                foreach ($fourth_table as $ft_date => $date_data) {
                    foreach ($date_data['sheets'] as $sheet_id => $sheet_data) {
                        $fourth_table[$ft_date]['sheets'][$sheet_id]['rowspan'] = count($sheet_data['procedures']);
                        $fourth_table[$ft_date]['rowspan'] += count($sheet_data['procedures']);

                        // complete the data for the second table
                        if (in_array($sheet_data['payment_status'], self::$sheet_tags_paid)) {
                            foreach ($sheet_data['payments'] as $paym) {
                                // third table completion
                                $third_table['companies'][$paym['company']]['containers'][$paym['container']]['paid'] += $paym['sum'];
                                $third_table['total_paid'] += $paym['sum'];
                            }
                        } elseif (in_array($sheet_data['payment_status'], self::$sheet_tags_partial)) {
                            foreach ($sheet_data['payments'] as $paym) {
                                // third table completion
                                $third_table['companies'][$paym['company']]['containers'][$paym['container']]['partial'] += $paym['sum'];
                                $third_table['total_partial'] += $paym['sum'];
                            }
                        } elseif (in_array($sheet_data['payment_status'], self::$sheet_tags_unpaid)) {
                            $third_table['total_unpaid'] += $sheet_data['total_sheet_value'];
                        }
                    }
                }

            } else {
                $error_flag = true;
                if (!$readonly_filters_check) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_invalid_period'));
                }
                if (empty($filters['from_date']) || empty($filters['to_date']) || empty(self::$available_companies)) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_filters'));
                }
            }

            $final_results['first_table'] = $first_table;
            $final_results['second_table'] = $second_table;
            $final_results['third_table'] = $third_table;
            $final_results['fourth_table'] = $fourth_table;
            $final_results['error_flag'] = $error_flag;
            $final_results['table2_name'] = sprintf($registry['translater']->translate('reports_table_2_name'), (!empty($filters['from_date']) ? General::strftime('%d.%m.%Y', $filters['from_date']) : '...'), (!empty($filters['to_date']) ? General::strftime('%d.%m.%Y', $filters['to_date']) : '...'));
            $final_results['table3_name'] = sprintf($registry['translater']->translate('reports_table_3_name'), (!empty($filters['from_date']) ? General::strftime('%d.%m.%Y', $filters['from_date']) : '...'), (!empty($filters['to_date']) ? General::strftime('%d.%m.%Y', $filters['to_date']) : '...'));
            $final_results['table4_name'] = sprintf($registry['translater']->translate('reports_table_4_name'), (!empty($filters['from_date']) ? General::strftime('%d.%m.%Y', $filters['from_date']) : '...'), (!empty($filters['to_date']) ? General::strftime('%d.%m.%Y', $filters['to_date']) : '...'));
            $final_results['show_second_table'] = in_array($registry['currentUser']->get('role'), $roles_full_rights);

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /*
         * Function to process the sheets for the final results table
         */
        public static function processSheets($sheets_list, $complete_sheets_ids_list = false) {
            $ordered_table = array();
            foreach($sheets_list as $sl) {
                if (!isset($ordered_table[$sl['doc_date']])) {
                    $ordered_table[$sl['doc_date']] = array(
                        'date'    => $sl['doc_date'],
                        'sheets'  => array(),
                        'rowspan' => 0,
                    );
                }

                if (!isset($ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']])) {
                    self::$sheet_dates[$sl['doc_id']] = $sl['doc_date'];
                    $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']] = array(
                        'id'                 => $sl['doc_id'],
                        'num'                => $sl['doc_full_num'],
                        'customer'           => $sl['customer'],
                        'customer_name'      => $sl['customer_name'],
                        'visit'              => $sl['related_visit'],
                        'visit_num'          => $sl['related_visit_num'],
                        'visit_hour'         => $sl['visit_hour'],
                        'procedures'         => array(),
                        'total_sheet_value'  => $sl['total_sheet_value'],
                        'total'              => 0,
                        'total_procedures'   => 0,
                        'total_cosmetics'    => 0,
                        'payment_status'     => $sl['payment_status'],
                        'payment_status_lbl' => $sl['payment_status_label'],
                        'payment_status_key' => '',
                        'payments'           => array(),
                        'containers'         => array(),
                        'rowspan'            => 0
                    );

                    if (in_array($sl['payment_status'], self::$sheet_tags_paid)) {
                        $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['payment_status_key'] = 'paid';
                    } elseif (in_array($sl['payment_status'], self::$sheet_tags_partial)) {
                        $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['payment_status_key'] = 'partial';
                    } elseif (in_array($sl['payment_status'], self::$sheet_tags_unpaid)) {
                        $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['payment_status_key'] = 'unpaid';
                    }
                    if ($complete_sheets_ids_list) {
                        self::$included_sheets_ids[] = $sl['doc_id'];
                    }
                }
                $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['procedures'][$sl['gt2_id']] = array(
                    'gt2_id'       => $sl['gt2_id'],
                    'article'      => $sl['article_id'],
                    'category'     => $sl['category'],
                    'type'         => $sl['article_type'],
                    'name'         => $sl['article_name'],
                    'made_by'      => $sl['made_by'],
                    'made_by_name' => $sl['made_by_name'],
                    'price'        => $sl['price'],
                    'quantity'     => $sl['quantity'],
                    'made_by_name' => $sl['made_by_name'],
                    'payment'      => $sl['payment'],
                );
                $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['total'] += $sl['subtotal_with_discount'];
                if ($sl['article_type'] == NOM_PROCEDURE) {
                    $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['total_procedures'] += $sl['subtotal_with_discount'];
                } elseif ($sl['article_type'] == NOM_COSMETICS) {
                    $ordered_table[$sl['doc_date']]['sheets'][$sl['doc_id']]['total_cosmetics'] += $sl['subtotal_with_discount'];
                }
            }

            return $ordered_table;
        }

        /*
         * Function to get the sheets for the final results table
         */
        public static function getSheets(&$registry, $filters, $priority, $ids_list = array()) {
            $sql = array();
            $sql = 'SELECT d.id as doc_id, d.full_num as doc_full_num, d.customer as customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, d_cstm_total.value as total_sheet_value, ' . "\n" .
                   '  DATE_FORMAT(d.date, "%Y-%m-%d") as doc_date, d.full_num as doc_num, d2.id as related_visit, d2.full_num as related_visit_num, gt2.free_field3 as company, nti18n.name as category, ' . "\n" .
                   '  d_cstm_sh.value as visit_hour, gt2.id as gt2_id, gt2.article_id, nom.type as article_type, nom_i18n.name as article_name, gt2.free_field2 as made_by, fo.label as payment, ' . "\n" .
                   '  CONCAT(ci18n_made_by.name, " ", ci18n_made_by.lastname) as made_by_name, gt2.price, gt2.quantity, gt2.subtotal_with_discount, tg.tag_id as payment_status, ti18n.name as payment_status_label' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n' . "\n" .
                   '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' as dr' . "\n" .
                   '  ON (dr.link_to=d.id AND dr.link_to_model_name="Document" AND dr.parent_model_name="Document")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d2' . "\n" .
                   '  ON (d2.id=dr.parent_id AND d2.active=1 AND d2.deleted_by=0 AND d2.type="' . DOCUMENT_VISIT . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_sh' . "\n" .
                   '  ON (d_cstm_sh.model_id=d2.id AND d_cstm_sh.var_id="' . (isset(self::$additional_vars_visit[DOCUMENT_VISIT_START_HOUR]) ? self::$additional_vars_visit[DOCUMENT_VISIT_START_HOUR] : '') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_total' . "\n" .
                   '  ON (d_cstm_total.model_id=d.id AND d_cstm_total.var_id="' . self::$total_with_vat_id . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' as gt2' . "\n" .
                   '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' as gt2i18n' . "\n" .
                   '  ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' as fo' . "\n" .
                   '  ON (fo.parent_name="free_text1" AND gt2i18n.free_text1=fo.option_value)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n_made_by' . "\n" .
                   '  ON (ci18n_made_by.parent_id=gt2.free_field2 AND ci18n_made_by.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' as nom' . "\n" .
                   '  ON (nom.id=gt2.article_id)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as nom_i18n' . "\n" .
                   '  ON (nom_i18n.parent_id=gt2.article_id AND nom_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' as nti18n' . "\n" .
                   '  ON (nti18n.parent_id=nom.type AND nti18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' as tg' . "\n" .
                   '  ON (tg.model="Document" AND tg.model_id=d.id)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' as ti18n' . "\n" .
                   '  ON (ti18n.parent_id=tg.tag_id AND ti18n.lang="' . $registry['lang'] . '")' . "\n";

            $where = array();
            $where[] = 'd.type="' . DOCUMENT_SHEET . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';

            if ($priority == 'sheet') {
                if (!empty($filters['from_date'])) {
                    $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                }
                if (!empty($filters['to_date'])) {
                    $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                }
            }
            if (!empty(self::$available_companies)) {
                $where[] = 'gt2.free_field3 IN ("' . implode('","', self::$available_companies) . '")';
            }
            if (!empty($filters['payment_status'])) {
                $where[] = 'tg.tag_id IN ("' . implode('","', $filters['payment_status']) . '")';
            }
            if (!empty(self::$available_procedures)) {
                $where[] = 'gt2.article_id IN ("' . implode('","', self::$available_procedures) . '")';
            }
            if (!empty(self::$medics_nurses)) {
                $where[] = 'gt2.free_field2 IN ("' . implode('","', self::$medics_nurses) . '")';
            }
            if (!empty($filters['show_cosmetics_only'])) {
                $where[] = 'nom.type="' . NOM_COSMETICS . '"';
            }
            if (!empty($ids_list)) {
                $where[] = 'd.id IN ("' . implode('","', $ids_list) . '")';
            }

            $sql .= 'WHERE ' . implode(' AND ', $where) . "\n";
            $sql .= 'ORDER BY d.id ASC, nom.type ASC';
            return $registry['db']->GetAll($sql);
        }

        /*
         * Function to get the payments for the final results table
         */
        public static function getPayments(&$registry, $filters, $priority, $exclude_ids = array()) {
            $payments_list = array();
            if ($priority == 'sheet' && empty(self::$included_sheets_ids)) {
                return $payments_list;
            }

            $sql = 'SELECT d.id as doc_id, d.full_num as doc_num, DATE_FORMAT(d.date, "%Y-%m-%d") as doc_date, d_cstm_sheet.value as related_sheet, ' . "\n" .
                   '  d_cstm_container.value as container, nom_i18n.name as container_name, d_cstm_value.value as payment_value, d_cstm_company.value as company ' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_sheet' . "\n" .
                   '  ON (d_cstm_sheet.model_id=d.id AND d_cstm_sheet.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_SHEET_ID]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_SHEET_ID] : '') . '"' . ($priority == 'sheet' ? ' AND d_cstm_sheet.value IN ("' . implode('","', self::$included_sheets_ids) . '")' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_container' . "\n" .
                   '  ON (d_cstm_container.model_id=d.id AND d_cstm_container.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_CONTAINER] : '') . '" AND d_cstm_container.num=d_cstm_sheet.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_company' . "\n" .
                   '  ON (d_cstm_company.model_id=d.id AND d_cstm_company.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_COMPANY]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_COMPANY] : '') . '" AND d_cstm_container.num=d_cstm_company.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as nom_i18n' . "\n" .
                   '  ON (nom_i18n.parent_id=d_cstm_container.value AND nom_i18n.lang="' . $registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' d_cstm_value' . "\n" .
                   '  ON (d_cstm_value.model_id=d.id AND d_cstm_value.var_id="' . (isset(self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE]) ? self::$additional_vars_payment[DOCUMENT_PAYMENT_VALUE] : '') . '" AND d_cstm_value.num=d_cstm_sheet.num)' . "\n";

            $where = array();
            $where[] = 'd.type="' . DOCUMENT_PAYMENT . '"';
            $where[] = 'd.active=1';
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.substatus!="' . DOCUMENT_PAYMENT_SUBSTATUS_ANNULLED . '"';
            if (count(self::$available_cashboxes)) {
                $where[] = 'd_cstm_container.value IN ("' . implode('","', array_keys(self::$available_cashboxes)) . '")';
            }
            if (!empty($exclude_ids)) {
                $where[] = 'd.id  NOT IN ("' . implode('","', $exclude_ids) . '")';
            }
            if ($priority != 'sheet') {
                if (!empty($filters['from_date'])) {
                    $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                }
                if (!empty($filters['to_date'])) {
                    $where[] = 'DATE_FORMAT(d.date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                }
            }

            $sql .= 'WHERE ' . implode(' AND ', $where) . "\n";
            $payments_list = $registry['db']->GetAll($sql);

            return $payments_list;
        }

        /*
         * Function to get the payments for the final results table
         */
        public static function rearrangePayments($table, $payments_list, $filters, $priority) {
            foreach ($payments_list as $pl) {
                if (isset(self::$sheet_dates[$pl['related_sheet']])) {
                    if ($priority == 'sheet') {
                        self::$payments_ids[] = $pl['doc_id'];
                    }
                    $table[self::$sheet_dates[$pl['related_sheet']]]['sheets'][$pl['related_sheet']]['payments'][] = array(
                        'id'             => $pl['doc_id'],
                        'full_num'       => $pl['doc_num'],
                        'date'           => $pl['doc_date'],
                        'company'        => $pl['company'],
                        'container'      => $pl['container'],
                        'container_name' => $pl['container_name'],
                        'sum'            => $pl['payment_value']
                    );
                    $table[self::$sheet_dates[$pl['related_sheet']]]['sheets'][$pl['related_sheet']]['containers'][] = $pl['container'];
                }
            }

            foreach ($table as $ft_date => $date_data) {
                foreach ($date_data['sheets'] as $sheet_id => $sheet_data) {
                    if (!empty($filters['cashbox_bank']) && !count(array_intersect(array_keys(self::$available_cashboxes), $sheet_data['containers']))) {
                        unset($table[$ft_date]['sheets'][$sheet_id]);
                        if (empty($table[$ft_date]['sheets'])) {
                            unset($table[$ft_date]);
                        }
                    }
                }
            }

            return $table;
        }
    }
?>