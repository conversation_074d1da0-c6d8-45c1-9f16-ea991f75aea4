/**
 * Function to load the statuses of selected document type
 */
function changeDocumentStatuses(element) {
    // prepare ajax options
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function (t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            eval('var statuses = ' + t.responseText + ';');

            var status = $('status');
            status.options.length = 0;

            if (statuses.length) {
                removeClass(status, 'missing_records');
                status.options[0] = new Option(i18n['labels']['please_select'].toLowerCase(), '', false, true);
                addClass(status.options[0], 'undefined');

                for (var j = 0; j < statuses.length; j++) {
                    status.options[j+1] = new Option(statuses[j]['label'], statuses[j]['option_value'], false, false);
                }
                toggleUndefined(status);
            } else {
                status.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(status, 'missing_records');
            }

            Effect.Fade('loading');
        },
        on404: function (t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function (t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_change_statuses&report_type=' + $('report_type').value + '&selected_doc_type=' + element.value;
    new Ajax.Request(url, opt);

}
