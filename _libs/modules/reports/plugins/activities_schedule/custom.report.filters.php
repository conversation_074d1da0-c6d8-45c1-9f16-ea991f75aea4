<?php

class Custom_Report_Filters extends Report_Filters {

    /**
     * @var Registry
     */
    private static $registry;

    /**
     * Defining filters for the certain type of report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(Registry &$registry) {

        self::$registry = &$registry;

        // $filters - array containing description of all filters
        $filters = array();

        /** @var Activities_Schedule $report_class */
        $report_class = Reports::getPluginFactory($this->reportName);

        $settings = $report_class::getReportSettings($registry, $this->reportName);

        $filters['status'] = array(
            'name' => 'status',
            'type' => 'checkbox_group',
            'options' => array(
                array(
                    'option_value' => 'active',
                    'label' => $registry['translater']->translate('reports_source_records_active'),
                ),
                array(
                    'option_value' => 'inactive',
                    'label' => $registry['translater']->translate('reports_source_records_inactive'),
                ),
            ),
            'options_align' => 'horizontal',
            'label' => $this->i18n('reports_source_records'),
        );

        // Prepare filter: trademark
        self::loadDefaultFilter($registry, $filters, 'autocompleter', array(
            'filter_name' => 'trademark',
            'autocomplete_type' => 'nomenclatures',
            'autocomplete' => array(
                'filters' => array(
                    '<type_keyword>' => 'trademark',
                ),
            ),
        ));
        $filters['trademark']['label'] = $registry['translater']->translate('trademark');

        $step = 25;
        $iter = 0;
        $filters['progress'] = array(
            'name' => 'progress',
            'type' => 'dropdown',
            'options' => array_map(function($lower) use (&$iter, $step, $registry) {
                $upper = '';
                if ($lower < 100) {
                    $upper = $lower + $step - 1;
                }
                $iter++;
                return array(
                    'option_value' => "progress_{$lower}_{$upper}",
                    'label' => (
                        $lower ?
                        (
                            $upper ?
                            $registry['translater']->translate('from') . " {$lower}% " .
                                mb_strtolower($registry['translater']->translate('to'), mb_internal_encoding()) . ' ' . "{$upper}%" :
                            "{$lower}%"
                        ) :
                        $registry['translater']->translate('to') . ' ' . "{$upper}%"
                    ),
                );
            }, range(0, 100, $step)),
            //'skip_please_select' => 1,
            'label' => $this->i18n('reports_schedule_progress'),
        );

        self::loadDefaultFilter($registry, $filters, 'period_from_to', array());

        // toggle between single mode and list mode depending on data in request
        if (!array_diff_key($settings['model_keys'], $registry['request']->getAll())) {
            $registry->set('hide_report_selection', true, true);
            $registry->set('hide_filters', true, true);
            // if coming from button in model, change shortened URL in session to lead back to model
            if ($registry['session'][$registry['session_reports_param']] && !array_intersect_key($filters, $registry['session'][$registry['session_reports_param']])) {
                $url_params =
                    array(
                        $registry['module_param'] => $registry['request']['source_module'],
                        $registry['request']['source_module'] => $settings['module_params'][$registry['request']['source_module']]['action'],
                        $settings['module_params'][$registry['request']['source_module']]['action'] => $registry['request']['model_id'],
                    );
                array_walk($url_params, function(&$v, $k) { $v = array('param' => $k, 'value' => $v); });
                $url_params = General::encodeUrlData($url_params);
                $registry['session']->set('d', $url_params, $registry['session_reports_param'], true);
                $registry['request']->set('d', $url_params, 'get', true);
            }
        } elseif ($registry['session_reports_param'] && $registry['session'][$registry['session_reports_param']] &&
        array_intersect_key($settings['model_keys'], $registry['session'][$registry['session_reports_param']])) {
            // switching from single mode to list mode - clear generated flag and model-related filters
            if (!array_intersect_key($filters, $registry['session'][$registry['session_reports_param']])) {
                $registry->remove('generated_report');
                $registry['session']->remove('generated', $registry['session_reports_param']);
            }
            $registry['session']->set(
                $registry['session_reports_param'],
                array_diff_key($registry['session'][$registry['session_reports_param']], $settings['model_keys']),
                '',
                true
            );
        }

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    function processDependentFilters(array &$filters) {
        $registry = &self::$registry;

        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        if (!$registry['generated_report']) {
            $filters['status']['value'] = array('active');
        }

        return $filters;
    }
}
