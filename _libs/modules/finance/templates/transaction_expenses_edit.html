<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$transaction_expense->get('model_lang')|default:$lang}" />
<input type="hidden" name="id" id="id" value="{$transaction_expense->get('id')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='transaction_expenses_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$transaction_expense->get('name')|escape}" title="{#finance_transaction_expenses_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_operation"><label for="operation"{if $messages->getErrors('operation')} class="error"{/if}>{help label='transaction_expenses_operation'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="operation" id="operation" title="{#finance_transaction_expenses_operation#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              {assign var='option_value' value='withdraw'}
              {capture assign='option_label'}finance_transaction_expenses_operation_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('operation')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='transfer'}
              {capture assign='option_label'}finance_transaction_expenses_operation_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('operation')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='receive'}
              {capture assign='option_label'}finance_transaction_expenses_operation_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('operation')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='deposit'}
              {capture assign='option_label'}finance_transaction_expenses_operation_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('operation')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_type"><label for="type"{if $messages->getErrors('type')} class="error"{/if}>{help label='transaction_expenses_type'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="type" id="type" title="{#finance_transaction_expenses_type#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              {assign var='option_value' value='bank'}
              {capture assign='option_label'}finance_transaction_expenses_type_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('type')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='cash'}
              {capture assign='option_label'}finance_transaction_expenses_type_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('type')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_transfer"><label for="transfer"{if $messages->getErrors('transfer')} class="error"{/if}>{help label='transaction_expenses_transfer'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select class="selbox" name="transfer" id="transfer" title="{#finance_transaction_expenses_transfer#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              {assign var='option_value' value='RINGS'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='internal'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='local'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='international'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='with_warning'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
              {assign var='option_value' value='without_warning'}
              {capture assign='option_label'}finance_transaction_expenses_transfer_{$option_value}{/capture}
                <option value="{$option_value}"{if $option_value eq $transaction_expense->get('transfer')} selected="selected"{/if}>{$smarty.config.$option_label}</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_bank_account"><label for="bank_account"{if $messages->getErrors('bank_account')} class="error"{/if}>{help label='transaction_expenses_bank_account'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {include file=`$theme->templatesDir`input_dropdown.html
                     name='bank_account'
                     options=$bank_accounts
                     value=$transaction_expense->get('bank_account')
                     required=1
                     width=200
                     standalone=true
                     label=#finance_transaction_expenses_bank_account#
            }
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_from_amount"><label for="from_amount"{if $messages->getErrors('from_amount')} class="error"{/if}>{help label='transaction_expenses_from_amount'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short hright" name="from_amount" id="from_amount" value="{$transaction_expense->get('from_amount')|escape}" title="{#finance_transaction_expenses_from_amount#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_to_amount"><label for="to_amount"{if $messages->getErrors('to_amount')} class="error"{/if}>{help label='transaction_expenses_to_amount'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short hright" name="to_amount" id="to_amount" value="{$transaction_expense->get('to_amount')|escape}" title="{#finance_transaction_expenses_to_amount#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_fixed_transaction"><label for="fixed_transaction"{if $messages->getErrors('fixed_transaction')} class="error"{/if}>{help label='transaction_expenses_fixed_transaction'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short hright" name="fixed_transaction" id="fixed_transaction" value="{$transaction_expense->get('fixed_transaction')|escape}" title="{#finance_transaction_expenses_fixed_transaction#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_free_amount"><label for="free_amount"{if $messages->getErrors('free_amount')} class="error"{/if}>{help label='transaction_expenses_free_amount'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short hright" name="free_amount" id="free_amount" value="{$transaction_expense->get('free_amount')|escape}" title="{#finance_transaction_expenses_free_amount#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_percentage"><label for="percentage"{if $messages->getErrors('percentage')} class="error"{/if}>{help label='transaction_expenses_percentage'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short hright" name="percentage" id="percentage" value="{$transaction_expense->get('percentage')|escape}" title="{#finance_transaction_expenses_percentage#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='transaction_expenses_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#finance_transaction_expenses_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$transaction_expense->get('description')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#edit#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$transaction_expense exclude='is_portal'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
