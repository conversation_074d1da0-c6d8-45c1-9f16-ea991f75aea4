<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox">{help label='cashboxes_name'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$cashbox->get('name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_code'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$cashbox->get('code')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_company'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$cashbox->get('company_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_amount'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
          {assign var=amounts value=$cashbox->getAmount()}
          {if $amounts}
            {foreach from=$amounts item=amount key=key}
              {$amount} {$key}<br />
            {/foreach}
          {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_office'}</td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            {$cashbox->get('office_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_location'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$cashbox->get('location')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='cashboxes_description'}</td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            {$cashbox->get('description')|escape|mb_wordwrap|url2href|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$cashbox exclude='is_portal'}
</div>
