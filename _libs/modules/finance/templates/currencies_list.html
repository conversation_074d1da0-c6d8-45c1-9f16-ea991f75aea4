<h1>{$title}</h1>
<h2>{#finance_currencies_list_legend#|escape} {$smarty.now|date_format:#date_short#}</h2>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=finance&amp;controller=currencies&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="finance_currency" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=finance&amp;controller=currencies" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.bank.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.bank.link}">{#finance_currencies_bank#|escape}</div></td>
          <td class="t_caption t_border {$sort.date.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.date.link}">{#finance_currencies_date#|escape}</div></td>
          <td class="t_caption t_border {$sort.code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.code.link}">{#finance_currencies_code#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#finance_currencies_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.units.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.units.link}">{#finance_currencies_units#|escape}</div></td>
          <td class="t_caption t_border {$sort.rate.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.rate.link}">{#finance_currencies_rate#|escape}</div></td>
          <td class="t_caption t_border {$sort.update_method.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.update_method.link}">{#finance_currencies_update_method#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$finance_currencies item='currency'}
      {strip}
      {capture assign='info'}
        <strong><u>{#finance_currencies_name#|escape}:</u></strong> ({$currency->get('code')}) {$currency->get('name')}<br />
        <strong>{#finance_currencies_rate#|escape}:</strong> {$currency->get('rate')|string_format:"%.5F"}<br />
        <strong>{#added#|escape}:</strong> {$currency->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$currency->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$currency->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$currency->get('modified_by_name')|escape}<br />
      {/capture}
      {assign var='bank_info' value=$currency->get('bank_info')}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.bank.isSorted}"><a href="{$bank_info.url}" target="_blank" title="{$bank_info.name|escape}">{$currency->get('bank')|escape}</a></td>
          <td class="t_border {$sort.date.isSorted}">{$currency->get('date')|date_format:#date_short#|escape}</td>
          <td class="t_border {$sort.code.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=currencies&amp;{$action_param}=view&amp;view={$currency->get('id')}">{$currency->get('code')|escape}</a></td>
          <td class="t_border {$sort.name.isSorted}" nowrap="nowrap"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=currencies&amp;{$action_param}=view&amp;view={$currency->get('id')}"><img src="{$theme->imagesUrl}flags/currencies/{$currency->get('code')|mb_lower}.png" width="16" height="11" alt="{$currency->get('code')}" border="0" style="padding-right: 5px;" {popup text=$info|escape caption=#system_info#|escape width=250} />{$currency->get('name')|escape}</a></td>
          <td class="t_border hright {$sort.units.isSorted}">{$currency->get('units')|escape}</td>
          <td class="t_border hright {$sort.rate.isSorted}">{$currency->get('rate')|string_format:'%.5f'}</td>
          <td class="t_border {$sort.update_method.isSorted}">{capture assign='update_method_var'}finance_currencies_update_methods_{$currency->get('update_method')}{/capture}{$smarty.config.$update_method_var}</td>
          <td class="hcenter" nowrap="nowrap" width="70">
            {include file=`$theme->templatesDir`single_actions_list.html object=$currency exclude='delete'}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="10">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_selection_stats=true
}
    </td>
  </tr>
</table>
