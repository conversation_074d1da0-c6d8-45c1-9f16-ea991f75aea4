<h1>{$title}</h1>

<div id="form_container">
{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance_incomes_reasons_attachments" id="finance_incomes_reasons_attachments" action="{$submitLink}" method="post" enctype="multipart/form-data">
<input type="hidden" name="id" id="id" value="{$finance_incomes_reason->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_incomes_reason->get('model_lang')|default:$lang}" />

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_incomes_reasons_info_header.html}
        <tr>
          <td colspan="3" nowrap="nowrap" class="nopadding">
            <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
              <tr>
                <td class="t_caption2" colspan="7"><div class="strong">{#attachments_genfiles_title#|mb_upper}</div></td>
              </tr>
              <tr>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#num#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_path#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_title#|escape}{#required#}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_description#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_revision#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_permission#|escape}</div></td>
                <td class="t_caption3">&nbsp;</td>
              </tr>
  {foreach name='i' from=$finance_incomes_reason->get('genfiles') item='file'}
    {capture assign='modified'}{if $modified_genfiles && array_key_exists($file->get('id'), $modified_genfiles)}1{else}0{/if}{/capture}
    {capture assign='gidx'}{if $modified}{$file->get('id')}{else}0{/if}{/capture}
    {capture assign='info'}
      {strip}
        <strong>{#attachments_path#|escape}:</strong> {$file->get('filename')|escape}<br />
        <strong>{#attachments_title#|escape}:</strong> {$file->get('name')|escape}<br />
        <strong>{#attachments_revision#|escape}:</strong> {$file->get('revision')|string_format:'%02d'}<br />
        <strong>{#attachments_pattern#|escape}:</strong> {$file->get('pattern_name')|escape}<br />
        <strong>{#attachments_added#|escape}:</strong> {$file->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('added_by_name')|escape}<br />
        <strong>{#attachments_modified#|escape}:</strong> {$file->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('modified_by_name')|escape}<br />
        <strong>{#attachments_description#|escape}:</strong> {$file->get('description')|mb_truncate|escape}<br />
        {if $file->isDeleted()}<strong>{#deleted#|escape}:</strong> {$file->get('deleted')|date_format:#date_mid#|escape}{if $file->get('deleted_by_name')} {#by#|escape} {$file->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
        <span class="translations">
        {foreach from=$file->get('translations') item='trans'}
          <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $file->get('model_lang')} class="selected"{/if} />
        {/foreach}
        </span>
      {/strip}
    {/capture}
    {if !$file->get('not_exist')}
      {capture assign='row_link'}{strip}{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=viewfile&amp;viewfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}{/strip}{/capture}
      {capture assign='row_link_clauses'}
        {strip}
          onclick="window.open('{$row_link}', '_blank')" style="cursor:pointer;"
        {/strip}
      {/capture}
    {else}
      {capture assign='row_link'}{/capture}
      {capture assign='row_link_clauses'}{/capture}
    {/if}
              <tr class="{cycle values='t_odd,t_even'}{if $erred_modified_genfiles && @in_array($file->get('id'), $erred_modified_genfiles)} t_deleted{/if}">
                <td{if $erred_modified_genfiles && @in_array($file->get('id'), $erred_modified_genfiles)} class="error"{/if} align="right">
            {if $file->get('not_exist')}
                <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=#finance_incomes_reasons_file_not_exist#|escape} />
            {/if}
                {$smarty.foreach.i.iteration}.</td>
                <td {$row_link_clauses}>
                  <input type="hidden" name="g_file_filenames[{$file->get('id')}]" value="{$file->get('filename')}" />
                  <input type="hidden" name="g_file_indices[{$file->get('id')}]" value="{$smarty.foreach.i.iteration}" />
                  <div id="g_file_paths_value_{$file->get('id')}"><img src="{$theme->imagesUrl}{$file->getIconName()}.png" width="16" height="16" border="0" alt="" /> {$file->get('filename')|escape}</div>
                </td>
                <td {$row_link_clauses}>
                  {capture assign='file_name'}{if $file->get('name')}{$file->get('name')}{else}{$file->get('filename')}{/if}{/capture}
                  {if $modified}
                  <div id="g_file_names_value_{$gidx}" style="display: none"><img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file_name caption=#attachments_title#|escape} /> {$file->get('name')|mb_truncate:20|escape}</div>
                  <input type="text" name="g_file_names[{$gidx}]" id="g_file_names_{$gidx}" value="{$modified_genfiles.$gidx.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_title#|escape}" style="width: 120px;" /></td>
                  {else}
                  <div id="g_file_names_value_{$file->get('id')}"><img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file_name caption=#attachments_title#|escape} /> {$file->get('name')|mb_truncate:20|escape}</div>
                  <input type="text" name="g_file_names[{$file->get('id')}]" id="g_file_names_{$file->get('id')}" value="{$file->get('name')}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_title#|escape}" style="width: 120px; display: none" disabled="disabled" /></td>
                  {/if}
                <td {$row_link_clauses}>
                  {if $modified}
                  <div id="g_file_descriptions_value_{$gidx}"{if $modified} style="display: none"{/if}>{if $file->get('description')}<img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file->get('description') caption=#attachments_description#|escape} /> {$file->get('description')|mb_truncate:20|escape}{else}&nbsp;{/if}</div>
                  <input type="text" name="g_file_descriptions[{$gidx}]" id="g_file_descriptions_{$gidx}" value="{$modified_genfiles.$gidx.description|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_description#|escape}" style="width: 120px;" />
                  {else}
                  <div id="g_file_descriptions_value_{$file->get('id')}">{if $file->get('description')}<img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file->get('description') caption=#attachments_description#|escape} /> {$file->get('description')|mb_truncate:20|escape}{else}&nbsp;{/if}</div>
                  <input type="text" name="g_file_descriptions[{$file->get('id')}]" id="g_file_descriptions_{$file->get('id')}" value="{$file->get('description')}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_description#|escape}" style="width: 120px; display: none" disabled="disabled" />
                  {/if}
                </td>
                <td {$row_link_clauses}>
                  <div id="g_file_revisions_value_{$file->get('id')}">{$file->get('revision')|string_format:'%02d'}</div>
                </td>
                <td {$row_link_clauses}>
                  {if $modified}
                  <div id="g_file_permissions_value_{$file->get('id')}" style="display: none">
                    {if $file->get('permission') eq 'mine'}
                      {#attachments_permission_mine#|escape}
                    {elseif $file->get('permission') eq 'group'}
                      {#attachments_permission_group#|escape}
                    {elseif $file->get('permission') eq 'all'}
                      {#attachments_permission_all#|escape}
                    {/if}
                  </div>
                  <select class="selbox" name="g_file_permissions[{$gidx}]" id="g_file_permissions_{$gidx}" title="{#attachments_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                    <option value="all"{if $modified_genfiles.$gidx.permission eq 'all'} selected="selected"{/if}>{#attachments_permission_all#|escape}</option>
                    <option value="group"{if $modified_genfiles.$gidx.permission eq 'group'} selected="selected"{/if}>{#attachments_permission_group#|escape}</option>
                    <option value="mine"{if $modified_genfiles.$gidx.permission eq 'mine'} selected="selected"{/if}>{#attachments_permission_mine#|escape}</option>
                  </select>
                  {else}
                  <div id="g_file_permissions_value_{$file->get('id')}">
                    {if $file->get('permission') eq 'mine'}
                      {#attachments_permission_mine#|escape}
                    {elseif $file->get('permission') eq 'group'}
                      {#attachments_permission_group#|escape}
                    {elseif $file->get('permission') eq 'all'}
                      {#attachments_permission_all#|escape}
                    {/if}
                  </div>
                  <select class="selbox" name="g_file_permissions[{$file->get('id')}]" id="g_file_permissions_{$file->get('id')}" title="{#attachments_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px; display: none;" disabled="disabled">
                    <option value="all"{if $file->get('permission') eq 'all'} selected="selected"{/if}>{#attachments_permission_all#|escape}</option>
                    <option value="group"{if $file->get('permission') eq 'group'} selected="selected"{/if}>{#attachments_permission_group#|escape}</option>
                    <option value="mine"{if $file->get('permission') eq 'mine'} selected="selected"{/if}>{#attachments_permission_mine#|escape}</option>
                  </select>
                  {/if}
                </td>
                <td nowrap="nowrap">
                  <img src="{$theme->imagesUrl}edit.png" width="16" height="16" border="0" alt="{#edit#|escape}" {if $currentUser->checkRights('finance_incomes_reasons', 'edit_file') && ($session->get('currentUserId') eq $file->get('added_by')) && !$file->get('not_exist')}onclick="editFileBrowse(this, {$file->get('id')}, 'generated', '{$row_link}')" class="pointer{if $modified} dimmed{/if}"{else} onclick="alert('{#error_edit_notallowed#|escape:'quotes'|escape}');" class="pointer dimmed"{/if} />
                {if !$file->get('not_exist')}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=viewfile&amp;viewfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}" target="_blank"><img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="" /></a>
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=getfile&amp;getfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}"><img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="" /></a>
                {else}
                  <img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="{#view#|escape}" class="pointer dimmed" />
                  <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
                {/if}
                {if $currentUser->checkRights('finance_incomes_reasons', 'delete_file') && ($session->get('currentUserId') eq $file->get('added_by')) && !$file->get('not_exist')}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=delfile&amp;delfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}" onclick="return confirmAction('delete_file', function(el) {ldelim} window.location.href = el.href; {rdelim}, this);" title="{#delete#|escape}"><img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" /></a>
                {else}
                  <img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" onclick="alert('{#error_delete_notallowed#|escape:'quotes'|escape}');" class="pointer dimmed"/>
                {/if}
                <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
                </td>
              </tr>
{foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="7">{#no_items_found#|escape}</td>
              </tr>
{/foreach}

<!-- ATTACHMENTS -->
              <tr>
                <td class="t_caption2" colspan="7"><div class="strong">{#attachments_files_title#|mb_upper}</div></td>
              </tr>
              <tr>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#num#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_path#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_title#|escape}{#required#}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_description#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_revision#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_permission#|escape}</div></td>
                <td class="t_caption3">&nbsp;</td>
              </tr>
  {foreach name='i' from=$finance_incomes_reason->get('attachments') item='file'}
    {capture assign='modified'}{if $modified_files && array_key_exists($file->get('id'), $modified_files)}1{else}0{/if}{/capture}
    {capture assign='midx'}{if $modified}{$file->get('id')}{else}0{/if}{/capture}
    {capture assign='info'}
      {strip}
        <strong>{#attachments_path#|escape}:</strong> {$file->get('filename')|escape}<br />
        <strong>{#attachments_title#|escape}:</strong> {$file->get('name')|escape}<br />
        <strong>{#attachments_revision#|escape}:</strong> {$file->get('revision')|string_format:'%02d'}<br />
        <strong>{#attachments_added#|escape}:</strong> {$file->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('added_by_name')|escape}<br />
        <strong>{#attachments_modified#|escape}:</strong> {$file->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$file->get('modified_by_name')|escape}<br />
        <strong>{#attachments_description#|escape}:</strong> {$file->get('description')|mb_truncate|escape}<br />
        {if $file->isDeleted()}<strong>{#deleted#|escape}:</strong> {$file->get('deleted')|date_format:#date_mid#|escape}{if $file->get('deleted_by_name')} {#by#|escape} {$file->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
        <span class="translations">
        {foreach from=$file->get('translations') item='trans'}
          <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $file->get('model_lang')} class="selected"{/if} />
        {/foreach}
        </span>
      {/strip}
    {/capture}
    {if !$file->get('not_exist')}
      {capture assign='row_link'}{strip}{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=viewfile&amp;viewfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}{/strip}{/capture}
      {capture assign='row_link_clauses'}
        {strip}
          onclick="window.open('{$row_link}', '_blank')" style="cursor:pointer;"
        {/strip}
      {/capture}
    {else}
      {capture assign='row_link'}{/capture}
      {capture assign='row_link_clauses'}{/capture}
    {/if}
              <tr class="{cycle values='t_odd,t_even'}{if $erred_modified_files && @in_array($file->get('id'), $erred_modified_files)} t_deleted{/if}">
                <td{if $erred_modified_files && @in_array($file->get('id'), $erred_modified_files)} class="error"{/if} align="right">
            {if $file->get('not_exist')}
                <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=#finance_incomes_reasons_file_not_exist#|escape} />
            {/if}
{$smarty.foreach.i.iteration}.</td>
                <td {$row_link_clauses}>
                  <input type="hidden" name="file_filenames[{$file->get('id')}]" value="{$file->get('filename')}" />
                  <input type="hidden" name="file_indices[{$file->get('id')}]" value="{$smarty.foreach.i.iteration}" />
                  {if $modified}
                  <div id="file_paths_value_{$midx}" style="display: none"><img src="{$theme->imagesUrl}{$file->getIconName()}.png" width="16" height="16" border="0" alt="" /> {$modified_files.$midx.filename|escape}</div>
                  <input type="file" class="filebox" name="file_paths[{$midx}]" id="file_paths_{$midx}" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_path#|escape}" />
                  {else}
                  <div id="file_paths_value_{$file->get('id')}"><img src="{$theme->imagesUrl}{$file->getIconName()}.png" width="16" height="16" border="0" alt="" /> {$file->get('filename')|escape}</div>
                  <input type="file" class="filebox" name="file_paths[{$file->get('id')}]" id="file_paths_{$file->get('id')}" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#attachments_path#|escape}" style="display: none" disabled="disabled" />
                  {/if}
                </td>
                <td {$row_link_clauses}>
                  {capture assign='file_name'}{if $file->get('name')}{$file->get('name')}{else}{$file->get('filename')}{/if}{/capture}
                  {if $modified}
                  <div id="file_names_value_{$midx}" style="display: none"><img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file_name caption=#attachments_title#|escape} /> {$file->get('name')|mb_truncate:20|escape}</div>
                  <input type="text" name="file_names[{$midx}]" id="file_names_{$midx}" value="{$modified_files.$midx.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_title#|escape}" style="width: 120px;" /></td>
                  {else}
                  <div id="file_names_value_{$file->get('id')}"><img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file_name caption=#attachments_title#|escape} /> {$file->get('name')|mb_truncate:20|escape}</div>
                  <input type="text" name="file_names[{$file->get('id')}]" id="file_names_{$file->get('id')}" value="{$file->get('name')}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_title#|escape}" style="width: 120px; display: none" disabled="disabled" /></td>
                  {/if}
                <td {$row_link_clauses}>
                  {if $modified}
                  <div id="file_descriptions_value_{$midx}"{if $modified} style="display: none"{/if}>{if $file->get('description')}<img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file->get('description') caption=#attachments_description#|escape} /> {$file->get('description')|mb_truncate:20|escape}{else}&nbsp;{/if}</div>
                  <input type="text" name="file_descriptions[{$midx}]" id="file_descriptions_{$midx}" value="{$modified_files.$midx.description|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_description#|escape}" style="width: 120px;" />
                  {else}
                  <div id="file_descriptions_value_{$file->get('id')}">{if $file->get('description')}<img src="{$theme->imagesUrl}small/info.png" width="11" height="11" border="0" alt="{#info#|escape}" class="help" {popup text=$file->get('description') caption=#attachments_description#|escape} /> {$file->get('description')|mb_truncate:20|escape}{else}&nbsp;{/if}</div>
                  <input type="text" name="file_descriptions[{$file->get('id')}]" id="file_descriptions_{$file->get('id')}" value="{$file->get('description')}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_description#|escape}" style="width: 120px; display: none" disabled="disabled" />
                  {/if}
                </td>
                <td {$row_link_clauses}>
                  {if $modified}
                  <div id="file_revisions_value_{$midx}" style="display: none">{$file->get('revision')|string_format:'%02d'}</div>
                  <select class="selbox" name="file_revisions[{$midx}]" id="file_revisions_{$midx}" title="{#attachments_revisions#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px;">
                    <option value=""{if $modified_files.$midx.revision eq ''} selected="selected"{/if}>{#attachments_new_revision#|escape}</option>
                    <option value="{$file->get('revision')}"{if $modified_files.$midx.revision eq $file->get('revision')} selected="selected"{/if}>{$file->get('revision')|string_format:'%02d'}</option>
                  </select>
                  {else}
                  <div id="file_revisions_value_{$file->get('id')}">{$file->get('revision')|string_format:'%02d'}</div>
                  <select class="selbox" name="file_revisions[{$file->get('id')}]" id="file_revisions_{$file->get('id')}" title="{#attachments_revisions#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px; display: none" disabled="disabled">
                    <option value="">{#attachments_new_revision#|escape}</option>
                    <option value="{$file->get('revision')}" selected="selected">{$file->get('revision')|string_format:'%02d'}</option>
                  </select>
                  {/if}
                </td>
                <td {$row_link_clauses}>
                  {if $modified}
                  <div id="file_permissions_value_{$file->get('id')}" style="display: none">
                    {if $file->get('permission') eq 'mine'}
                      {#attachments_permission_mine#|escape}
                    {elseif $file->get('permission') eq 'group'}
                      {#attachments_permission_group#|escape}
                    {elseif $file->get('permission') eq 'all'}
                      {#attachments_permission_all#|escape}
                    {/if}
                  </div>
                  <select class="selbox" name="file_permissions[{$midx}]" id="file_permissions_{$midx}" title="{#attachments_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                    <option value="all"{if $modified_files.$midx.permission eq 'all'} selected="selected"{/if}>{#attachments_permission_all#|escape}</option>
                    <option value="group"{if $modified_files.$midx.permission eq 'group'} selected="selected"{/if}>{#attachments_permission_group#|escape}</option>
                    <option value="mine"{if $modified_files.$midx.permission eq 'mine'} selected="selected"{/if}>{#attachments_permission_mine#|escape}</option>
                  </select>
                  {else}
                  <div id="file_permissions_value_{$file->get('id')}">
                    {if $file->get('permission') eq 'mine'}
                      {#attachments_permission_mine#|escape}
                    {elseif $file->get('permission') eq 'group'}
                      {#attachments_permission_group#|escape}
                    {elseif $file->get('permission') eq 'all'}
                      {#attachments_permission_all#|escape}
                    {/if}
                  </div>
                  <select class="selbox" name="file_permissions[{$file->get('id')}]" id="file_permissions_{$file->get('id')}" title="{#attachments_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px; display: none;" disabled="disabled">
                    <option value="all"{if $file->get('permission') eq 'all'} selected="selected"{/if}>{#attachments_permission_all#|escape}</option>
                    <option value="group"{if $file->get('permission') eq 'group'} selected="selected"{/if}>{#attachments_permission_group#|escape}</option>
                    <option value="mine"{if $file->get('permission') eq 'mine'} selected="selected"{/if}>{#attachments_permission_mine#|escape}</option>
                  </select>
                  {/if}
                </td>
                <td nowrap="nowrap">
                  <img src="{$theme->imagesUrl}edit.png" width="16" height="16" border="0" alt="{#edit#|escape}" {if $currentUser->checkRights('finance_incomes_reasons', 'edit_file') && $session->get('currentUserId') eq $file->get('added_by') && !$file->get('not_exist')}onclick="editFileBrowse(this, {$file->get('id')}, '', '{$row_link}')" class="pointer{if $modified} dimmed{/if}"{else} onclick="alert('{#error_edit_notallowed#|escape:'quotes'|escape}');" class="pointer dimmed"{/if} />
                {if !$file->get('not_exist')}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=viewfile&amp;viewfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}" target="_blank"><img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="" /></a>
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=getfile&amp;getfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}"><img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="" /></a>
                {else}
                  <img src="{$theme->imagesUrl}view.png" width="16" height="16" border="0" alt="{#view#|escape}" class="pointer dimmed" />
                  <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
                {/if}
                  {if $currentUser->checkRights('finance_incomes_reasons', 'delete_file') && ($session->get('currentUserId') eq $file->get('added_by')) && !$file->get('not_exist')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$controller_param}={$controller}&amp;{$action_param}=delfile&amp;delfile={$finance_incomes_reason->get('id')}&amp;file={$file->get('id')}" onclick="return confirmAction('delete_file', function(el) {ldelim} window.location.href = el.href; {rdelim}, this);" title="{#delete#|escape}"><img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" /></a>
                  {else}
                    <img src="{$theme->imagesUrl}delete.png" width="16" height="16" border="0" alt="{#delete#|escape}" onclick="alert('{#error_delete_notallowed#|escape:'quotes'|escape}');" class="pointer dimmed"/>
                  {/if}
                  <img src="{$theme->imagesUrl}info.png" width="16" height="16" border="0" alt="" class="help" {popup text=$info|escape caption=#system_info#|escape} />
                </td>
              </tr>
{foreachelse}
              <tr class="{cycle values='t_odd,t_even'}">
                <td class="error" colspan="7">{#no_items_found#|escape}</td>
              </tr>
{/foreach}
              <tr>
                <td class="error" colspan="7">&nbsp;</td>
              </tr>
              <tr>
                <td class="t_caption2" colspan="7"><div class="strong">{#attachments_add_new#|mb_upper}</div></td>
              </tr>
              <tr>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#num#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_path#|escape}{#required#}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_title#|escape}{#required#}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_description#|escape}</div></td>
                <td class="t_caption3 t_border"><div class="t_caption3_title">{#attachments_revision#|escape}</div></td>
                <td class="t_caption3" colspan="2">
                  <div class="t_caption3_title floatl">{#attachments_permission#|escape}</div>
                  <div class="t_buttons">
                    <div id="plusButton" onclick="addFileBrowse('a_file_paths', 'a_file_names', 'a_file_descriptions', 'a_file_revisions', 'a_file_permissions', this)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                    <div id="minusButton"{if count($added_files) eq 1 || count($erred_added_files) eq 1} class="disabled"{/if} onclick="removeFileBrowse(this)" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
                  </div>
                </td>
              </tr>
{foreach name='j' from=$added_files item='file' key='idx'}
  {if empty($file) || $erred_added_files && @in_array($idx, $erred_added_files)}
    {assign var='aidx' value=$idx+1}
              <tr class="{cycle values='t_odd,t_even'}{if !empty($file)} t_deleted{/if}">
                <td class="t_border hright{if !empty($file)} error{/if}" align="right">{$aidx}.</td>
                <td class="t_border">
                  <input type="file" class="filebox" name="a_file_paths[]" id="a_file_paths_{$aidx}" value="" onfocus="highlight(this)" onblur="unhighlight(this)"  title="{#attachments_path#|escape}" />
                </td>
                <td class="t_border">
                  <input type="text" name="a_file_names[]" id="a_file_names_{$aidx}" value="{$file.name}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_title#|escape}" style="width: 120px;" />
                </td>
                <td class="t_border">
                  <input type="text" name="a_file_descriptions[]" id="a_file_descriptions_{$aidx}" value="{$file.description}" onfocus="highlight(this)" onblur="unhighlight(this)" class="txtbox" title="{#attachments_description#|escape}" style="width: 120px;" />
                </td>
                <td class="t_border">
                  <select class="selbox" name="a_file_revisions[]" id="a_file_revisions_{$aidx}" title="{#attachments_revisions#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 50px;">
                    <option value="">{#attachments_new_revision#|escape}</option>
                  </select>
                </td>
                <td colspan="2">
                  <select class="selbox" name="a_file_permissions[]" id="a_file_permissions_{$aidx}" title="{#attachments_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" style="width: 100px;">
                    <option value="all"{if $file.permission eq 'all'} selected="selected"{/if}>{#attachments_permission_all#|escape}</option>
                    <option value="group"{if $file.permission eq 'group'} selected="selected"{/if}>{#attachments_permission_group#|escape}</option>
                    <option value="mine"{if $file.permission eq 'mine'} selected="selected"{/if}>{#attachments_permission_mine#|escape}</option>
                  </select>
                </td>
              </tr>
  {/if}
{/foreach}
            </table>
            <div style="height:10px; border-top: 1px solid #CCCCCC"></div>
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
