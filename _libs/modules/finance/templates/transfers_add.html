<h1>{$title|escape}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_transfer->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table" style="width: 870px;">
      {foreach from=$finance_transfer->get('layouts_details') key='lkey' item='layout'}

        <tr{if !$layout.view || !$layout.visible} style="display: none;"{/if}>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="finance_transfer_{$layout.keyword}_switch">
              <a name="finance_transfer_{$layout.keyword}_index"></a><div class="switch_{if $layout.cookie eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{$layout.name|escape}</div>
            </div>
          </td>
        </tr>

        {if $lkey eq 'name'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <input type="text" class="txtbox" name="name" id="name" value="{$finance_transfer->get('name')|escape}" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
            {else}
              {mb_truncate_overlib text=$finance_transfer->get('name')|escape|default:"&nbsp;"}
              <input type="hidden" name="name" id="name" value="{$finance_transfer->get('name')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'from_company_data'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_from_company_data"><label for="from_company_data"{if $messages->getErrors('from_company_data')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              {include file='input_dropdown.html'
                       standalone=true
                       name='from_company_data'
                       index=0
                       optgroups=$companies_data
                       value=$finance_transfer->get('from_company_data')
                       onchange='changeContainer(this); getContainerAmount(this);'
                       required=1
                       really_required=1
                       readonly=0
                       hidden=0
                       width=200
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {if $finance_transfer->get('from_company_data')}
                {foreach from=$companies_data key='from_company_name' item='company_data'}
                  {foreach from=$companies_data.$from_company_name item='data'}
                    {if $data.option_value eq $finance_transfer->get('from_company_data')}<span class="strong">{$from_company_name|escape}</span> {$data.label|escape}{/if}
                  {/foreach}
                {/foreach}
              {/if}
              <input type="hidden" name="from_company_data" id="from_company_data" value="{$finance_transfer->get('from_company_data')}" />
            {/if}
            <img src="{$theme->imagesUrl}small/info.png" style="margin-left: 5px;" width="11" height="11" border="0" alt="{#system_info#|escape}" class="help" {popup text=#help_finance_after_transfer_container_amount#|escape caption=#finance_after_transfer_container_amount#|escape} />
            <input type="text" name="actual_from_quantity" id="actual_from_quantity" value="{$finance_transfer->get('actual_from_quantity')}" readonly="readonly"  class="txtbox short hright readonly"{if $finance_transfer->get('actual_from_quantity')<0} style="color: red;"{/if} />
            <input type="hidden" name="current_container_from_amount" id="current_container_from_amount" value="{$finance_transfer->get('current_container_from_amount')}" />
            <input type="hidden" name="current_container_from_currency" id="current_container_from_currency" value="{$finance_transfer->get('current_container_from_currency')}" />
            {include file=`$templatesDir`_transfers_amount.html prefix='from'}
          </td>
        </tr>
        {elseif $lkey eq 'to_company_data'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_to_company_data"><label for="to_company_data"{if $messages->getErrors('to_company_data')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              {include file='input_dropdown.html'
                       standalone=true
                       name='to_company_data'
                       index=0
                       optgroups=$companies_data
                       value=$finance_transfer->get('to_company_data')
                       onchange='changeContainer(this); getContainerAmount(this);'
                       required=1
                       really_required=1
                       readonly=0
                       hidden=0
                       width=200
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {if $finance_transfer->get('to_company_data')}
                {foreach from=$companies_data key='to_company_name' item='company_data'}
                  {foreach from=$companies_data.$to_company_name item='data'}
                    {if $data.option_value eq $finance_transfer->get('to_company_data')}<span class="strong">{$to_company_name|escape}</span> {$data.label|escape}{/if}
                  {/foreach}
                {/foreach}
              {/if}
              <input type="hidden" name="to_company_data" id="to_company_data" value="{$finance_transfer->get('to_company_data')}" />
            {/if}
            <img src="{$theme->imagesUrl}small/info.png" style="margin-left: 5px;" width="11" height="11" border="0" alt="{#system_info#|escape}" class="help" {popup text=#help_finance_after_transfer_container_amount#|escape caption=#finance_after_transfer_container_amount#|escape} />
            <input type="text" name="actual_to_quantity" id="actual_to_quantity" value="{$finance_transfer->get('actual_to_quantity')}" readonly="readonly"  class="txtbox short hright readonly"{if $finance_transfer->get('actual_to_quantity')<0} style="color: red;"{/if} />
            <input type="hidden" name="current_container_to_amount" id="current_container_to_amount" value="{$finance_transfer->get('current_container_to_amount')}" />
            <input type="hidden" name="current_container_to_currency" id="current_container_to_currency" value="{$finance_transfer->get('current_container_to_currency')}" />
            {include file=`$templatesDir`_transfers_amount.html prefix='to'}
          </td>
        </tr>
        {elseif $lkey eq 'amount'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_amount"><label for="amount"{if $messages->getErrors('amount')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              <input type="text" class="txtbox short hright" name="amount" id="amount" value="{$finance_transfer->get('amount')|escape}" title="{$layout.name|escape}" onpaste="return false;" ondrop="return false;" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyFloats);" onkeyup="calculateContainerAmount($('from_rate')); calculateContainerAmount($('to_rate')); reacalculateCurrentContainerAmount('from');  reacalculateCurrentContainerAmount('to');" />
            {else}
              {$finance_transfer->get('amount')|escape}
              <input type="hidden" name="amount" id="amount" value="{$finance_transfer->get('amount')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'currency'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_currency"><label for="currency"{if $messages->getErrors('currency')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              <select class="selbox short" name="currency" id="currency" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="getContainerCurrencyRate('from'); getContainerCurrencyRate('to'); getContainerAmount($('from_company_data')); getContainerAmount($('to_company_data'));">
                {foreach from=$currency item='option'}
                  <option value="{$option.currency_code|escape}"{if $option.currency_code eq $finance_transfer->get('currency')} selected="selected"{/if}>{$option.currency_code|escape}</option>
                {/foreach}
              </select>
            {else}
              {foreach from=$currency item='option'}
                {if $option.currency_code eq $finance_transfer->get('currency')}{$option.currency_code|escape}{/if}
              {/foreach}
              <input type="hidden" name="currency" id="currency" value="{$finance_transfer->get('currency')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'transfer_date'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_transfer_date"><label for="transfer_date"{if $messages->getErrors('transfer_date')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="required">{#required#}</td>
          <td>
            {if $layout.edit}
              {include file="input_date.html"
                       standalone=true
                       name='transfer_date'
                       value=$finance_transfer->get('transfer_date')
                       width=200
                       show_calendar_icon=true
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {if $finance_transfer->get('transfer_date') ne 0}{$finance_transfer->get('transfer_date')|escape|date_format:#date_short#}{/if}
              <input type="hidden" name="transfer_date" id="transfer_date" value="{$finance_transfer->get('transfer_date')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'reason'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_reason"><label for="reason"{if $messages->getErrors('reason')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <textarea class="areabox" name="reason" id="reason" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_transfer->get('reason')|escape}</textarea>
            {else}
              {$finance_transfer->get('reason')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
              <input type="hidden" name="reason" id="reason" value="{$finance_transfer->get('reason')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'note'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_note"><label for="note"{if $messages->getErrors('note')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              <textarea class="areabox" name="note" id="note" title="{$layout.name|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$finance_transfer->get('note')|escape}</textarea>
            {else}
              {$finance_transfer->get('note')|escape|mb_wordwrap:70|url2href|default:"&nbsp;"}
              <input type="hidden" name="note" id="note" value="{$finance_transfer->get('note')|escape}" />
            {/if}
          </td>
        </tr>
        {elseif $lkey eq 'employee'}
        <tr id="finance_transfer_{$layout.keyword}"{if ($layout.visible && $layout.cookie eq 'off') || !$layout.view} style="display: none;"{/if}>
          <td class="labelbox"><a name="error_employee"><label for="employee"{if $messages->getErrors('employee')} class="error"{/if}>{help label_content=$layout.name text_content=$layout.description}</label></a></td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $layout.edit}
              {include file=`$theme->templatesDir`input_autocompleter.html
                       name='employee'
                       autocomplete_type='customers'
                       stop_customer_details=1
                       autocomplete_var_type='basic'
                       autocomplete_buttons='search clear'
                       value=$finance_transfer->get('employee')
                       value_name=$finance_transfer->get('employee_name')
                       filters_array=$autocomplete_employee_filters
                       width=244
                       standalone=true
                       label=$layout.name
                       help=$layout.description
              }
            {else}
              {$finance_transfer->get('employee_name')|escape}
              <input type="hidden" name="employee" id="employee" value="{$finance_transfer->get('employee')|default:0}" />
            {/if}
          </td>
        </tr>
        {/if}
      {/foreach}
        <tr>
          <td colspan="3" style="padding: 15px;">
            {strip}
            <input type="hidden" name="status" id="status" value="" />
            {foreach from=$after_action_options item='fin_action' name='ai' key='ak'}
              <button type="submit" name="saveButton1" id="submit_{$ak}" class="button" onclick="$('status').value='{$fin_action.option_value}';" title="{$fin_action.description|escape}">{$fin_action.label|escape}</button>
            {/foreach}
            {include file=`$theme->templatesDir`cancel_button.html}
            {/strip}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$finance_transfer exclude='is_portal,active'}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
