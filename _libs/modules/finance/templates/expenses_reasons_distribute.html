<h1>{$title|escape}</h1>

<div class="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="finance" enctype="multipart/form-data" action="{$submitLink}" method="post" onsubmit="if (distribution_calc('submit_distribution'))return true; else{ldelim}alert('{#error_distribution_amounts#|escape}');return false;{rdelim}">
<input type="hidden" name="model_lang" id="model_lang" value="{$finance_expenses_reason->get('model_lang')|default:$lang}" />
<input type="hidden" name="id" id="id" value="{$finance_expenses_reason->get('id')}" />
<input type="hidden" name="type" id="type" value="{$finance_expenses_reason->get('type')}" />
<input type="hidden" name="num" id="num" value="{$finance_expenses_reason->get('num')}" />
<input type="hidden" name="type_name" id="type_name" value="{$finance_expenses_reason->get('type_name')}" />
<input type="hidden" name="name" id="name" value="{$finance_expenses_reason->get('name')}" />
<input type="hidden" name="currency" id="currency" value="{$finance_expenses_reason->get('currency')}" />
<input type="hidden" name="issue_date" id="issue_date" value="{$finance_expenses_reason->get('issue_date')}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        {include file=`$templatesDir`_expenses_reasons_info_header.html}
      </table>
    </td>
  </tr>
  <tr>
    <td>
      <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table">
        {if $finance_expenses_reason->get('distributed') neq $smarty.const.PH_FINANCE_DISTRIBUTION_YES || $finance_expenses_reason->get('distributed_outdated')}
        <tr>
          <td>&nbsp;</td>
        </tr>
        <tr class="t_inactive t_v_border t_top_border legend">
          <td>
            {if $finance_expenses_reason->get('distributed') neq $smarty.const.PH_FINANCE_DISTRIBUTION_YES}
              {#finance_expenses_reasons_distribution_not_saved_legend#|escape}
            {else}
              {#finance_expenses_reasons_distribution_outdated_legend#|escape}
            {/if}
          </td>
        </tr>
        {/if}
        <tr>
          <td>
            {include file=`$theme->templatesDir`_distribute.html model=$finance_expenses_reason}
          </td>
        </tr>
        <tr>
          <td style="padding: 10px;">
            <div class="legend">
             {#distribution_currency#|escape} {$finance_expenses_reason->get('currency')}
             <br /><br />
            {if $finance_expenses_reason->get('admit_VAT_credit')}
              {#distribution_no_vat#|escape}
            {else}
              {#distribution_with_vat#|escape} ({$finance_expenses_reason->get('total_vat_rate')} %){if $finance_expenses_reason->get('total_no_vat_reason') || $finance_expenses_reason->get('total_no_vat_reason_text')}<br />{#gt2_total_no_vat_reason#|escape}: {$finance_expenses_reason->get('total_no_vat_reason_text')|escape|default:"&nbsp;"}{/if}
            {/if}
            </div>
          </td>
        </tr>
        <tr>
          <td style="padding: 10px;">
            <button type="submit" name="saveButton1" id="submit_distribution" class="button">{#distribute#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
