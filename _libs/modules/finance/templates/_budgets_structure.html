{** PARAMETERS:
 * items_type       - type of items - can be either 'income' or 'expense'
 * analysis_items    - array with income/expense items
 *}
{array assign='months' eval='range(1, 12)'}

{capture assign='items_type_name'}finance_budgets_{$items_type}{/capture}
{capture assign='amount_type_name'}{$items_type}_amount{/capture}
{assign var='total_amount' value=$finance_budget->get($amount_type_name)}
{assign var='cookie_var' value='finance_budgets_months'}
{assign var='errors' value=$finance_budget->get('errors')}
{assign var='errors' value=$errors.$items_type}
{assign var='table_name' value=`$items_type`_table}

<div class="budget_amount_caption">{#finance_budgets_budget#|mb_upper|escape} {#finance_budgets_year#|mb_upper|escape} {$finance_budget->get('year')} / {$smarty.config.$items_type_name|escape} - <span class="budget_amount">{$total_amount} {if $finance_budget->get('currency')}{$finance_budget->get('currency')}{else}{$currency_options.0.label}{/if}</span></div>

<table class="t_table" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" class="t_grouping_table_multilevel t_table1" id="{$table_name}">
        <tr>
          <th nowrap="nowrap"><div style="width: 15px">{#num#|escape}</div></th>
          <th width="265" nowrap="nowrap">
            <div class="nopadding" style="width: 262px;">
              <div style="padding-right: 10px;">{#finance_budgets_item#|escape}</div><div class="switch_{if $smarty.cookies.$cookie_var eq 'off'}expand{else}collapse{/if}_cols_left" onclick="toggleFinanceBudgetsMonths(this)" title="{#finance_budgets_toggle_months#|escape}"></div>
            </div>
          </th>
          {foreach name='mi' key='mk' from=$months item='month'}
          <th width="80" nowrap="nowrap" class="month_amount"{if $smarty.cookies.$cookie_var eq 'off'} style="display: none"{/if}>
            <div style="width: 74px">{$month_names.$mk|mb_ucfirst|escape}</div>
          </th>
          {/foreach}
          <th width="80" nowrap="nowrap">
            <div style="padding-left: 10px; width: 72px">{#finance_budgets_budget#|escape}</div><div class="switch_{if $smarty.cookies.$cookie_var eq 'off'}expand{else}collapse{/if}_cols_right" onclick="toggleFinanceBudgetsMonths(this)" title="{#finance_budgets_toggle_months#|escape}"></div>
          </th>
          <th width="160" nowrap="nowrap"><div style="width: 151px">{#finance_budgets_responsible#|escape}</div></th>
          <th width="160" nowrap="nowrap"><div style="width: 151px">{#finance_budgets_controlling#|escape}</div></th>
          <th width="140" nowrap="nowrap"><div style="width: 131px">{#finance_budgets_deadline#|escape}</div></th>
          <th width="80" nowrap="nowrap"><div style="width: 71px">{#finance_budgets_status#|escape}</div></th>
        </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach name='ii' key='ik' from=$analysis_items item='analysis_item'}
        <tr id="{$items_type}_item_row_{$smarty.foreach.ii.iteration}" class="outer_row">
          <td class="hright">{counter name='item_counter' print=true}</td>
          <td>
            {assign var='level' value=$analysis_item.level}
            {if $analysis_item.model_factory && $analysis_item.is_leaf}
              <div class="pointer switch_expand" style="margin-left: {math equation='12*x' x=$level}px; position: absolute;" onclick="toggleFinanceBudgetsItem($('{$items_type}_model_id_{$ik}').form, this, '{$items_type}_model_id_{$ik}', '{$table_name}')"></div> 
              {assign var='level' value=$level+1}
            {/if}
            {$analysis_item.name|escape|default:"&nbsp;"|indent:$level:"&nbsp;&nbsp;&nbsp;"}
            {include file='input_hidden.html'
                     name=`$items_type`[model_id][`$ik`]
                     custom_id=`$items_type`_model_id_`$ik`
                     value=$analysis_item.model_id
                     standalone=true}
            {if $action != 'view'}
              {include file='input_hidden.html'
                       name=`$items_type`[model_factory][`$ik`]
                       custom_id=`$items_type`_model_factory_`$ik`
                       value=$analysis_item.model_factory
                       standalone=true}
              {include file='input_hidden.html'
                       name=`$items_type`[is_leaf][`$ik`]
                       custom_id=`$items_type`_is_leaf_`$ik`
                       value=$analysis_item.is_leaf
                       standalone=true}
            {/if}
          </td>
          {foreach name='mi' key='mk' from=$months item='month'}
          <td class="hright item_{$analysis_item.status} month_amount"{if $smarty.cookies.$cookie_var eq 'off'} style="display: none"{/if}>
            {capture assign='month_amount'}month_amount_{$month}{/capture}
            {$analysis_item.$month_amount|default:'0'}
          </td>
          {/foreach}
          <td class="hright item_{$analysis_item.status}">{$analysis_item.data_amount|default:'0'}</td>
          <td {if $errors.responsible.$ik} class="attention"{/if}>
            {if $action != 'view'}
              {if $analysis_item.is_leaf}
                {if $analysis_item.status != 'approved'}
                  {include file='input_dropdown.html'
                          name=`$items_type`[responsible][`$ik`]
                          custom_id=`$items_type`_responsible_`$ik`
                          standalone=true
                          required=1
                          really_required=1
                          value=$analysis_item.responsible
                          options=$users_options
                          width='150'
                          label=$smarty.config.finance_budgets_responsible
                          onchange='removeClass(this.parentNode, \'attention\')'}
                {else}
                  {$analysis_item.responsible_name|escape}
                  {include file='input_hidden.html'
                         name=`$items_type`[responsible][`$ik`]
                         custom_id=`$items_type`_responsible_`$ik`
                         value=$analysis_item.responsible
                         standalone=true}
                {/if}
              {else}
                {include file='input_hidden.html'
                         name=`$items_type`[responsible][`$ik`]
                         custom_id=`$items_type`_responsible_`$ik`
                         value=''
                         standalone=true}
              {/if}
            {else}
              {$analysis_item.responsible_name|escape}
            {/if}
          </td>
          <td {if $errors.controlling.$ik} class="attention"{/if}>
            {if $action != 'view'}
              {if $analysis_item.is_leaf}
                {if $analysis_item.status != 'approved'}
                  {include file='input_dropdown.html'
                          name=`$items_type`[controlling][`$ik`]
                          custom_id=`$items_type`_controlling_`$ik`
                          standalone=true
                          required=1
                          really_required=1
                          value=$analysis_item.controlling
                          options=$users_options
                          width='150'
                          label=$smarty.config.finance_budgets_controlling
                          onchange='removeClass(this.parentNode, \'attention\')'}
                {else}
                  {$analysis_item.controlling_name|escape}
                  {include file='input_hidden.html'
                         name=`$items_type`[controlling][`$ik`]
                         custom_id=`$items_type`_controlling_`$ik`
                         value=$analysis_item.controlling
                         standalone=true}
                {/if}
              {else}
                {include file='input_hidden.html'
                         name=`$items_type`[controlling][`$ik`]
                         custom_id=`$items_type`_controlling_`$ik`
                         value=''
                         standalone=true}
              {/if}
            {else}
              {$analysis_item.controlling_name|escape}
            {/if}
          </td>
          <td class="deadline{if $errors.deadline.$ik} attention{/if}" nowrap="nowrap" onclick="removeClass(this, 'attention')">
            {if $action != 'view'}
              {if $analysis_item.is_leaf && $analysis_item.status != 'approved'}
                {include file='input_datetime.html'
                         name=`$items_type`[deadline][`$ik`]
                         custom_id=`$items_type`_deadline_`$ik`
                         standalone=true
                         required=1
                         show_calendar_icon=true
                         width='110'
                         disallow_date_before=now()
                         value=$analysis_item.deadline
                         label=$smarty.config.finance_budgets_deadline}
              {else}
                {if $analysis_item.deadline != '0000-00-00 00:00:00'}{$analysis_item.deadline|date_format:#date_mid#}{/if}
                {include file='input_hidden.html'
                       name=`$items_type`[deadline][`$ik`]
                       custom_id=`$items_type`_deadline_`$ik`
                       value=$analysis_item.deadline
                       standalone=true}
              {/if}
            {else}
              {if $analysis_item.deadline != '0000-00-00 00:00:00'}{$analysis_item.deadline|date_format:#date_mid#}{/if}
            {/if}
          </td>
          <td class="status hright">
            {capture assign='item_status_name'}finance_budgets_status_{$analysis_item.status|default:'unforwarded'}{/capture}
            <img src="{$theme->imagesUrl}budgets_{$analysis_item.status|default:'unforwarded'}.png" width="16" height="16" border="0" alt="{$smarty.config.$item_status_name|escape}" title="" {popup text=$smarty.config.$item_status_name|escape caption=#help_finance_budgets_item_status#|escape width=200} />
            {if $action != 'view'}
              {include file='input_hidden.html'
                       name=`$items_type`[status][`$ik`]
                       custom_id=`$items_type`_status_`$ik`
                       value=$analysis_item.status
                       standalone=true}
            {/if}
          </td>
        </tr>
      {foreachelse}
        <tr>
          <td class="error" colspan="19">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
      </table>
    </td>
  </tr>
</table>
