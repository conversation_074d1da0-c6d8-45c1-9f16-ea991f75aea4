{if $warehouses}
  {assign var='has_total_quantity' value=0}
  {if $table.vars.total_quantity}{assign var='has_total_quantity' value=1}{/if}
  {assign var='warehouse_columns' value=$table.warehouse_columns|default:2}
  {if $warehouses|@count gt 1}
    {math equation='x-(y+1*z)*k+1' x=$cols_count y=$warehouses|@count z=$has_total_quantity k=$warehouse_columns assign='colspan_empty'}
  {else}
    {math equation='x-y*k+1' x=$cols_count y=$warehouses|@count k=$warehouse_columns assign='colspan_empty'}
  {/if}
<tr>
  <td colspan="{$colspan_empty}" style="border:none"></td>
  {foreach name='wh' from=$warehouses item='warehouse'}
    {assign var='wid' value=$warehouse->get('id')}
    <td colspan="{$warehouse_columns}">
      <h2>{$warehouse->get('name')|escape}</h2>
    {foreach from=$layouts_details key='lkey' item='layout'}
      {if $lkey eq 'from'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <a name="error_from_{$wid}"><label for="from_{$wid+1}" class="labelbox{if $messages->getErrors("from_$wid")} error{/if}">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label></a>
        {if !isset($requiredFields) || in_array('from', $requiredFields)}<span class="required">{#required#}</span>{/if}
        <br />
        {if $layout.edit}
          {if $table.handover_direction eq 'incoming'}
            {include file='input_text.html'
                     standalone=true
                     name='from'
                     index=$wid+1
                     value=$table.warehouses.$wid.from
                     hidden=0
                     label=$layout.name}
          {else}
            {assign var='employees_options' value=$warehouse->getEmployees('DROPDOWN')}
            {if $table.warehouses.$wid.from}
              {assign var='from' value=''}
              {foreach from=$employees_options item='eo'}
                {if $eo.option_value eq $table.warehouses.$wid.from}
                  {assign var='from' value=$table.warehouses.$wid.from}
                {/if}
              {/foreach}
              {if !$from}
                {array assign='new_item' label=$table.warehouses.$wid.from option_value=$table.warehouses.$wid.from}
                {array_prepend array='employees_options' key=$table.warehouses.$wid.from new_item=$new_item}
              {/if}
            {/if}
            {include file='input_combobox.html'
                     standalone=true
                     name='from'
                     index=$wid+1
                     options=$employees_options
                     value=$table.warehouses.$wid.from
                     hidden=0
                     label=$layout.name}
          {/if}
        {else}
          {if $table.handover_direction eq 'incoming'}
            {assign var='from' value=$table.warehouses.$wid.from|default:$model->get('customer_name')}
            {$from|escape}
          {else}
            {assign var='from' value=''}
            {foreach from=$warehouse->getEmployees('DROPDOWN') item='eo'}
              {if $eo.option_value eq $table.warehouses.$wid.from}
                {$eo.label|escape}
                {assign var='from' value=$table.warehouses.$wid.from}
              {/if}
            {/foreach}
            {if !$from}
              {assign var='from' value=$table.warehouses.$wid.from|default:$currentUser->get('employee_name')}
              {$from|escape}
            {/if}
          {/if}
          {include file='input_hidden.html'
                   standalone=true
                   name='from'
                   index=$wid+1
                   value=$from}
        {/if}
        <br />
      </span>
      {elseif $lkey eq 'to'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <a name="error_to_{$wid}"><label for="to_{$wid+1}" class="labelbox{if $messages->getErrors("to_$wid")} error{/if}">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label></a>
        {if !isset($requiredFields) || in_array('to', $requiredFields)}<span class="required">{#required#}</span>{/if}
        <br />
        {if $layout.edit}
          {if $table.handover_direction eq 'incoming'}
            {assign var='employees_options' value=$warehouse->getEmployees('DROPDOWN')}
            {if $table.warehouses.$wid.to}
              {assign var='to' value=''}
              {foreach from=$employees_options item='eo'}
                {if $eo.option_value eq $table.warehouses.$wid.to}
                  {assign var='to' value=$table.warehouses.$wid.to}
                {/if}
              {/foreach}
              {if !$to}
                {array assign='new_item' label=$table.warehouses.$wid.to option_value=$table.warehouses.$wid.to}
                {array_prepend array='employees_options' key=$table.warehouses.$wid.to new_item=$new_item}
              {/if}
            {/if}
            {include file='input_combobox.html'
                     standalone=true
                     name='to'
                     index=$wid+1
                     options=$employees_options
                     value=$table.warehouses.$wid.to
                     hidden=0
                     label=$layout.name}
          {else}
            {include file='input_text.html'
                     standalone=true
                     name='to'
                     index=$wid+1
                     value=$table.warehouses.$wid.to
                     hidden=0
                     label=$layout.name}
          {/if}
        {else}
          {if $table.handover_direction eq 'incoming'}
            {assign var='to' value=''}
            {foreach from=$warehouse->getEmployees('DROPDOWN') item='eo'}
              {if $eo.option_value eq $table.warehouses.$wid.to}
                {$eo.label|escape}
                {assign var='to' value=$table.warehouses.$wid.to}
              {/if}
            {/foreach}
            {if !$to}
              {assign var='to' value=$table.warehouses.$wid.to|default:$currentUser->get('employee_name')}
              {$to|escape}
            {/if}
          {else}
            {assign var='to' value=$table.warehouses.$wid.to|default:$model->get('customer_name')}
            {$to|escape}
          {/if}
          {include file='input_hidden.html'
                   standalone=true
                   name='to'
                   index=$wid+1
                   value=$to}
        {/if}
        <br />
      </span>
      {elseif $lkey eq 'location'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <a name="error_location_{$wid}"><label for="location_{$wid+1}" class="labelbox{if $messages->getErrors("location_$wid")} error{/if}">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label></a>
        {if !isset($requiredFields) || in_array('location', $requiredFields)}<span class="required">{#required#}</span>{/if}
        <br />
        {if $layout.edit}
          {include file='input_text.html'
                   standalone=true
                   name='location'
                   index=$wid+1
                   value=$table.warehouses.$wid.location
                   hidden=0
                   label=$layout.name}
        {else}
          {assign var='location' value=$table.warehouses.$wid.location|default:$warehouse->get('name')}
          {$location|escape}
          {include file='input_hidden.html'
                   standalone=true
                   name='location'
                   index=$wid+1
                   value=$location}
        {/if}
        <br />
      </span>
      {elseif $lkey eq 'date'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <a name="error_date_{$wid}"><label for="date_{$wid+1}" class="labelbox{if $messages->getErrors("date_$wid")} error{/if}">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label></a><span class="required">{#required#}</span>
        <br />
        {if $layout.edit}
          {include file='input_date.html'
                   standalone=true
                   name='date'
                   index=$wid+1
                   value=$table.warehouses.$wid.date|default:$smarty.now|date_format:#date_iso_short#
                   hidden=0
                   label=$layout.name}
        {else}
          {$table.warehouses.$wid.date|default:$smarty.now|date_format:#date_short#}
          {include file='input_hidden.html'
                   standalone=true
                   name='date'
                   index=$wid+1
                   value=$table.warehouses.$wid.date|default:$smarty.now|date_format:#date_iso_short#}
        {/if}
        <br />
      </span>
      {elseif $lkey eq 'description'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <label for="description_{$wid+1}" class="labelbox">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label>
        <br />
        {if $layout.edit}
          {include file='input_textarea.html'
                   standalone=true
                   name='description'
                   index=$wid+1
                   value=$table.warehouses.$wid.description
                   hidden=0
                   label=$layout.name}
        {else}
          {$table.warehouses.$wid.description|escape|nl2br|url2href}
          {include file='input_hidden.html'
                   standalone=true
                   name='description'
                   index=$wid+1
                   value=$table.warehouses.$wid.description}
        {/if}
        <br />
      </span>
      {elseif $lkey eq 'notes'}
      <span{if !$layout.view} style="display: none;"{/if}>
        <label for="notes_{$wid+1}" class="labelbox">{help label_content=$layout.name text_content=$layout.description label_sufix=''}</label>
        <br />
        {if $layout.edit}
          {include file='input_textarea.html'
                   standalone=true
                   name='notes'
                   index=$wid+1
                   value=$table.warehouses.$wid.notes
                   hidden=0
                   label=$layout.name}
        {else}
          {$table.warehouses.$wid.notes|escape|nl2br|url2href}
          {include file='input_hidden.html'
                   standalone=true
                   name='notes'
                   index=$wid+1
                   value=$table.warehouses.$wid.notes}
        {/if}
        <br />
      </span>
      {/if}
    {/foreach}
      {include file='input_hidden.html'
               name='status'
               standalone=true
               index=$wid+1
               value='finished'}
    </td>
  {/foreach}
  {if $warehouses|@count gt 1 && $has_total_quantity}
    <td colspan="{$warehouse_columns}">&nbsp;</td>
  {/if}
</tr>
{/if}
