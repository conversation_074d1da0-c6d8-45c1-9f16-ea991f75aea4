<?php

require_once 'finance.repayment_plans.model.php';

/**
 * Finance_Repayment_Plans model class
 */
Class Finance_Repayment_Plans extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Finance_Repayment_Plan';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = 'ORDER BY frp.active DESC, frp.status ASC';
        }
        $sort .= ', frp.id DESC';

        $sql['select'] = 'SELECT DISTINCT(frp.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . ' AS frp ' . "\n";

        if (preg_match('#fci18n\.name#', $sort) || isset($filters['field']) && preg_match('#fci18n\.#', $filters['field'])) {
            //relate to i18n table
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                            '  ON (frp.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#\bci18n\.name#', $sort) || isset($filters['field']) && (preg_match('#\bci18n.name#', $filters['field']) || !$filters['field'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (frp.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to repayment_plan to fetch added_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (frp.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to repayment_plan to fetch modified_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (frp.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        if (!empty($filters['paginate']) && !empty($ids)) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(DISTINCT frp.id) AS total';
                $sql['order'] = '';
                $sql['limit'] = '';
                if (!empty($sql_for_sort)) {
                    $sql['from'] = preg_replace('#' . preg_quote($sql_for_sort) . '#', '', $sql['from']);
                }
                $query = implode("\n", $sql);
                $filters['total'] = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $filters['total'] = count($ids);
            }
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            //so getIds is not needed
            $ids = self::constructWhere($registry, $filters);
        } else {
            $ids = self::getIds($registry, $filters, $sql);
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql['select'] = 'SELECT DISTINCT(frp.id), frp.*, frp.id as order_idx, fci18n.name as company_name, ' . "\n" .
                         '  CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
                         '  frps.name AS substatus_name,' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . ' AS frp' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' AS fci18n' . "\n" .
                       '  ON (frp.company=fci18n.parent_id AND fci18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (frp.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to substatuses
                       'LEFT JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_STATUSES . ' AS frps' . "\n" .
                       '  ON (frp.substatus=frps.id AND frps.lang = "' . $model_lang . '")' . "\n" .
                        //relate to repayment_plan to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (frp.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to repayment_plan to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (frp.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to repayment_plan to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (frp.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE frp.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }
        $sql['group'] = 'GROUP BY frp.id';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';
        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'finance';
        $controller = 'repayment_plans';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {
                //search in all fields
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }

                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    $filter = $filter . ' AND ' . "\n";
                }

                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#frp\.deleted#', $where)) {
            $where .= ' AND frp.deleted = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * gets status of a document
     */
    public static function getModelStatus(&$registry, $id) {
        $db = $registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS .
                 ' WHERE id="' . $id . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");
        $set['modified_by']  = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        $modifiable_ids = self::checkDelete($registry, $ids);
        $count_modified = count($modifiable_ids);

        if ($count_modified) {
            //start transaction
            $db->StartTrans();

            //multiple deletion is part of the transaction
            $deleted = self::deleteMultiple($registry, $modifiable_ids, DB_TABLE_FINANCE_REPAYMENT_PLANS);

            if (!$deleted) {
                $count_modified = false;
                $db->FailTrans();
            }

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $db->HasFailedTrans();

            //complete the transaction (commit/rollback whether SQL failed or not)
            $db->CompleteTrans();

            //the result is true if there is no transaction error
            $result = !$dbTransError;
        }

        return $count_modified;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_FINANCE_REPAYMENT_PLANS);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks if specified records have any releted records.
     * Returns array with records with no related records which can be deleted.
     *
     * @param object $registry - the main registry
     * @param array $ids - array with ids of records to delete
     * @return array - array with ids of records that can be deleted
     */
    public static function checkDelete(&$registry, $ids) {

        $query = 'SELECT id ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . ' AS frp' . "\n" .
                 'WHERE frp.id IN (' . implode(', ', $ids) . ') AND ' . "\n" .
                 '(SELECT SUM(paid_amount) FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                 'WHERE parent_id=frp.id)=0';
        $modifiable_ids = $registry['db']->GetCol($query);

        return $modifiable_ids;
    }

    /**
     * get amount
     *
     * @return float - amount
     */
    public static function getAmount(&$registry, $id) {
        $db = $registry['db'];
        $query = 'SELECT SUM(amount)' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' as frpa ' . "\n" .
                 'WHERE frpa.parent_id=' . $id;
        $amount = $db->GetOne($query);

        return $amount;
    }
}

?>
