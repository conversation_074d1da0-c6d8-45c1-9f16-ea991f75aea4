<?php

class Finance_Warehouses_Documents_History extends History {

    /**
     * prepare history data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, $params = array()) {
        //set action type
        self::$action_type = $params['action_type'];

        if (isset($params['new_model'])) {
            $new_model = $params['new_model'];
        }

        switch (self::$action_type) {
            case 'add':
            case 'edit':
            case 'translate':
            case 'addinvoice':
            case 'addcorrect':
            case 'addcredit':
            case 'adddebit':
            case 'assign':
            case 'email':
            case 'receive_email':
            case 'add_comment':
            case 'edit_comment':
            case 'tag':
            case 'multitag':
            case 'annul':
            case 'finish_reservation':
            case 'release_reservation':
                $data = array (
                    'total' => $new_model->get('total_with_vat'),
                    'currency' => $new_model->get('currency'),
                    'lang' => $new_model->get('model_lang')
                );
                break;
            case 'addhandover':
                $data = array (
                    'total' => $new_model->get('total_with_vat'),
                    'id' => $new_model->get('handover_id'),
                    'num' => $new_model->get('num'),
                    'currency' => $new_model->get('currency'),
                    'lang' => $new_model->get('model_lang')
                );
                break;
            case 'status':
            case 'multistatus':
                $data = array (
                    'total' => $new_model->get('total_with_vat'),
                    'currency' => $new_model->get('currency'),
                    'status' => $new_model->get('status'),
                    'substatus' => $new_model->get('substatus'),
                    'lang' => $new_model->get('model_lang')
                );
                break;
            case 'generate':
            case 'print':
            case 'multiprint':
                $data = array (
                    'pattern' => $params['pattern'],
                    'generated_file' => $params['generated_file']
                );
                break;
            case 'attachments':
            default:
                $data = array ();
                break;
        }

        self::$data = $data;

        return true;
    }

    /**
     * prepare history data for view
     *
     * @return array
     */
    public static function prepareGetData(&$registry, array $records, array $params) {
        $model = $registry->get('finance_warehouses_document');
        if (!$model || $model->get('type') > PH_FINANCE_TYPE_MAX) {
            $model_name = mb_strtolower($registry['translater']->translate('finance_warehouses_document'), 'utf-8');
        } else {
            $model_name = mb_strtolower($model->get('type_name'), 'utf-8');
        }
        if ($model && $model->get('num')) {
            $model_name .= ' ' . $model->get('num');
        }

        foreach ($records as $k => $record) {
            $i18n_param = 'finance_warehouses_documents_log_';
            if ($record['user_id'] == PH_AUTOMATION_USER) {
                $i18n_param .= 'system_';
            }
            $i18n_param .= $record['action_type'];

            $log_text = $registry['translater']->translate($i18n_param);
            if ($record['user_id'] == PH_AUTOMATION_USER && !$log_text) {
                $log_text = $registry['translater']->translate(str_replace('system_', '', $i18n_param));
            }

            $records[$k]['action_type_name'] = $registry['translater']->translate('finance_warehouses_documents_logtype_' . $record['action_type']);

            //switch for additional options
            switch ($record['action_type']) {
                case 'add':
                case 'edit':
                case 'translate':
                case 'status':
                case 'multistatus':
                case 'addinvoice':
                case 'addcorrect':
                case 'addcredit':
                case 'adddebit':
                case 'assign':
                case 'email':
                case 'receive_email':
                case 'add_comment':
                case 'edit_comment':
                case 'tag':
                case 'multitag':
                case 'annul':
                case 'finish_reservation':
                case 'release_reservation':
                    //default options
                    @$records[$k]['data'] = sprintf($log_text,
                                                    $record['user_name'],
                                                    $model_name,
                                                    $record['data']['total'] . ' ' . $record['data']['currency']);
                    break;
                case 'addhandover':
                    $handover_url = sprintf('<a target="_blank" href="%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s">%s</a>',
                                            $_SERVER['PHP_SELF'],
                                            $registry['module_param'], 'finance',
                                            $registry['controller_param'], 'warehouses_documents',
                                            'warehouses_documents', 'view',
                                            'view', @$record['data']['id'],
                                            @$record['data']['num']);
                    $records[$k]['data'] = sprintf($log_text,
                                                   $record['user_name'],
                                                   $handover_url,
                                                   $record['data']['total'] . ' ' . $record['data']['currency']);
                    break;
                case 'generate':
                case 'print':
                case 'multiprint':
                    $pattern_name = '';
                    $file_name = '';
                    if (!empty($record['data']['pattern'])) {
                        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                        $filters = array('where' => array('p.id = ' . $record['data']['pattern'],
                                                          'p.active IS NOT NULL',
                                                          'p.deleted IS NOT NULL'),
                                         'sanitize' => true);
                        $pattern = Patterns::searchOne($registry, $filters);
                        if ($pattern) {
                            $pattern_name = $pattern->get('name');
                        }
                    }
                    if (!empty($record['data']['generated_file'])) {
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                        $filters = array('where' => array('f.id = ' . $record['data']['generated_file'],
                                                          'f.deleted IS NOT NULL'),
                                         'sanitize' => true);
                        $file = Files::searchOne($registry, $filters);
                        if ($file) {
                            $file_name = '. ' . $file->getAsHTML();
                        }
                    }

                    @$records[$k]['data'] = sprintf($log_text,
                                                    $record['user_name'],
                                                    $model_name,
                                                    $pattern_name,
                                                    $file_name);
                    break;
                case 'attachments':
                default:
                    $records[$k]['data'] = sprintf($log_text,
                                                   $record['user_name'],
                                                   $model_name,
                                                   '');
                    break;
            }
        }

        return $records;
    }

    /**
     * save model history
     *
     * @return int
     */
    public static function saveData(&$registry, $params) {
        //prepare the data before saving the history
        self::prepareData($registry, $params);

        //save history record and get the history id
        $history_id = parent::saveData($registry, $params);
        $audit_id = '';
        if (isset($params['old_model']) || isset($params['new_model'])) {

            //audit options
            $audit_options = array('model_name'   => self::$model->modelName,
                                   'action_type'  => self::$action_type,
                                   'parent_id'    => $history_id,
                                   'new_model'    => $params['new_model'],
                                   'old_model'    => $params['old_model']);

            //save audit
            if (in_array(self::$action_type, array('add', 'edit', 'printform', 'annul', 'translate', 'status', 'multistatus', 'addcorrect', 'assign', 'email', 'add_comment', 'edit_comment', 'tag', 'multitag', 'finish_reservation', 'release_reservation'))) {
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.audit.php');
                $audit_id = Finance_Warehouses_Documents_Audit::saveData($registry, $audit_options);
            }
        }

        return $audit_id;
    }

    /**
     * get model history
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        if (!empty($params['paginate'])) {
            $params['sanitize'] = false;
            list($records, $pagination) = Model_Factory::paginatedSearch($registry, $params, 'History', 'getData');
        } else {
            $records = parent::getData($registry, $params);
        }
        $records = self::prepareGetData($registry, $records, $params);
        if (isset($pagination)) {
            $records = array($records, $pagination);
        }

        return $records;
    }
}

?>
