<?php

class Finance_Currencies_History_Viewer extends Viewer {

    public $template = 'history.html';

    public function prepare() {
        $this->data['model'] = $this->getModel();

        // set submit link
        $this->submitLink = sprintf(
            '%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'],
            $this->module,
            $this->registry['controller_param'],
            $this->controller,
            $this->registry['action_param'],
            $this->action,
            $this->action,
            $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $filters = array(
            'model' => $this->model,
            'display' => $this->registry['currentUser']->getPersonalSettings('interface', 'list_history') ?: Finance_Currencies::$itemsPerPage,
            'paginate' => true,
            'page' => $this->registry['request']->get('page') ?: 1
        );
        // prepare model history
        require_once (PH_MODULES_DIR . 'finance/models/finance.currencies.history.php');
        list($history, $pagination) = Finance_Currencies_History::getData($this->registry, $filters);
        $this->data['history'] = $history;
        $this->data['pagination'] = $pagination;
        // prepare audit
        if ($history) {
            require_once (PH_MODULES_DIR . 'finance/models/finance.currencies.audit.php');
            $this->data['audit'] = Finance_Currencies_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $history[0]['h_id'],
                    'model_name' => $this->model->modelName,
                    'action_type' => $history[0]['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->i18n('finance_currency_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $history[0]['user_name'],
                date('d.m.Y, H:i', strtotime($history[0]['h_date']))
            ));
        }
        $this->data['audit_subpanel_template'] = PH_MODULES_DIR . 'finance/templates/_audit.html';

        if ($this->registry['request']->get('source') != 'ajax') {
            $this->prepareTranslations();
            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('finance_currencies_history');
        $this->data['title_legend'] = $this->i18n('finance_currencies_history_legend');
    }
}

?>
