<?php

class Finance_Bank_Accounts_List_Viewer extends Viewer {
    public $template = 'bank_accounts_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'finance.bank_accounts.factory.php';

        $filters = Finance_Bank_Accounts::saveSearchParams($this->registry);

        list($bank_accounts, $pagination) = Finance_Bank_Accounts::pagedSearch($this->registry, $filters);
        $this->data['bank_accounts'] = $bank_accounts;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('finance_bank_accounts');
        $this->data['title'] = $title;
    }
}

?>
