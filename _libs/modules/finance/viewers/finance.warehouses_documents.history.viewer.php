<?php

class Finance_Warehouses_Documents_History_Viewer extends Viewer {

    public $template = 'history.html';

    public function prepare() {
        $this->data['model'] = $this->getModel();

        // set submit link
        $this->submitLink = sprintf(
            '%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
            $_SERVER['PHP_SELF'],
            $this->registry['module_param'],
            $this->module,
            $this->registry['controller_param'],
            $this->controller,
            $this->registry['action_param'],
            $this->action,
            $this->action,
            $this->model->get('id')
        );
        $this->data['submitLink'] = $this->submitLink;

        $filters = array(
            'model' => $this->model,
            'display' => $this->registry['currentUser']->getPersonalSettings('interface', 'list_history') ?: Finance_Warehouses_Documents::$itemsPerPage,
            'paginate' => true,
            'page' => $this->registry['request']->get('page') ?: 1,
            'history_activity' => $this->registry['request']->get('history_activity') ?: 0,
        );
        require_once (PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
        $h_id = $this->registry['request']->get('audit') ?: 0;
        if ($h_id) {
            $filters['page'] = Finance_Warehouses_Documents_History::findPage($this->registry, $h_id, $filters) ?: $filters['page'];
        }

        // prepare model history
        list($history, $pagination) = Finance_Warehouses_Documents_History::getData($this->registry, $filters);
        $this->data['history'] = $history;
        $this->data['pagination'] = $pagination;
        // prepare audit
        if ($history && !$this->registry['request']->get('history_activity')) {
            $audit_history = array();
            if ($h_id) {
                $audit_history = array_filter($history, function($a) use ($h_id) { return $a['h_id'] == $h_id; });
            }
            $audit_history = !empty($audit_history) ? reset($audit_history) : $history[0];

            require_once (PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.audit.php');
            $this->data['audit'] = Finance_Warehouses_Documents_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $audit_history['h_id'],
                    'model_name' => $this->model->modelName,
                    'model_type' => $this->model->get('type'),
                    'action_type' => $audit_history['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->model->get('type_name') ?: $this->i18n('finance_warehouses_document_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $audit_history['user_name'],
                date('d.m.Y, H:i', strtotime($audit_history['h_date']))
            ));
        }
        $this->data['audit_subpanel_template'] = PH_MODULES_DIR . 'finance/templates/_audit.html';

        if ($this->registry['request']->get('source') != 'ajax') {
            $this->prepareTranslations();
            $this->prepareTitleBar();
        }
    }

    public function prepareTitleBar() {
        $this->data['title'] = sprintf($this->i18n('finance_warehouses_documents_history'), $this->model->getModelTypeName());
    }
}

?>
