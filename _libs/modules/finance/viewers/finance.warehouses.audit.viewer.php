<?php

class Finance_Warehouses_Audit_Viewer extends Viewer {

    public $template = '_audit.html';

    public function prepare() {
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.audit.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.history.php';

        // prepare model audit
        $history = Finance_Warehouses_History::getData(
            $this->registry,
            array(
                'h_id' => $this->registry['request']->get('audit'),
                'model' => 'Finance_Warehouse'
            ));
        if ($history) {
            $filters = array(
                'where' => array(
                    'fwh.id = ' . $history[0]['model_id']
                ),
                'sanitize' => true
            );
            $model = Finance_Warehouses::searchOne($this->registry, $filters);

            $this->data['audit'] = Finance_Warehouses_Audit::getData(
                $this->registry,
                array(
                    'parent_id' => $this->registry['request']->get('audit'),
                    'model_name' => 'Finance_Warehouse',
                    'action_type' => $history[0]['action_type']
                ));
            $this->data['audit_title'] = $this->i18n('audit_vars', array(
                $this->i18n('finance_warehouse_audit')
            ));
            $this->data['audit_legend'] = $this->i18n('audit_legend', array(
                $history[0]['user_name'],
                date('d.m.Y, H:i', strtotime($history[0]['h_date']))
            ));
        }

        $this->setFrameset('frameset_blank.html');
    }
}

?>
