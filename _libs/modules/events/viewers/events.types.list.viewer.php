<?php

class Events_Types_List_Viewer extends Viewer {
    public $template = 'types_list.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'events.types.factory.php';
        $filters = Events_Types::saveSearchParams($this->registry);
        $filters['where'][] = 'et.keyword IS NOT NULL';
        list($events_types, $pagination) = Events_Types::pagedSearch($this->registry, $filters);

        $this->data['events_types'] = $events_types;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('events_types');
        $this->data['title'] = $title;
    }
}

?>
