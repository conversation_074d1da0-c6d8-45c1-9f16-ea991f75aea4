{capture assign="date_panel_enabled"}{if !$event->get('recurrence_type') || $new_recurrence_date}1{else}0{/if}{/capture}
<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td>
    <div id="form_container">

      {include file=`$theme->templatesDir`actions_box.html}
      {include file=`$theme->templatesDir`translate_box.html}
      {include file=`$theme->templatesDir`_submenu_actions_box.html}

      <form name="events_remind" action="{$submitLink}" method="post">
      <input type="hidden" name="id" id="id" value="{$event->get('id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$event->get('model_lang')|default:$lang}" />
      <input type="hidden" name="reminder_exists" id="reminder_exists" value="{if $reminder.parent_id}1{else}0{/if}" />
      {if $new_recurrence_date}
        <input type="hidden" name="new_recurrence_date" id="new_recurrence_date" value="{$new_recurrence_date}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table">
        <tr>
          <td>
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              {include file=`$templatesDir`_info_header.html}
              <tr>
                <td class="t_caption3" colspan="3"><div class="t_caption3_title">{$title}</div></td>
              </tr>
              <tr>
                <td nowrap="nowrap" colspan="3" class="m_header_menu t_table nopadding" style="border-bottom: 1px solid #DDDDDD; width: inherit!important;">
                  <a name="reminder"></a>
                  <ul>
                    <li><span{if $reminder.kind ne 'date'} class="selected"{/if}><a onclick="{if $date_panel_enabled}togglePanels(this, 'offset, date'){else}#reminder{/if}" id="offset" class="pointer">{#events_interval_before#|escape}</a></span></li>
                    {if $date_panel_enabled}
                    <li><span{if $reminder.kind eq 'date'} class="selected"{/if}><a onclick="togglePanels(this, 'offset, date')" id="date" class="pointer">{#events_remind_date#|escape}</a></span></li>
                    {/if}
                  </ul>
                  <input type="hidden" id="selected_panel" name="selected_panel" value="{$reminder.kind|default:'offset'}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="reminder_type">{help label='type'}</label></td>
                <td class="required">{#required#}</td>
                <td nowrap="nowrap">
                  <select class="selbox" name="reminder_type" id="reminder_type" title="{#events_type#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                {foreach from=$reminderTypes item='reminderType' key='value'}
                    <option value="{$value}"{if $value eq $reminder.type} selected="selected"{/if}>{$reminderType}</option>
                {/foreach}
                  </select>
                </td>
              </tr>
              <tr class="offset_panel"{if $reminder.kind eq 'date'} style="display: none"{/if}>
                <td class="labelbox"><a name="error_reminder_offset"><label for="reminder_offset"{if $messages->getErrors('reminder_offset')} class="error"{/if}>{help label='reminder_offset'}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <input type="text" class="txtbox" style="width: 30px; text-align: right" name="reminder_offset_days" id="reminder_offset_days" value="{$reminder.offset_days|escape|default:0}" title="{#reminder_offset_days#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />&nbsp;{#days#}&nbsp;
                  <input type="text" class="txtbox" style="width: 30px; text-align: right" name="reminder_offset_hours" id="reminder_offset_hours" value="{$reminder.offset_hours|escape|default:0}" title="{#reminder_offset_hours#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />&nbsp;{#hours#}&nbsp;
                  <input type="text" class="txtbox" style="width: 30px; text-align: right" name="reminder_offset" id="reminder_offset" value="{$reminder.offset|escape|default:0}" title="{#reminder_offset#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onkeypress="return changeKey(this, event, insertOnlyDigits);" />&nbsp;{#minutes#}
                </td>
              </tr>
            {if $date_panel_enabled}
              <tr class="date_panel"{if $reminder.kind ne 'date'} style="display: none"{/if}>
                <td class="labelbox"><label for="reminder_date_formatted">{help label='start_date'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {include file="input_datetime.html"
                    standalone=true
                    required=0
                    name='reminder_date'
                    label=#events_start_date#
                    help=#help_events_start_date#
                    show_calendar_icon=1
                    width=200
                    value=$reminder.date}
                </td>
              </tr>
            {/if}
              <tr>
                <td class="labelbox"><a name="error_custom_message"><label for="custom_message"{if $messages->getErrors('custom_message')} class="error"{/if}>{help label='custom_message'}</label></a></td>
                <td class="unrequired">&nbsp;</td>
                <td nowrap="nowrap">
                  <textarea class="areabox" name="custom_message" id="custom_message" title="{#events_custom_message#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$reminder.custom_message|escape}</textarea>
                </td>
              </tr>
              <tr>
                <td colspan="3">&nbsp;</td>
              </tr>
              <tr>
                <td colspan="3">
                  <button type="submit" name="saveButton1" class="button">{#remind#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      {include file=`$theme->templatesDir`help_box.html}
      {include file=`$theme->templatesDir`after_actions_box.html}
      </form>
      </div>
    </td>
    <td class="side_panel_container">
      <div id="assignments_info" class="info_extra_panel">
        {include file=`$templatesDir`_assignments_info_panel.html}
      </div>
    </td>
  </tr>
</table>
