events = Ereignisse
event = Ereignis
events_name = Betreff
events_location = Ort
events_description = Beschreibung
events_type = Typ
events_type_section = Abschnitt
events_priority = Priorität
events_start_date = Beginn
events_end_date = Ende
events_start_time = Startzeit
events_end_time = Endzeit
events_event_start = Beginn
events_event_end = Ende
events_duration = Dauer
events_allday_event = Ganztäglich
events_not_allday_event = Nicht ganztäglich
events_duration_event = Mit Datum und Dauer
events_customer = Vertragspartner
events_contact_person = Kotaktperson
events_main_contact_person = Hauptkontaktperson
events_customers_info = Daten des Vertragspartners
events_customers_contacts = Kontaktdaten
events_create = Erstellen
events_active = Aktivieren
events_all_events = Alle ereignisse
events_action_email = E-Mails
events_project_name_code = [Kode] Projekt
events_customer_name_code = [Kode] Vertragspartner
events_modified = Geändert am
events_modified_by = Geändert von
events_added_by = Hinzugefügt von

events_project = Projekt
events_search_customer = Kundensuche
events_add_customer = Vertragspartner hinzufügen
events_search_project = Projektsuche
events_assignments = Teilnehmer und Beobachter
events_recurrence_type = Periodizität
events_availability = Verfügbarkeit
events_availability_check = Aufgaben
events_document = Dokument
events_documents = Dokumente
events_project = Projekt
events_projects = Projekte
events_task = Aufgabe
events_tasks = Aufgaben
events_addtimesheet_btn = Bericht hinzufügen
events_status = Status
events_multistatus = Status mehrfach ändern
events_resource = Quelle
events_children = von diesem Ereignis entstandene zusammengehörige Datensätze
events_parents = vor diesem Ereignis entstandene zusammengehörige Datensätze
events_group = Gruppe
events_department = Abteilung
events_added = Datum
events_date = Datum
events_phase = Phase

events_last_customers_records = letzte %s zum Vertragspartner
events_no_customer_events = keine Ereignisse für diesen Vertragspartner

events_view =  %s einsehen
events_edit = %s editieren
events_multiedit = mehfaches Editieren
events_translate = %s übersetzen
events_add = Hinzufügen
events_multiadd = Mehfaches Hinzufügen
events_status_btn = Status bestätigen
events_status_confirm = bestätigen
events_status_not_sure = nicht sicher
events_status_deny = abbrechen
events_assign = %s zuweisen
events_attachments = Dateien zu %s
events_history = Historie der/des %s
events_history_activity = Aktivität
events_remind = Erinnerung an %s
events_relatives = Links zu %s
events_no_relatives = keine zusammengehörigen Datensätze in Bezug auf dieses Ereignis
events_invite_users = weitere Benutzer einlagen
events_invite_customers = andere Kunden einladen
events_assign_users = Andere Benutzer als Beobachter bestellen
events_transformations = Transformationen
events_communications = Kummunikation
events_comments = Kommentare
events_emails = E-Mails
events_documents_referer = Zusammengehörige Dokumente
events_link_type = Linktyp
events_document_referer = Zusammengehöriger Dokument
events_project_referer = Zusammengehöriges Projekt

events_check_availability = Aufgaben überprüfen
events_selected = das gewählte Ereignis
events_conflict = Ereignisse im selben Intervall!
events_participancy_not_sure = Ereignis mit nicht sicherer Teilnahme
events_other = Weitere Ereignisse

events_add_legend = Neues Ereignis hinzufügen
events_invitations = Einladungen zur Teilnahme an Ereignissen
events_last_invitation_date = Einladung gesendet
events_invitation_not_sent = Einladung nicht gesendet
events_operations = Aktion
events_history_legend2 = Falls Sie eine ausführliche Beschreibung der für das Ereignis vorgenommenen Bearbeitung einsehen möchten, einfach die von ihnen gewünschte Zeile auf der unteren Tabelle anklicken. Die Daten für die gewählte Zeile erscheinen in der zweiten Tabelle. Erscheinen keine Daten in der zweiten Tabelle, so heisst das, dass diese Tätigkeit nicht geprüft wurde.
events_custom_message = Text
events_interval_before = nach Zeit vor dem Beginn
events_remind_date = nach Datum
events_file_not_exist = Diese Datei existiert nicht!
events_added_attachments = Hinzugefügte Dateien
events_deleted_attachments = gelöschte Dateien

events_observer = Beobachter
events_observers = Beobachter
events_participant = Teilnehmer
events_participants = Teilnehmer
events_reminder_participants = Informierte Personen
events_participant_type = Art
events_participant_type_user = Benutzer
events_participant_type_customer = Kunde
events_participant_status = Status
events_participant_status_pending = wartet auf Antwort
events_participant_status_confirmed = bestätigt
events_participant_status_not_sure = nicht sicher
events_participant_status_denied = abgebrochen
events_no_participants = keine Teilnehmer gefunden
events_no_observers = keine Beobachter gefunden
events_user_participant_title = Teilnehmer - Benutzer
events_customer_participant_title = Teilnehmer - Vertragspartner
events_user_observer_title = Beobachter

events_remaining_participant = noch 1 Teilnehmer
events_remaining_participants = noch %s Teilnehmer
events_remaining_observer = Noch  1 Beobachter
events_remaining_observers = Noch %s Beobachter

events_visibility = Sichtbarkeit
events_public = öffentlich
events_private = persönlich
events_private_event = persönliches Ereignis
events_private_event2 = Das ist ein persönliches Ereignis, und die Info ist beschränkt

events_priority_verylow = sehr niedrig
events_priority_low = niedrig
events_priority_medium = normal
events_priority_high = hoch
events_priority_veryhigh = sehr hoch

events_availability_available = frei
events_availability_busy = besetzt
events_availability_no_data = keine Daten über andere Aufgaben oder es handelt sich um ein wiederkehrendes Ereignis

events_recurrence_types_none = wird nicht wiederholt
events_recurrence_types_daily = jeden Tag
events_recurrence_types_monthlyByDate = jeden Monat an diesem Datum
events_recurrence_types_monthlyByDay = jeden Monat an diesem Wochentag
events_recurrence_types_weekly = jede Woche an diesem Tag
events_recurrence_types_yearly = jedes Jahr an diesem Tag

events_reminder_offset_from = zuvor
events_reminder_offset_days = Tage vor dem Beginn
events_reminder_offset_hours = Stunden vor dem Beginn
events_reminder_offset = Minuten vor dem Beginn
events_reminder_repeats = Wiederholungen
events_reminder_repeat_interval = Intervall der Wiederholungen in Minuten

events_documents_internal = intern
events_documents_outgoing = ausgehend
events_documents_incoming = eingehend
events_document_type = Typ des Dokuments

events_reminder_email = mit e-mail
events_reminder_toaster = online
events_reminder_both = beides
events_reminder_start = Beginn
events_reminder_end = Ende
events_stop_reminder = Erinnerung an Ereignis eingestellt

events_reminder_nomenclature_name = Erinnerung an Nomenklatur %s

events_continues_from_prev_day = dauert vom vorigen Tag

events_status_planning = Planung
events_status_progress = Durcführung
events_status_finished = Beendet
events_status_unstarted = Nicht durchgeführt
events_status_moved = verschoben

event_access = Rechte
event_access_edit = volle Rechte
event_access_view = beschränkte Rechte

events_replace_col = Spalte ersetzen

events_event_assign_participant_notify = für Teilnahmeeinladungen
events_event_assign_customer_notify = für Teilnahmeeinladungen
events_event_assign_observer_notify = für Zuweisung eines Beobachters
events_event_changed_participant_user_notify = für notwendige Teilnahmebestätigung
events_event_changed_customer_notify = für notwendige Teilnahmebestätigung
events_event_changed_observer_user_notify = für Änderung eines Ereignisses
events_event_delete_user_notify = für Einstellung der Teilnahme
events_event_delete_customer_notify = für Einstellung der Teilnahme

events_event_participant_confirmed_notify = hat seine Anwesenheit bestätigt
events_event_participant_denied_notify = wird nicht anwesend sein
events_event_participant_not_sure_notify = seine Anwesenheit ist noch nicht sicher

events_event_participant_became_observer_notify = Änderung der Zuordnung
events_event_delete_observer_notify = für еnding der Zuweisung für Beobachter

events_log_add = %s fügt ein Ereignis hinzu (Status: %s, %s)
events_log_multiadd = %s fügt ein Ereignis hinzu (Status: %s, %s)
events_log_edit = %s editiert ein Ereignis (Status: %s, %s)
events_log_multiedit = %s editiert Aufgaben (Status: %s, %s)
events_log_translate = %s übersetzt ein Ereignis (Status: %s, %s)
events_log_activate = %s aktiviert ein Ereignis (Status: %s, %s)
events_log_deactivate = %s deaktiviert ein Ereignis (Status: %s, %s)
events_log_delete = %s löscht ein Ereignis (Status: %s, %s)
events_log_restore = %s wiederherstellt ein Ereignis (Status: %s, %s)
events_log_assign = %s ordnet ein Ereignis zu (Status: %s, %s)
events_log_status = %s ändert Status des Ereignisses (Status: %s, %s)
events_log_multistatus = %s ändert den Status des Ereignisses (Status: %s, %s)
events_log_relatives = %s ändert Link des Ereignisses (Status: %s, %s)
events_log_add_attachments = %s fügt eine angehängte Datei für ein Ereignis hinzu (Status: %s, %s)
events_log_modified_attachments = %s ändert eine angehängte Datei für ein Ereignis (Status: %s, %s)
events_log_del_attachments = %s löscht eine angehängte Datei für ein Ereignis (Status: %s, %s)
events_log_modified_gen = %s ändert die erstellte Datei des Ereignisses (Status: %s, %s)
events_log_generate_delete = %s löscht eine erstellte Datei für ein Ereignis (Status: %s, %s)
events_log_print = %s druckt ein Ereignis durch Verwendung einer Vorlage "%s"%s
events_log_multiprint = %s druckt ein Ereignis durch Verwendung einer Vorlage "%s"%s
events_log_email = %s sendet eine E-Mail für ein Ereignis
events_log_receive_email = %s receives event e-mail
events_log_receive_email_detailed = There is an email from %s sent
events_log_add_comment = %s fügt einen Kommentar für ein Ereignis hinzu
events_log_edit_comment = %s editiert einen Kommentar für ein Ereignis
events_log_create = %s erstellt %s durch %s

events_logtype_translate = Übersetzung
events_logtype_edit = Editieren
events_logtype_activate = Aktivieren
events_logtype_deactivate = Deaktivieren
events_logtype_delete = Löschen
events_logtype_restore = Wiederherstellen
events_logtype_assign = Zuweisung
events_logtype_status = Status ändern
events_logtype_multistatus = Status mehrfach ändern
events_logtype_relatives = Beziehungen
events_logtype_add_attachments = Datei hinzufügen
events_logtype_modified_attachments = Datei ändern
events_logtype_del_attachments = Datei löschen
events_logtype_modified_gen = Datei ändern
events_logtype_generate_delete = Datei löschen
events_logtype_print = Druck
events_logtype_multiprint = Mehrfacher Druck
events_logtype_email = E-Mail senden
events_logtype_receive_email = Received e-mail
events_logtype_add_comment = Kommentar hinzufügen
events_logtype_edit_comment = Kommentar editieren
events_logtype_create = Erstellen

error_events_add_failed = %s nicht erfolgreich hinzugefügt
error_events_edit_failed = %s nicht erfolgreich bearbeitet
error_events_translate_failed = %s nicht erfolgreich übersetzt
error_events_status_failed = Statuswechsel von %s nicht erfolgreich
error_events_multiadd_failed = %s nicht erfolgreich hinzugefügt
error_events_multiedit_failed = %s nicht erfolgreich editiert
error_events_multistatus_failed = Statusänderung von %s nicht erfolgreich
error_events_multiprint_failed = Fehler beim mehrfachen Druck von %s
error_no_name = Bitte %s eingeben
error_no_type_specified = Bitte %s wählen
error_event_start = Bitte gültiges Datum für %s wählen
error_event_end = Bitte gültiges Enddatum wählen
error_event_start_end = Bitte gültiges Enddatum nach %s wählen
error_duration = Bitte geben Sie gültige Dauer ein!
error_events_planned_time_duration = Insgesamt zugeordneten Zeit für die Aufgabe übersteigt geplanten Zeit! Maximale posssible Dauer %d Minuten.
error_events_planned_time_overlap = %s hat bereits geplante Zeit auf Aufgabe in der gleichen Intervall
error_events_cannot_allocate_in_past = Geplante Zeit kann nicht in der Vergangenheit an!
error_events_expired_task = Um geplante Zeit zu bearbeiten, sollten Task-Frist bis heute oder später erweitert werden.
error_assignments = Bitte Zuweisungen wählen
error_no_such_event = Dieser Datensatz ist nicht verfügbar für Sie!
error_no_such_event_type = Dieser Datensatz ist nicht verfügbar für Sie!
error_no_user_edit_event = Bitte Benutzer mit Recht auf Bearbeitung wählen
error_invalid_type = Ein ungültiger oder nicht aktiver Typ wurde gewählt!
error_events_no_multi_operation_variables = Keine Variablen für diese mehrfache Aktion
error_no_events_or_deleted = Keine Ereignisse wurden gewählt oder ein der Ereignisse ist gelöscht
error_different_types = Die gewählten Ereignisse sind aus unterschiedlichen Typen
error_set_user_status = Fehler beim Speichern der Antwort auf eine Einladung
error_no_such_task = Keine Aufgabe zum gewählten Datensatz
error_set_event_status_finished = Fehler beim Beenden von Ereignissen
error_events_print_no_default_pattern = Druckvorlage nicht gewählt
error_events_print_document = Die Datei kann infolge eines Fehlers beim Erstellen der Datei nicht gedruckt werden
error_events_file_deleted_failed = Fehler beim Löschen der gewählten Datei
error_invalid_trademark = Bitte einen gültigen Wert für %s wählen oder eingeben!
error_invalid_recurrence_type = Ungültigen Wert für %s ausgewählt!
error_events_transform = %s nicht erfolgreich erstellt!
error_no_such_transformation = Eine nicht aktive oder nicht existierende Transformation wurde gewählt!
error_events_reminder_edit_failed = Die Erinnerung wurde nicht bearbeitet
error_events_print_invalid_pattern = Keine Vorlage oder eine nicht existierene Vorlage wurde gewählt

help_events_check_all_caption = Markieren/ Markierung entfernen
help_events_check_all_text = Markieren/ Markierung entfernen für alle Felder zum Ersetzen von Spalten

message_events_add_success = %s erfolgreich hinzugefügt
message_events_edit_success = %s erfolgreich bearbeitet
message_events_translate_success = %s erfolgreich übersetzt
message_events_status_success = Status von %s erfolgreich gewechselt
message_events_assign_success = Erfolgreiche Zuweisungen!
message_events_multiadd_success = %s erfolgreich hinzugefügt
message_events_multiedit_success = %s erfolgreich bearbeitet
message_events_multistatus_success = Status von %s erfolgreich gewechselt
message_event_user_status_isset = Diese Einladung haben Sie schon beantwortet.
message_events_status_finished_isset = Die Ereignisse sind schon beendet.
message_events_status_finished_success = %d Ereignisse wurden beendet.
message_events_status_finished_already = %d Ereignisse wurden schon beendet.
message_events_file_deleted_success = Die markierte datei wurde erfolgreich gelöscht.
message_events_transform_success = %s erfolgreich erstellt!
message_events_reminder_add_success = Die Erinnerung wurde hinzugefügt
message_events_reminder_edit_success = Die Erinnerung wurde editiert

warning_events_change_status_not_all = Der Status mancher %s wurde nicht gewechselt, weil der Statuswechsel ungültig ist oder Sie keine Rechte zum Statuswechsel besitzen.

help_events_phase = Phase zum gewählten Objekt
help_events_status = Status des Ereignisses

var_type = Typ
var_name = Feld
var_value = Neuer Text
old_value = voriger Text
no_old_value = kein voriger Text

basic_vars = Hauptvariablen
additional_vars = zusätzliche Variablen

audit_vars = Historie der am Ereignis vorgenommenen Bearbeitungen
audit_legend = Nähere Angaben zu der von %s am %s vorgenommenen Änderung

events_assignments_configurator_load_save = Schablonen für Zuweisungen laden/speichern
events_assignments_configurator_name = Name des Schablonen
error_events_assignments_configurator_save = Fehler beim Speichern den Schablonen für Zuweisungen!
error_events_assignments_configurator_delete = Fehler beim Löschen den Schablonen für Zuweisungen!
