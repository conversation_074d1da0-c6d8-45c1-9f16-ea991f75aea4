<form name="events_subpanel" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=events" method="post" enctype="multipart/form-data">
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="form_container">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list" width="100%">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#events_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#events_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.event_start.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.event_start.link}">{#events_start_date#|escape}</div></td>
          <td class="t_caption t_border {$sort.event_end.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.event_end.link}">{#events_end_date#|escape}</div></td>
          <td class="t_caption t_border {$sort.priority.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.priority.link}">{#events_priority#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#events_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{#events_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.project.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.project.link}">{#events_project#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#date#|escape}</div></td>
          <td class="t_caption t_border {$sort.participants.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.participants.link}">{#events_participants#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$events item='event'}
      {strip}
      {capture assign='info'}
        <strong>{#events_name#|escape}:</strong> {$event->get('name')|escape}<br />
        <strong>{#events_description#|escape}:</strong> {$event->get('description')|mb_truncate|escape}<br />
        <strong>{#events_customer#|escape}:</strong> {$event->get('customer_name')|escape}<br />
        <strong>{#events_location#|escape}:</strong> {$event->get('location')|escape}<br />
        <strong>{#added#|escape}:</strong> {$event->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$event->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$event->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$event->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$event->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$event->get('status_modified_by_name')|escape}<br />
        {if $event->isDeleted()}<strong>{#deleted#|escape}:</strong> {$event->get('deleted')|date_format:#date_mid#|escape}{if $event->get('deleted_by_name')} {#by#|escape} {$event->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$event->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $event->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$event->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="10" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$event disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        {strip}
          {if $event->get('ownership') eq 'other'}
            {capture assign='event_color'}{$calendar_settings.color_other}{/capture}
            {capture assign='event_background_color'}{$calendar_settings.background_color_other}{/capture}
          {elseif $event->get('ownership') eq 'mine'}
            {capture assign='mine_color'}color_{$event->get('type')}{/capture}
            {capture assign='mine_background_color'}background_color_{$event->get('type')}{/capture}
            {capture assign='event_color'}{$calendar_settings.$mine_color}{/capture}
            {capture assign='event_background_color'}{$calendar_settings.$mine_background_color}{/capture}
          {else}
            {capture assign='event_color'}{$calendar_settings.color_none}{/capture}
            {capture assign='event_background_color'}{$calendar_settings.background_color_none}{/capture}
          {/if}
        {/strip}
        <tr class="t_row{if !$event->get('active')} t_inactive{/if}{if $event->get('deleted_by')} t_deleted{/if}"{if $event->get('active')} style="background-color:{$event_background_color};"{/if}>
          <td class="t_border hright" nowrap="nowrap"{if $event->get('active')} style="color:{$event_color};"{/if}>
          {if $event->get('files_count')}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=attachments&amp;attachments={$event->get('id')}">
              <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, 'events', 'events', {$event->get('id')})"
                     onmouseout="mclosetime()" />
            </a>
          {/if}
          {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event->get('id')}">{$event->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.type.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if}>{$event->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.event_start.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if} nowrap="nowrap">{if $event->get('allday_event')}{$event->get('event_start')|date_format:#date_short#|escape}<br />({if $event->get('allday_event') == -1}{$event->get('duration')} {if abs($event->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){else}{$event->get('event_start')|date_format:#date_mid#|escape}{/if}</td>
          <td class="t_border {$sort.event_end.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if} nowrap="nowrap">{if $event->get('allday_event')}{$event->get('event_end')|date_format:#date_short#|escape}<br />({if $event->get('allday_event') == -1}{$event->get('duration')} {if abs($event->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){else}{$event->get('event_end')|date_format:#date_mid#|escape}{/if}</td>
          {capture assign='priority_name'}events_priority_{$event->get('priority')}{/capture}
          <td class="t_border {$sort.priority.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if}>{$smarty.config.$priority_name}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap"{if $event->get('active')} style="color:{$event_color};"{/if}>
            {capture assign='status_name'}events_status_{$event->get('status')}{/capture}
            {capture assign='popup_and_onclick'}
              {popup text=$smarty.config.$status_name|escape caption=#help_events_status#|escape width=250}{if $event->checkPermissions('setstatus')} onclick="changeStatus({$event->get('id')}, 'events')" style="cursor: pointer;"{/if}
            {/capture}
            <img src="{$theme->imagesUrl}events_{$event->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} /> <span {$popup_and_onclick}>{$smarty.config.$status_name|escape}</span>
          </td>
          <td class="t_border {$sort.customer.isSorted}">{if $event->get('customer')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$event->get('customer')}" title="{#view#}: {$event->get('customer_name')|escape}">{$event->get('customer_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          <td class="t_border {$sort.project.isSorted}">{if $event->get('project')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$event->get('project')}" title="{#view#}: {$event->get('project_name')|escape}">{$event->get('project_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          <td class="t_border {$sort.added.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if}>{$event->get('added')|date_format:#date_short#|escape}</td>
          <td class="t_border {$sort.participants.isSorted}"{if $event->get('active')} style="color:{$event_color};"{/if}>
            {foreach name='cp' from=$event->get('customers_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.cp.last},{/if}
            {/foreach}
            {foreach name='up' from=$event->get('users_participants') item='participant'}
              {capture assign="status_name"}events_participant_status_{$participant.user_status}{/capture}
              {if $participant.user_status eq 'pending'}<img src="{$theme->imagesUrl}pending.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'confirmed'}<img src="{$theme->imagesUrl}message.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'not_sure'}<img src="{$theme->imagesUrl}not_sure.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{elseif $participant.user_status eq 'denied'}<img src="{$theme->imagesUrl}error.png" width="10" height="10" alt="" title="{$smarty.config.$status_name}" border="0" />{/if}{$participant.assigned_to_name|escape}{if !$smarty.foreach.up.last},{/if}
            {/foreach}
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$event module='events' action_param='events' controller='events'}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="12">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{capture assign='search_link'}{$smarty.server.PHP_SELF}?{$module_param}=events&amp;events=subpanel&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$search_link
  use_ajax=$use_ajax
  hide_selection_stats=true
  session_param=$session_param
}
    </td>
  </tr>
</table>
</form>
