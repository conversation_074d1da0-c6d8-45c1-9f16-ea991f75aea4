<?php

require_once 'turnovers.model.php';

/**
 * Turnovers factory class
 */
Class Turnovers extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Turnover';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches rows with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all rows found
     *                  2. paged search  - array of array of all rows found and their count
     */
    public static function search(&$registry, $filters = array()) {

        //set interface lang filter
        $lang = $registry['lang'];

        //if vat is 20%
        $vat_val = 1.2;
        $period = 'day';

        if (isset($filters['where']) && isset($filters['where']['period'])) {
            preg_match('/\'(.*)\'/', $filters['where']['period'], $matches);
            $period = $matches[1];
        }

        if ($period == 'day') {
            $total_with_vat = 'ROUND(' . $vat_val . '*total, 2)';
            //day search
            $sql['select'] = 'SELECT day as date, customer, trademark, ROUND(total, 2) as total, confirmed,' . "\n" .
                             'confirmed_by, added, added_by, modified, modified_by,' . "\n" .
                             'ROUND(' . $vat_val . '*total, 2) as total_with_vat,' . "\n" .
                             'CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
                             'ni18n.name AS trademark_name' . "\n";
        } elseif ($period == 'month') {
            $total_with_vat = 'SUM(' . $vat_val . '*total)';
            //month search
            $sql['select'] = 'SELECT CONCAT(SUBSTRING(day, 1, 8), "01") as date, customer, trademark, SUM(total) as total,' . "\n" .
                             ' COUNT(confirmed) as confirmed_days, SUM(' . $vat_val . '*total) as total_with_vat,' . "\n" .
                             ' COUNT(*) as saved_days, DATE_FORMAT(LAST_DAY(day), "%e") as month_days,' . "\n" .
                             'CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
                             'ni18n.name AS trademark_name' . "\n";
        } elseif ($period == 'year') {
            $total_with_vat = 'SUM(' . $vat_val . '*total';
            //year search
            $sql['select'] = 'SELECT SUBSTRING(day, 1, 4) as date, customer, trademark, SUM(total) as total,' . "\n" .
                             ' COUNT(confirmed) as confirmed_days, SUM(' . $vat_val . '*total) as total_with_vat,' . "\n" .
                             ' COUNT(*) as saved_days, DATE_FORMAT(CONCAT(SUBSTRING(day, 1, 4), "-12-31"), "%j") as year_days,' . "\n" .
                             'CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
                             'ni18n.name AS trademark_name' . "\n";
        }

        $sql['from'] = 'FROM ' . DB_TABLE_TURNOVERS . ' as t ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       ' ON (t.customer=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       ' ON (t.trademark=ni18n.parent_id AND ni18n.lang="' . $lang . '")' . "\n";

        $tmp_filters = $filters;
        if (isset($tmp_filters['where']['period'])) {
            //unset period filter
            unset($tmp_filters['where']['period']);
        }
        $sql['where'] = self::constructWhere($registry, $tmp_filters);
        if (strpos($sql['where'], 'total_with_vat')) {
            $sql['where'] = str_replace('total_with_vat', $total_with_vat, $sql['where']);
        }

        if ($period != 'day') {
            $sql['group'] = 'GROUP BY date';
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = ' ORDER BY t.day DESC';
        }
        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        if (!empty($sql['select'])) {
            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);
        } else {
            $records = array();
        }

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit'] && !empty($sql['select'])) {
                //get the total number of records for this search
                if ($period == 'day') {
                    $sql['select'] = 'SELECT COUNT(*) AS total';
                } elseif ($period == 'month') {
                    $sql['select'] = 'SELECT COUNT(DISTINCT SUBSTRING(day, 1, 8)) AS total';
                } elseif ($period == 'year') {
                    $sql['select'] = 'SELECT COUNT(DISTINCT SUBSTRING(day, 1, 4)) AS total';
                }
                $sql['limit'] = '';
                $sql['group'] = '';
                $sql['order'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($records);
            }

            $results = array($records, $total);
        } else {
            //no pagination required return only the records
            $results = $records;
        }

        return $results;
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            if ($registry['currentUser']) {
                $current_user_id = $registry['currentUser']->get('id');
            }
            foreach ($filters['where'] as $filter) {
                if(preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                if (isset($current_user_id)) {
                    $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                }
                if(!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        return $where;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    /* public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    } */

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        if ($registry['request']->isRequested('customer')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 't.customer=\''. $registry['request']->get('customer') . '\'';
        }

        if ($registry['request']->isRequested('trademark')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 't.trademark=\''. $registry['request']->get('trademark') . '\'';
        }
        if ($registry['request']->isRequested('period')) {
            if(!$registry['request']->isRequested('customer')) {
                $tmp_array = $registry['session']->get($sessionParam);
                //get filter values from session
                if (is_array($tmp_array) && isset($tmp_array['search_fields'])) {
                    foreach ($tmp_array['search_fields'] as $key => $val) {
                        $filters['where'][$key] = $val . ' = \'' . $tmp_array['values'][$key] . '\'';
                    }
                }
            }
            //set requested period filter
            $filters['where']['period'] = 't.period = \'' . $registry['request']->get('period') . '\'';
        }
        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }
}

?>
