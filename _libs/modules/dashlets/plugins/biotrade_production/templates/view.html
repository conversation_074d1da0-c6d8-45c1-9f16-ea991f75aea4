{if $dashlet}
  <h1>{$title}</h1>

  <div class="form_container">
    {include file=`$theme->templatesDir`actions_box.html}
    <table border="0" cellpadding="0" cellspacing="0" class="t_table">
      <tr>
        <td class="t_footer"></td>
      </tr>
      <tr>
        <td>
          <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            <!-- FIELDS FOR ALL PLUGINS -->
            <tr>
              <td class="labelbox">{help label='dashlet_for'}</td>
              <td class="required">{#required#}</td>
              <td>{$module_name_i18n|escape}</td>
            </tr>
            <tr>
              <td class="labelbox">{help label_content=#plugin_name# text_content=#help_plugin_name#}</td>
              <td class="required">{#required#}</td>
              <td>{$dashlet->get('name')|escape}</td>
            </tr>
            <tr>
              <td class="labelbox">{help label_content=#plugin_description# text_content=#help_plugin_description#}</td>
              <td class="unrequired">&nbsp;</td>
              <td>{$dashlet->get('description')|mb_wordwrap|url2href}</td>
            </tr>
            {if $dashlet->get('default') eq '1'}
            <tr>
              <td class="labelbox">{help label='default'}</td>
              <td class="unrequired">&nbsp;</td>
              <td colspan="2"><img src="{$theme->imagesUrl}small/check_yes.png" alt="{#yes#}" title="{#yes#}" /></td>
            </tr>
            {/if}
            <tr>
              <td class="labelbox">{help label_content=#plugin_full_width# text_content=#help_plugin_full_width#}</td>
              <td class="unrequired">&nbsp;</td>
              <td>{if $dashlet->get('full_width')}<img src="{$theme->imagesUrl}small/check_yes.png" alt="{#yes#}" title="{#yes#}" />{else}&nbsp;{/if}</td>
            </tr>
            <tr>
              <td colspan="3">&nbsp;</td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
    {include file=`$theme->templatesDir`help_box.html}
    {include file=`$theme->templatesDir`system_settings_box.html object=$dashlet}
  </div>
{/if}