##########################################################
# Add all plugin records necessary for production module #
##########################################################

SET NAMES utf8;

START TRANSACTION;

-- Dashlet plugin

SELECT @dpid := `id` FROM `dashlets_plugins` WHERE `type` = 'production' ORDER BY `id` DESC LIMIT 0, 1;

INSERT IGNORE INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(@dpid, 'production', NULL, 0, 1);

SELECT @dpid := `id` FROM `dashlets_plugins` WHERE `type` = 'production' ORDER BY `id` DESC LIMIT 0, 1;

INSERT IGNORE INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `lang`) VALUES
(@dpid, 'Производство', 'bg'),
(@dpid, 'Production', 'en');


-- Automations

DELETE FROM `automations` WHERE `method` LIKE 'plugin := production%';

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създава заявки за производство рекурсивно', 0, NULL, 1, 'documents', '', 'before_action', '0', '', 'condition := 1', 'plugin := production\r\nmethod := createProductionRequests\r\n', 'cancel_action_on_fail := 1', 1, 0, 1),
(NULL, 'Не позволява редакция на ТК/продукти/полу-продукти, за които тече производство', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '0', '', 'condition := \'[action]\' == \'edit\'\r\ncondition := count(Documents::search($this->registry, array(\'where\' => array("d.type = \'{$this->registry[\'config\']->getParam(\'production\', \'type_route_card\')}\' AND", "d.status = \'locked\' AND", "a____card_tech_id = \'[b_id]\' OR", "a____product_id = \'[b_id]\'")))) > 0', 'plugin := production\r\nmethod := redirectFromEdit', '', 1, 0, 1),
(NULL, 'Валидация на ТК', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '0', '', 'condition := \'[request_is_post]\'\r\ncondition := preg_match(\'#^(add(quick)?|edit)$#\', \'[action]\')\r\ncondition := \'[b_type]\' == $this->registry[\'config\']->getParam(\'production\', \'type_technological_card\')', 'plugin := production\r\nmethod := validateTechnologicalCard', 'cancel_action_on_fail := 1', 2, 0, 1),
(NULL, 'Обновяване (на заден фон) на планирана себестойност на продукт и всички свързани продукти при редакция', 0, NULL, 0, 'nomenclatures', NULL, 'action', '0', '', 'condition := \'[action]\' == \'edit\' && \'[request_is_post]\' && in_array(\'[b_type]\', array((int)$this->registry[\'config\']->getParam(\'production\', \'type_product\'), (int)$this->registry[\'config\']->getParam(\'production\', \'type_semiproduct\')))', 'plugin := production\r\nmethod := updateProductCost', NULL, 1, 0, 1),
(NULL, 'Периодично обновяване на планирана себестойност на продукти', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', '', '# condition will cause automation to be performed for models one by one\r\nwhere := n.type IN (SELECT value FROM settings WHERE section = \'production\' AND name IN (\'type_product\', \'type_semiproduct\'))', 'plugin := production\r\nmethod := updateProductCost', NULL, 1, 0, 1);


-- Reports

SET @rid := 356;

INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@rid, 'production_stock_planning', '# optional filter for products - specify categories\r\nnomenclature_categories := \r\n# optional filter for available quantity of materials - specify warehouses\r\nwarehouses := \r\n# id of Num measure\r\nmeasure_num := 1\r\n\r\n# variables of materials\r\nmeasure_var := measure_name\r\nmin_stock_var := minimal_quantity\r\ndelivery_term_var := delivery_term\r\n\r\n# variables of products\r\nproduct_article_id_var := article_id\r\nproduct_article_quantity_var := article_quantity\r\nproduct_recipe_quantity_var := recipe_quantity\r\n\r\n# variables of production requests\r\nrequest_status_planning := opened\r\nrequest_substatus_planning := \r\nrequest_product_id_var := product_id\r\nrequest_quantity_var := product_quantity\r\nrequest_customer_types := \r\n', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@rid, 'Планиране на наличности', '03. Производство', NULL, 'bg'),
(@rid, 'Stock planning', '03. Production', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @rid, '0', '1'),
(NULL, 'reports', '', 'export', @rid, '0', '2');

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @rid;

SET @rid := 391;

INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@rid, 'production_capacity', '', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@rid, 'Производствени капацитети', '03. Производство', NULL, 'bg'),
(@rid, 'Production capacity', '03. Production', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @rid, '0', '1'),
(NULL, 'reports', '', 'export', @rid, '0', '2');

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @rid;

COMMIT;
