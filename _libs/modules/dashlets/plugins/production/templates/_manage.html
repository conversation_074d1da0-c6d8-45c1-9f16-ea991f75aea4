<div class="t_section_title">
  <h1 class="hcenter">{#plugin_production#|escape} <span class="green">{$document->get('date')|date_format:#date_short#}, {$document->getPlainVarValue('workplace_name')|escape}, {$document->getPlainVarValue('workshift_name')|escape} ({#plugin_production_schedule_short#|escape} {$document->getPlainVarValue('production_schedule_name')|escape} {$document->getPlainVarValue('product_name')|escape})</span>{if $document->get('status') eq 'closed'} <span class="red">({#plugin_timesheet_closed#})</span>{/if}</h1>
</div>
<div class="section_container nopadding">
  <form action="">
    <input type="hidden" name="id" id="id" value="{$document->get('id')}" />
    <input type="hidden" name="group" id="group" value="{$document->get('group')|default:$smarty.const.PH_ROOT_GROUP}" />
    <input type="hidden" name="active" id="active" value="{$document->get('active')|default:1}" />
    {if $document->get('vars')}
      <table class="t_layout_table" cellspacing="0" cellpadding="0" border="0">
      {assign var='layouts_vars' value=$document->get('vars')}
      {assign var='all_vars' value=$document->getAll()}
      {foreach from=$document->get('layouts_details') key='lkey' item='layout'}
        {if $lkey eq 'notes'}
          <tr>
            <td class="nopadding" colspan="3">
              <div class="labelbox" style="padding: 15px 0 5px;">{help label_content=$layout.name text_content=$layout.description}</div>
              {include file="input_textarea.html"
                       standalone=true
                       name='notes'
                       value=$document->get('notes')
                       width='100%'
                       height='60'
                       label=$layout.name
              }
            </td>
          </tr>
        {elseif $layout.system}
          <tr class="hidden">
            <td colspan="3">
              <input type="hidden" name="{$lkey}" id="{$lkey}" value="{$all_vars.$lkey|default:''|escape}" />
              {if $lkey eq 'customer'}
              <input type="hidden" name="branch" id="branch" value="{$document->get('branch')|default:0}" />
              <input type="hidden" name="contact_person" id="contact_person" value="{$document->get('contact_person')|default:0}" />
              {elseif $lkey eq 'full_num'}
              <input type="hidden" name="num" id="num" value="{$document->get('num')}" />
              {/if}
            </td>
          </tr>
        {elseif $layout.view && array_key_exists($layout.id, $layouts_vars)}
          {assign var='layout_id' value=$layout.id}
          {assign var='vars' value=$layouts_vars.$layout_id}
          {foreach name='j' from=$vars item='var'}
            {if $var.type}
              {if $var.type eq 'grouping'}
                <tr>
                  <td colspan="3" class="nopadding">
                    {include file="`$smarty.const.PH_MODULES_DIR`documents/templates/input_transform_grouping.html"
                             module='documents'
                             action_param='documents'}
                    <script type="text/javascript" defer="defer">production.processDiaryTable();</script>
                  </td>
                </tr>
              {elseif $var.type eq 'gt2'}
              {else}
                {include file="input_`$var.type`.html"
                          var=$var
                          standalone=false
                          var_id=$var.id
                          name=$var.name
                          custom_id=$var.custom_id
                          label=$var.label
                          help=$var.help
                          back_label=$var.back_label
                          back_label_style=$var.back_label_style
                          value=$var.value
                          value_id=$var.value_id
                          options=$var.options
                          optgroups=$var.optgroups
                          option_value=$var.option_value
                          first_option_label=$var.first_option_label
                          onclick=$var.onclick
                          on_change=$var.on_change
                          sequences=$var.sequences
                          check=$var.check
                          scrollable=$var.scrollable
                          calculate=$var.calculate
                          readonly=$var.readonly
                          source=$var.source
                          onchange=$var.onchange
                          map_params=$var.map_params
                          width=$var.width
                          hidden=$var.hidden
                          really_required=$var.required
                          required=$var.required
                          disabled=$var.disabled
                          options_align=$var.options_align
                          autocomplete=$var.autocomplete
                          js_methods=$var.js_methods
                          restrict=$var.js_filter
                          deleteid=$var.deleteid
                          show_placeholder=$var.show_placeholder
                          text_align=$var.text_align
                          custom_class=$var.custom_class
                }
              {/if}
            {/if}
          {/foreach}
        {/if}
      {/foreach}
      </table>
    {/if}
    <br class="clear" />
    <div class="hright section_container full_width">
      {if $document->get('load_store')}
      <a href="javascript: void(0);" class="floatl" onclick="production.load({ldelim}action: 'loadStore', complex: 1, data: {ldelim}id: '{$document->get('id')}'{rdelim}{rdelim});">{#plugin_store_load#|escape}</a>
      {/if}
      <button class="button" type="button" onclick="production.load({ldelim}action: 'home'{rdelim});">{#back#|mb_upper|escape}</button>
      {if $document->get('status') ne 'closed'}
      <button class="button green" type="button" onclick="production.load({ldelim}form: this.form, action: 'manage', complex: 1{rdelim});">{#save#|mb_upper|escape}</button>
      {/if}
      <button class="button green" type="button" onclick="production.load({ldelim}form: this.form, action: 'manage', complex: 1, data: {ldelim}after_action: 'finish'{rdelim}{rdelim});">{#save#|mb_upper|escape} {#and#|mb_upper|escape} {#finish#|mb_upper|escape}</button>
    </div>
  </form>
</div>