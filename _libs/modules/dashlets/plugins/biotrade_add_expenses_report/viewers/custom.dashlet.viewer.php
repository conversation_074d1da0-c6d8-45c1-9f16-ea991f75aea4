<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template = 'dashlet.html';
        $this->pluginName = 'biotrade_add_expenses_report';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');

    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $registry = &$this->registry;

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');
        $dashlet_settings = '';

        // get the current dashlet properties to get settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings = $dashlet_plugin[$this->pluginName]['settings'];

        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);

//         // get the employee's default department id and name
//         $department_info = $custom_dashlet_model->getEmployeeDepartment();

//         // prepare all needed options for the dashlet
//         $custom_dashlet_options = $custom_dashlet_model->prepareCustomOptions();

//         // assign options to smarty
//         foreach ($custom_dashlet_options as $var_name => $var_options) {
//             $this->data[$var_name . '_options'] = $var_options;
//         }

//         $this->data['employee_department'] = (!empty($department_info['id']) ? $department_info['id'] : '');
//         $this->data['employee_department_name'] = (!empty($department_info['name']) ? $department_info['name'] : '');
        $this->data['dashlet'] = $dashlet;
        $this->data['scripts_url'] = $this->scriptsUrl . 'custom.js';

    }

}

?>
