{assign var='today' value=$smarty.now|date_format:"%Y-%m-%d"}
<form id="add_new_visit" method="post" action="">
  <input type="hidden" name="mode" id="mode" value="{$mode}" />
  <input type="hidden" name="num" id="num" value="{$data.num}" />
  <input type="hidden" name="schedule_id" id="schedule_id" value="{$data.schedule_id}" />
  <table border="0" cellpadding="5" cellspacing="0" class="t_list" width="100%" border="0">
    <!-- date -->
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_date#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
        {if $mode eq 'add'}
          {include file="input_date.html"
            standalone=true
            name='date'
            label=#plugin_vistaawt_date#
            value=$data.date
            width=200
            disallow_date_before=$today
            show_calendar_icon=true}
        {else}
          {$data.date|date_format:#date_short#}
        {/if}
      </td>
    </tr>

    <!-- reschedule date (only in reschedule mode) -->
    {if $mode eq 'reschedule'}
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_date_reschedule#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
          {include file="input_date.html"
            standalone=true
            name='date'
            label=#plugin_vistaawt_date_reschedule#
            value=$data.date
            width=200
            disallow_date_before=$today
            show_calendar_icon=true}
          <input type="hidden" name="previous_date" id="previous_date" value="{$data.date}" />
      </td>
    </tr>
    {/if}

    {if $protocol_customer_name}
    <!-- protocol customer name (ASKMERCHANDISER specific) -->
    <tr>
      <td class="labelbox"><label for="date">{$protocol_customer_name.label}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
        {if $mode eq 'add'}
          {include file="input_autocompleter.html"
            standalone=true
            name=$protocol_customer_name.name
            label=$protocol_customer_name.label
            value=$data.protocol_customer_name
            autocomplete=$protocol_customer_name.autocomplete
            width=200}
        {else}
          {$data.protocol_customer_name}
        {/if}
        <input type="hidden" name="{$protocol_customer_id.name}" id="{$protocol_customer_id.name}" value="{$data.protocol_customer_id}" />
      </td>
    </tr>
    {/if}

    <!-- city -->
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_city#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
      {if $mode eq 'add'}
          {include file="input_dropdown.html"
            standalone=true
            name='city_id'
            label=#plugin_vistaawt_city#
            value=$data.city_id
            required=true
            really_required=true
            onchange=cityChanged(this)
            options=$cities
            width=200}
      {else}
          {$data.city_name}
          <input type="hidden" name="city_id" id="city_id" value="{$data.city_id}" />
      {/if}
      </td>
    </tr>

    <!-- object_category -->
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_object_category#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
      {if $mode eq 'add'}
          {include file="input_dropdown.html"
            standalone=true
            name='object_category_id'
            label=#plugin_vistaawt_object_category#
            value=$data.object_category_id
            disabled=true
            required=true
            really_required=true
            onchange=objectCategoryChanged(this)
            no_select_records_label=#plugin_vistaawt_object_category_initial_option#
            width=200}
      {else}
          {$data.object_category}
          <input type="hidden" name="object_category_id" id="object_category_id" value="{$data.object_category_id}" />
      {/if}
      </td>
    </tr>

    <!-- object -->
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_object#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
      {if $mode eq 'add'}
          {include file="input_dropdown.html"
            standalone=true
            name='object_id'
            label=#plugin_vistaawt_object#
            value=$data.object_id
            disabled=true
            required=true
            really_required=true
            onchange=objectChanged(this)
            no_select_records_label=#plugin_vistaawt_object_initial_option#
            width=200}
      {else}
          {$data.object_name}
          <input type="hidden" name="object_id" id="object_id" value="{$data.object_id}" />
      {/if}
      </td>
    </tr>

    <!-- object -->
    <tr>
      <td class="labelbox"><label for="date">{#plugin_vistaawt_object_address#}</label></td>
      <td class="required">{#required#}</td>
      <td nowrap="nowrap">
      {if $mode eq 'add'}
          {include file="input_textarea.html"
            standalone=true
            name='object_address'
            label=#plugin_vistaawt_object_address#
            value=$data.object_address
            required=true
            width=200}
      {else}
          {$data.object_address}
      {/if}
      </td>
    </tr>

    <!-- customer (only in view mode) -->
    <tr>
      <td class="labelbox"><label for="customer">{#plugin_vistaawt_customer#}</label></a></td>
      <td class="required">{#required#}</td>
      <td>
        {$data.customer_name}
      </td>
    </tr>

    <tr>
      <td colspan="3">
        {strip}
        <input type="button" class="button" onclick="scheduleVisit(this);" value="{if $mode eq 'add'}{#plugin_vistaawt_add#|escape}{else}{#plugin_vistaawt_reschedule#|escape}{/if}" />
        <input type="button" class="button" onclick="closeScheduleLightbox();" value="{#plugin_vistaawt_cancel#|escape}" />
        {/strip}
      </td>
    </tr>
  </table>
</form>