<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template = 'dashlet.html';
        $this->pluginName = 'aon_correct_policy';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');

    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $registry = &$this->registry;

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');
        $settings = $dashlet->get('filters');

        // get the current dashlet properties to get settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings = $dashlet_plugin[$this->pluginName]['settings'];

        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);

        if (isset($settings['employees'])) {
            $dashlet_employees = $settings['employees'];
        } else {
            $dashlet_employees = array();
        }

        $this->data['contract_ac'] = array(
            'type'         => 'contracts',
            'url'          => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'contracts', 'contracts'),
            'suggestions'  => '<custom_num>',
            'search'       => array('<custom_num>'),
            'fill_options' => array(
                '$contract_ac => <id>',
                '$contract_ac_autocomplete => <custom_num>',
                '$contract_ac_oldvalue => <custom_num>'),
            'filters'      => array(
                '<type>'    => $custom_dashlet_model->available_contract_types,
                '<subtype>' => 'contract'
            ),
            'execute_after'=> 'checkContractDataPaymentTypes',
            'buttons'      => 'clear',
            'buttons_hide' => 'search');

        $this->data['dashlet'] = $dashlet;
        $this->data['dashlet_plugin'] = $this->pluginName;
        $this->data['dashlet_id'] = $dashlet->get('id');
        $this->data['scripts_url'] = $this->scriptsUrl . 'custom.js';
        $this->data['scripts_gt2_url'] = PH_JAVASCRIPT_URL . '_gt2.js';
    }

}

?>
