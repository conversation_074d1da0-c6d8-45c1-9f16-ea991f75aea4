/**
 * Object for the current dashlet
 */
var dashletLazyReporting = {
    /**
     * Set some defaults for the dashlet
     *
     * @param dashlet_id             - the ID of the current dashlet
     * @param current_active_task_id - the ID of the currently active task
     * @return boolean true|false
     */
    setDefaults: function (dashlet_id) {
        // Set the dashlet ID
        this.id = dashlet_id;
        return true;
    },

    reloadContent: function(user, date) {
        var container = $('content_dashlet_' + this.id);
        addClass(container, 'dashlet_loader');
        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        var opt = {
            // asynchronous:false,
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }

                removeClass(container, 'dashlet_loader');
                container.innerHTML = t.responseText;
                var scripts = container.getElementsByTagName('script');
                for (var s in scripts) {
                    ajaxLoadJS(scripts[s]);
                };
                // Fade the loading effect
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=';
        url += 'dashlets&dashlets=dashlet&dashlet=' + this.id + '&plugin=chronika_lazy_reporting';
        url += '&user=' + user + '&date=' + date;
        new Ajax.Request(url, opt);
    },

    /**
     * show the lightbox)
     *
     * @param current_button           - the DOM object of the current button
     * @param current_selected_task_id - the ID of the currently selected task
     * @return boolean true
     */
    showForm: function (currentUser, date, time) {
        if (this.started) {
            return true;
        }
        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        // Prepare the AJAX URL
        var url = env.base_url + '?' + env.module_param + '=dashlets' +
            '&dashlets=custom_action' +
            '&plugin=chronika_lazy_reporting' +
            '&dashlet=' + this.id +
            '&custom_plugin_action=prepareLightbox' +
            '&user=' + currentUser +
            '&date=' + date;
        if (time) {
            url += '&time=' + time;
        }
        // Prepare the AJAX options
        var opt = {
            method:    'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                // Get the AJAX result
                eval('var ajax_result = ' + t.responseText);

                // Set default lightbox width
                if (!ajax_result.width) {
                    ajax_result.width = '450px';
                }

                // Set default lightbox height
                if (!ajax_result.height) {
                    ajax_result.height = '';
                }

                // Build the lightbox
                lb = new lightbox(ajax_result);

                // Activate the lightbox
                lb.activate();

                // Fade the loading effect
                Effect.Fade('loading');

                // Enable the current button
                current_button.disabled = false;
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Execute the AJAX
        new Ajax.Request(url, opt);

        return true;
    },

    /**
     * show the lightbox for edit
     *
     * @param current_button           - the DOM object of the current button
     * @param current_selected_task_id - the ID of the currently selected task
     * @return boolean true
     */
    editForm: function (doc, row, editable) {
        // Show loading effect
        Effect.Center('loading');
        Effect.Appear('loading');

        this.started = true;

        // Prepare the AJAX URL
        var url = env.base_url + '?' + env.module_param + '=dashlets' +
            '&dashlets=custom_action' +
            '&plugin=chronika_lazy_reporting' +
            '&dashlet=' + this.id +
            '&custom_plugin_action=prepareLightbox' +
            '&doc=' + doc +
            '&row=' + row + '&editable=' + editable;
        // Prepare the AJAX options
        var opt = {
            method:    'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                // Get the AJAX result
                eval('var ajax_result = ' + t.responseText);

                // Set default lightbox width
                if (!ajax_result.width) {
                    ajax_result.width = '450px';
                }

                // Set default lightbox height
                if (!ajax_result.height) {
                    ajax_result.height = '';
                }

                // Build the lightbox
                lb = new lightbox(ajax_result);

                // Activate the lightbox
                lb.activate();

                dashletLazyReporting.started = false;
                // Fade the loading effect
                Effect.Fade('loading');

                // Enable the current button
                current_button.disabled = false;
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Execute the AJAX
        new Ajax.Request(url, opt);

        return true;
    },

    updateDocument: function(form) {
        // Prepare the AJAX options
        // Show loading effect
        Effect.Appear('loading');
        Effect.Center('loading');
        var opt = {
            method:       'post',
            asynchronous: false,
            parameters: Form.serialize(form),
            onSuccess:    function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                // Eval the response
                eval(t.responseText);
                if (result == 1) {
                    removeClass(form, 'errors');
                    dashletLazyReporting.reloadContent(form.user.value, form.date.value);
                    lb.deactivate();
                }
                if (result.errors) {
                    addClass(form, 'errors');
                    alert(result.errors);
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // Prepare the AJAX URL
        var url = env.base_url + '?' + env.module_param + '=dashlets' +
            '&dashlets=custom_action' +
            '&plugin=chronika_lazy_reporting' +
            '&dashlet=' + this.id +
            '&custom_plugin_action=updateDocument';

        // Execute the AJAX
        new Ajax.Request(url, opt);

        return true;
    },

    setLines: function (el) {
       eval(' var data = ' + el.form.companies.value + ';');
       var options = new Object(), params;
       var first_empty = true;
       if (el.id == "company") {
           if (data && data[el.value] && data[el.value].tasks) {
               options = data[el.value].tasks;
           }
           params = {
               type: 'dropdown',
               name: 'task',
               options: options,
               first_empty: true,
               sequences: 'dashletLazyReporting.setLines(this)',
               width: '200px'
           };
           var cnt = el.form.task.parentNode;
           cnt.innerHTML = '';
           cnt = createField(params, cnt);
           options = new Object();
           if (data && data[el.value] && data[el.value].lines) {
               options = data[el.value].lines;
           }
           dashletLazyReporting.taskMode = false;
       } else if (el.id == "task") {
           if (el.value) {
               if (data && data[$('company').value] && data[$('company').value].tasks && data[$('company').value].tasks[el.value] && data[$('company').value].tasks[el.value]['line']) {
                   options = [{option_value: data[$('company').value].tasks[el.value]['line'], label: data[$('company').value].tasks[el.value]['line_name']}];
                   first_empty = false;
                   dashletLazyReporting.taskMode = true;
               }
           } else {
               dashletLazyReporting.setLines($('company'));
           }
       }

       params = {
           type: 'dropdown',
           name: 'business_line',
           options: options,
           first_empty: first_empty,
           sequences: 'dashletLazyReporting.setActivities(this)',
           width: '200px'
       };
       var cnt = el.form.business_line.parentNode;
       cnt.innerHTML = '';
       cnt = createField(params, cnt);
       dashletLazyReporting.setActivities(cnt);
    },

    setActivities: function (el) {
        if (dashletLazyReporting.taskMode) {
            eval(' var act = ' + el.form.tActMinus.value + ';');
        } else {
            eval(' var act = ' + el.form.actMinus.value + ';');
        }
        var options = new Object(), params;
        if (act && act[el.value]) {
            options = act[el.value];
        }
        params = {
            type: 'dropdown',
            name: 'activity_minus',
            options: options,
            first_empty: true,
            sequences: 'dashletLazyReporting.setActivitiesPlus(this)',
            width: '200px'
        };
        var cnt = el.form.activity_minus.parentNode;
        cnt.innerHTML = '';
        cnt = createField(params, cnt);
        dashletLazyReporting.setActivitiesPlus(cnt);
    },

    setActivitiesPlus: function (el) {
        if (dashletLazyReporting.taskMode) {
            eval(' var act = ' + el.form.tActPlus.value + ';');
        } else {
            eval(' var act = ' + el.form.actPlus.value + ';');
        }
        var options = new Object(), params;
        if (act && act[el.value]) {
            options = act[el.value];
        }

        params = {
            type: 'dropdown',
            name: 'activity_plus',
            options: options,
            first_empty: true,
            width: '200px'
        };
        var cnt = el.form.activity_plus.parentNode;
        cnt.innerHTML = '';
        createField(params, cnt);
    }
};
