@media all {
    .dashlet_wienerberger_meeting_protocol {
        width: 620px;
        min-height: 252px;
    /*     height: 240px; */
    }
    .dashlet_wienerberger_meeting_protocol .tabs_container {
        height: 19px;
        margin: 5px 0px 0px 0px;
    }
    .dashlet_wienerberger_meeting_protocol .tabs_container .tab {
        width: 120px;
        height: 12px;
        float: left;
        border: 1px solid #CCC;
        margin: 0px 0px 0px 0px;
        padding: 3px;
        border-radius: 5px 5px 0px 0px;
        background-color: #F5F5F5;
        cursor: pointer;
        text-align: center;
    }
    .dashlet_wienerberger_meeting_protocol .tabs_container .selected{
        background-color: #FFF !important;
    }
    
    .dashlet_wienerberger_meeting_protocol .content {
        border: 1px solid #CCC;
        min-height: 230px;
    /*     height: 218px; */
    }
    .dashlet_wienerberger_meeting_protocol .content .buttons_container {
        align: right;
        text-align: right;
        padding: 0px 8px 4px 0px;
    }
    .dashlet_wienerberger_meeting_protocol .content > table {
        width: 100%;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table {
        width: 500px;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table tr,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table td,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table th {
        border: 1px solid #CCC;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table td,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table th {
        padding: 3px;
        height: 20px;
    }
    .dashlet_wienerberger_meeting_protocol .button.add_meeting_protocol {
        margin: 3px 0px;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_container {
        height: 215px;
        overflow-y: scroll;
    /*     overflow-x: visible; */
    /*     overflow: auto; */
        border: 1px solid #CCC;
        display: inline-block;
        margin: 3px 0px 0px 0px;
    }
    .dashlet_wienerberger_meeting_protocol .next_steps_list_new,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_existing {
        list-style-type: none;
        margin: 5px;
        padding: 0px;
        display: inline-block;
        height: 215px;
        width: 180px;
        overflow: auto;
    }
    .dashlet_wienerberger_meeting_protocol .next_steps_list_new li,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_existing li {
        line-height: 24px;
        cursor: pointer;
        font-height: 14px;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table tr:not(:first-child):active,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_new li:active,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_existing li:active,
    .dashlet_wienerberger_meeting_protocol .tabs_container .tab:active {
        background-color: lightblue!important;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table tr:not(:first-child):hover,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_new li:hover,
    .dashlet_wienerberger_meeting_protocol .next_steps_list_existing li:hover {
        background-color: #EAF4FC;
    }
    .dashlet_wienerberger_meeting_protocol .tabs_container .tab:hover {
        background-color: #FFF;
    }
    .dashlet_wienerberger_meeting_protocol .next_step_container_table {
        width: 450px;
    }
    .dashlet_wienerberger_meeting_protocol .next_step_title {
        font-weight: bold;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_tab_list_container {
        width: 520px;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_tab_add_new_container {
        vertical-align: top;
    }
    .dashlet_wienerberger_meeting_protocol .button {
        white-space: nowrap;
    }
    .dashlet_wienerberger_meeting_protocol .nextstep_event_employees {
        height: 70px;
        overflow-y: scroll;
        width: 270px;
    }
    .dashlet_wienerberger_meeting_protocol .nextstep_event_date {
        vertical-align: top;
    }
}
/* .dashlet_wienerberger_meeting_protocol:-webkit-full-screen { */
.dashlet_wienerberger_meeting_protocol {
    background-color: white;
}

/* @media only screen and (max-width: 1024px) { */
@media
only screen and (max-width: 1024px),
only screen and (max-width: 1024px) and (min-resolution: 100dpi) and (min--moz-device-pixel-ratio: 1.3),
only screen and (max-width: 1024px) and (min-resolution: 100dpi) and (-o-min-device-pixel-ratio: 13/10),
only screen and (max-width: 1024px) and (min-resolution: 100dpi) {
/*
    .dashlet_wienerberger_meeting_protocol {
        width: auto!important;
    }
*/
/*     .dashlet_wienerberger_meeting_protocol { */
/*         background-color: red!important; */
/*     } */
/*
    .dashlet_wienerberger_meeting_protocol .button {
        height: 150px!important;
    }
    .dashlet_wienerberger_meeting_protocol .button,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table td,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table th {
        font-size: 25px!important;
    }
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_list_table {
        width: 1050px;
    }
    .dashlet_wienerberger_meeting_protocol,
    .dashlet_wienerberger_meeting_protocol .meeting_protocols_tab_list_container {
        width: auto!important;
    }
*/
}



/* Fix: show scrollbar in mobile browsers */
/*
::-scrollbar,
::-o-scrollbar,
::-moz-scrollbar,
::-ms-scrollbar,
::-webkit-scrollbar {
    -webkit-appearance: none;
}
::-scrollbar:vertical,
::-o-scrollbar:vertical,
::-moz-scrollbar:vertical,
::-ms-scrollbar:vertical,
::-webkit-scrollbar:vertical {
    width: 12px;
}
::-scrollbar:horizontal,
::-o-scrollbar:horizontal,
::-moz-scrollbar:horizontal,
::-ms-scrollbar:horizontal,
::-webkit-scrollbar:horizontal {
    height: 12px;
}
::-scrollbar-thumb,
::-o-scrollbar-thumb,
::-moz-scrollbar-thumb,
::-ms-scrollbar-thumb,
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, .5);
    border-radius: 10px;
    border: 2px solid #ffffff;
}
::-scrollbar-track,
::-o-scrollbar-track,
::-moz-scrollbar-track,
::-ms-scrollbar-track,
::-webkit-scrollbar-track {
    border-radius: 10px;  
    background-color: #ffffff; 
}
*/