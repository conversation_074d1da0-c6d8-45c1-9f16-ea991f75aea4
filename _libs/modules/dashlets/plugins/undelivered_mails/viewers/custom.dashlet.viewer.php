<?php

class Custom_Dashlet_Viewer extends Viewer {

    public $template = 'dashlet.html';
    public $pluginName = 'undelivered_mails';
    public $sortables = array();

    public function __construct(&$registry, $is_main = false) {
        parent::__construct($registry, $is_main);

        $this->sortables = array(
            'added'   => 'added',
            'from'    => 'added_by_name',
            'content' => 'subject'
        );

        $this->setFrameset('frameset_blank.html');

    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir = $this->pluginDir . 'templates/';

        $this->modelsDir      = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir     = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir      = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir = $this->pluginDir . 'javascript/';
        $this->scriptsUrl = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {
        $registry = &$this->registry;

        $plugin_model_class = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/models/custom.model.php';

        //prepare the data for the template
        $dashlet = $registry->get('dashlet');

        $session_param = 'dashlets_' . $dashlet->get('id') . '_plugin';

        require_once($plugin_model_class);
        $custom_model = new Custom_Model;
        $filters_search = $dashlet->get('filters');
        $filters_search['dashlet_id'] = $dashlet->get('id');

        if ($registry->get('take_from_request')) {
            if ($registry->get('page_list')) {
                $filters_search['page'] = $registry->get('page_list');
            }
            if ($registry->get('sort') && $registry->get('order')) {
                $filters_search['sort'] = array(
                    $registry->get('sort') . ' ' . $registry->get('order')
                );
            }
        } else {
            $session_filters = $registry['session']->get($session_param);
            if ($session_filters) {
                foreach ($session_filters as $key => $sf) {
                    if (!isset($filters_search[$key]) && $key!='page') {
                        $filters_search[$key] = $sf;
                    }
                }
            }
        }
        $registry['session']->set($session_param, $filters_search, '', true);

        list($undelivered_mails, $pagination) = $custom_model->searchUndeliveredMails($registry, $filters_search);

        $base_link = sprintf('%s?%s=dashlets&amp;dashlets=custom_action&amp;plugin=%s&amp;dashlet=%d&amp;custom_plugin_action=', $_SERVER['PHP_SELF'], $this->registry['module_param'], $dashlet->get('controller'), $dashlet->get('id'));

        $this->data['communications_sort'] = $this->prepareAjaxSort($filters_search, $pagination['session_param'], $pagination['session_param'], $base_link . 'listUndeliveredMails', true);

        $pagination['link'] = $base_link . 'listUndeliveredMails' . ($registry->get('sort') ? ('&amp;sort=' . $registry->get('sort')) : '') . ($registry->get('order') ? ('&amp;order=' . $registry->get('order')) : '') . '&amp;page=';

        $this->data['dashlet'] = $dashlet;
        $this->data['communications'] = $undelivered_mails;
        $this->data['base_link'] = $base_link;
        $this->data['pagination'] = $pagination;
        $this->data['scripts_url1'] = PH_MODULES_URL . 'communications/javascript/communications.js';

    }

}

?>
