<?php

class Custom_Dashlet_Viewer extends Viewer {

    public function __construct(&$registry, $is_main = false) {
        $this->template   = 'dashlet.html';
        $this->pluginName = 'lazy_reporting';

        parent::__construct($registry, $is_main);
        $this->setFrameset('frameset_blank.html');
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {
        // Prepare the registry
        $registry = &$this->registry;

        // Set the dashlet into the viewer
        $this->data['dashlet']     = $registry->get('dashlet');
        // Set the scripts URL into the viewer
        $this->data['scripts_url'] = $this->scriptsUrl . 'custom.js';

        // Get the dashlet settings
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $filter_dashlet_plugin = array('get_one' => $this->pluginName);
        $dashlet_plugin        = Dashlets::getPlugins($registry, $filter_dashlet_plugin);
        $dashlet_settings      = $dashlet_plugin[$this->pluginName]['settings'];
        require_once $this->pluginDir . 'models/custom.model.php';
        $custom_dashlet_model = new Custom_Model($dashlet_settings);

        $tasks_types = array_filter(is_array($custom_dashlet_model->tasks_type) ? $custom_dashlet_model->tasks_type : preg_split('/\s*\,\s*/', $custom_dashlet_model->tasks_type));
        $tasks_statuses = array_filter(is_array($custom_dashlet_model->tasks_status) ? $custom_dashlet_model->tasks_status : preg_split('/\s*\,\s*/', $custom_dashlet_model->tasks_status));

        // Get all tasks in progress for the current user
        $query = 'SELECT `t`.`id` AS `id`,' . "\n" .
                 '    TRIM(CONCAT(`ci18n`.`name`, \' \', `ci18n`.`lastname`)) AS `customer_name`,' . "\n" .
                 '    IF(`sw`.`date` IS NULL OR `sw`.`date` = \'0000-00-00 00:00:00\', \'0\', \'1\') AS `watch_is_active`,' . "\n" .
                 '    `ti18n`.`name` AS `task_name`' . "\n" .
                 '  FROM `' . DB_TABLE_TASKS . '` AS `t`' . "\n" .
                 '  JOIN `' . DB_TABLE_TASKS_I18N . '` AS `ti18n`' . "\n" .
                 '    ON (`t`.`id` = `ti18n`.`parent_id`' . "\n" .
                 '      AND `ti18n`.`lang` = \'' . $registry['lang'] . '\')' . "\n" .
                 '  JOIN `' . DB_TABLE_TASKS_ASSIGNMENTS . '` AS `ta`' . "\n" .
                 '    ON `ta`.`parent_id` = `t`.`id`' . "\n" .
                 '  JOIN `' . DB_TABLE_CUSTOMERS_I18N . '` AS `ci18n`' . "\n" .
                 '    ON (`ci18n`.`parent_id` = `t`.`customer`' . "\n" .
                 '      AND `ci18n`.`lang`    = \'' . $registry['lang'] . '\')' . "\n" .
                 '  LEFT JOIN `' . DB_TABLE_STOPWATCHES . '` AS `sw`' . "\n" .
                 '    ON (`sw`.`user_id`    = `ta`.`assigned_to`' . "\n" .
                 '      AND `sw`.`model`    = \'task\'' . "\n" .
                 '      AND `sw`.`model_id` = `t`.`id`)' . "\n" .
                 '  WHERE `t`.`type`              IN (\'' . implode('\',\'', $tasks_types) . '\')' . "\n" .
                 '    AND `t`.`active`            = \'1\'' . "\n" .
                 '    AND `t`.`deleted_by`        = \'0\'' . "\n" .
                 '    AND `t`.`status`            IN (\'' . implode('\',\'', $tasks_statuses) . '\')' . "\n" .
                 '    AND `ta`.`assignments_type` = \'0\'' . "\n" .
                 '    AND `ta`.`assigned_to`      = \'' . $registry['currentUser']->get('id') . '\'' . "\n" .
                 '  GROUP BY `t`.`id`';
        $this->data['tasks'] = $registry['db']->GetAssoc($query);
    }
}

?>
