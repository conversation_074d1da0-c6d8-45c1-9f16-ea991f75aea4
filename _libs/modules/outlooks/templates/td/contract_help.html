      {strip}
      {capture assign='info'}
        <strong><u>{$basic_vars_labels.num|default:#contracts_num#|escape}:</u></strong> {if $single->get('num')}{if $single->get('num') eq 'system'}{#contracts_system_num#}{else}{$single->get('num')}{/if}{else}<i>{#contracts_unfinished_contract#}</i>{/if}<br />
        <strong>{$basic_vars_labels.name|default:#contracts_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{$basic_vars_labels.type|default:#contracts_type#|escape}:</strong> {$single->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$single->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('status_modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='contract_status'}
        {if $single->get('status') eq 'opened'}
          {#help_contracts_status_opened#}
        {elseif $single->get('status') eq 'locked'}
          {#help_contracts_status_locked#}
        {elseif $single->get('status') eq 'closed'}
          {#help_contracts_status_closed#}
        {/if}
        {if $single->get('substatus_name')}
          <br />
          {#help_contracts_substatus#}{$single->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      <div id="rf{$single->get('id')}" style="display: none">{if $single->get('num')}{if $single->get('num') eq 'system'}{#contracts_system_num#}{else}[{$single->get('num')}]{/if}{else}<i>{#contracts_unfinished_contract#}</i>{/if} {$single->get('name')|escape|default:"&nbsp;"}</div>