<?php

class Customers_Translate_Viewer extends Viewer {
    public $template = 'translate.html';

    public function prepare() {
        $this->model = $this->registry['customer'];

        // prepare layout index
        $this->prepareLayoutIndex();

        // clear all but super-layouts from layout index
        if (!empty($this->data['vars_index'])) {
            $vars_index_options = $this->data['vars_index']['options'];
            foreach ($vars_index_options as $idx => $option) {
                if (!preg_match('#^customer_(main|contact|company|personal)_data_index$#', $option['name'])) {
                    unset($vars_index_options[$idx]);
                }
            }
            $this->data['vars_index']['options'] = array_values($vars_index_options);
        }

        // count layouts with view+edit permissions in each super-layout
        $layouts_details = $this->model->get('layouts_details');
        $reg_data = $this->model->get('is_company') ? 'company_data' : 'personal_data';
        // add 2 rows for the i18n info
        $num_view_layouts = array('main_data' => 2, 'contact_data' => 2, $reg_data => 2);

        // locate contacts layout
        $layout = $layouts_details['contacts'];
        $contacts_place = $layout['place'];
        $contacts_super_layout = '';
        if ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_MAIN_TO) {
            $contacts_super_layout = 'main_data';
        } elseif ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_ADDRESS_TO) {
            $contacts_super_layout = 'contact_data';
        } elseif ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_REG_TO) {
            $contacts_super_layout = $reg_data;
        }

        foreach ($layouts_details as $lkey => $layout) {
            // exclude layouts that are currently not in form, also exclude contacts
            // (it should be up to date with template if any modifications are made)
            if ($layout['view'] && $layout['edit'] && !in_array($lkey, array('salutation', 'department', 'assigned', 'iban', 'bic', 'contacts'))) {
                $super_layout = '';
                if ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_MAIN_TO) {
                    $super_layout = 'main_data';
                } elseif ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_ADDRESS_TO) {
                    $super_layout = 'contact_data';
                } elseif ($layout['place'] <= PH_CUSTOMERS_LAYOUTS_REG_TO) {
                    $super_layout = $reg_data;
                }

                // if layout is within a super-layout
                if ($super_layout) {
                    $num_view_layouts[$super_layout]++;
                    if (!$this->model->get('is_company') && $lkey == 'name') {
                        // add one more for lastname
                        $num_view_layouts[$super_layout]++;
                    }

                    // switch places so that contacts layout goes last in super-layout
                    if ($super_layout == $contacts_super_layout && $contacts_place < $layout['place']) {
                        $layouts_details['contacts']['place'] = $layout['place'];
                        $layouts_details[$lkey]['place'] = $contacts_place;
                        $contacts_place = $layouts_details['contacts']['place'];
                    }
                }
            }
        }

        // set number of layouts with view+edit permissions to model
        $this->model->set('num_view_layouts', $num_view_layouts, true);

        // reorder layouts
        uasort($layouts_details, function($a, $b) {
            return ($a['place'] - $b['place']) ?: ($a['keyword'] == 'contacts' ? 1 : ($a['id'] - $b['id']));
        });
        // set layouts back to model
        $this->model->set('layouts_details', $layouts_details, true);

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare template for company or person
        if ($this->model->get('is_company')) {
            $this->data['translate_template'] = $this->templatesDir . '_translate_company.html';
        } else {
            $this->data['translate_template'] = $this->templatesDir . '_translate_person.html';
        }

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        $basic_model_lang = $model_translations[0];
        //prepare the basic language model
        $filters = array('where' => array('c.id = ' . $this->model->get('id')),
                         'model_lang' => $basic_model_lang,
                         'sanitize' => true);
        $this->data['base_model'] = Customers::searchOne($this->registry, $filters);

        // get additional required fields
        $fields = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('type'));
        $this->data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('customers_translate'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
