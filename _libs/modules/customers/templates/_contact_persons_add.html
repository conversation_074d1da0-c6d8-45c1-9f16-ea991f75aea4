    {if $action eq 'ajax_add'}
      <div id="contact_errors" style="display: none;"></div>
    {/if}
    <form name="contact_persons_add" action="" method="post" onsubmit="saveContactPerson(this,'{$customers_contact_persons_session_param}', 'add', '{$contact_person->get('parent_customer_id')}'); return false;">
      <input type="hidden" name="branch_main_contacts" id="branch_main_contacts" value="{$branch_main_contacts|escape}" />
      <input type="hidden" name="parent_customer_id" id="parent_customer_id" value="{$contact_person->get('parent_customer_id')}" />
      <input type="hidden" name="model_lang" id="model_lang" value="{$contact_person->get('model_lang')|default:$lang}" />

      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_table_border t_layout_table" style="width: 75%;">
        <tr>
          <td class="t_caption" nowrap="nowrap" colspan="3"><div class="t_caption_title"><img src="{$theme->imagesUrl}flags/{$contact_person->get('model_lang')}.png" alt="" {capture assign='lang_label'}lang_{$contact_person->get('model_lang')}{/capture} title="{$smarty.config.$lang_label}" class="t_flag" />{#customers_contact_persons_add_contact#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox"><label for="salutation">{help label='contact_persons_salutation'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            {include file='input_dropdown.html'
                     name='salutation'
                     options=$salutations
                     value=$contact_person->get('salutation')
                     width=80
                     standalone=true
                     label=#customers_contact_persons_salutation#
            }
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="name">{help label='contact_persons_name'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="name" id="name" value="{$contact_person->get('name')|escape}" title="{#customers_contact_persons_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="lastname">{help label='contact_persons_lastname'}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="lastname" id="lastname" value="{$contact_person->get('lastname')|escape}" title="{#customers_contact_persons_lastname#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="main_contact">{help label='contact_persons_main_contact'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap" align="left"><input type="checkbox" name="main_contact" id="main_contact"{if $contact_person->get('main_contact')} checked="checked"{/if} title="{#customers_contact_persons_main_contact#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="financial_person">{help label='contact_persons_financial_person'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap" align="left"><input type="checkbox" name="financial_person" id="financial_person"{if $contact_person->get('financial_person')} checked="checked"{/if} title="{#customers_contact_persons_financial_person#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="position">{help label='contact_persons_position'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap"><input type="text" class="txtbox" name="position" id="position" value="{$contact_person->get('position')|escape}" title="{#customers_contact_persons_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="parent_branch">{help label_content=$contact_person->getBranchLabels('customers_contact_persons_branch')}</label></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <select name="parent_branch" id="parent_branch" class="selbox{if !$parent_branches} missing_records{elseif !$contact_person->get('parent_branch')} undefined{/if}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{$contact_person->getBranchLabels('customers_contact_persons_branch')|escape}">
            {if $parent_branches}
              <option value="" class="undefined">[{#please_select#|escape}]</option>
              {foreach from=$parent_branches item='parent_branch'}
                {if (!$parent_branch->isDeleted() && $parent_branch->isActivated()) || ($parent_branch->get('id') eq $contact_person->get('parent_branch'))}
                  <option value="{$parent_branch->get('id')}"{if ($parent_branch->isDeleted() || !$parent_branch->isActivated())} class="inactive_option" title="{#inactive_option#}"{/if}{if $parent_branch->get('id') eq $contact_person->get('parent_branch')} selected="selected"{/if}>{$parent_branch->get('name')|escape}</option>
                {/if}
              {/foreach}
            {else}
              <option value="" class="missing_records" selected="selected">{#no_select_records#|escape}</option>
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="assign_user">{help label='contact_persons_assigned'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <select name="assign_user" id="assign_user" class="selbox{if !$assign_users} missing_records{elseif !$contact_person->get('assign_user')} undefined{/if}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="{#customers_contact_persons_assigned#|escape}">
            {if $assign_users}
              <option value="" class="undefined">[{#please_select#|escape}]</option>
              {foreach from=$assign_users item='assign_user'}
                {if (!$assign_user->isDeleted() && $assign_user->isActivated()) || ($assign_user->get('id') eq $contact_person->get('assign_user'))}
                  <option value="{$assign_user->get('id')}"{if ($assign_user->isDeleted() || !$assign_user->isActivated())} class="inactive_option" title="{#inactive_option#}"{/if}{if $assign_user->get('id') eq $contact_person->get('assign_user')} selected="selected"{/if}>{$assign_user->get('firstname')|escape} {$assign_user->get('lastname')|escape}</option>
                {/if}
              {/foreach}
            {else}
              <option value="" class="missing_records" selected="selected">{#no_select_records#|escape}</option>
            {/if}
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="notes">{help label='contact_persons_notes'}</label></td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="notes" id="notes" title="{#customers_contact_persons_notes#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$contact_person->get('notes')|escape}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><label for="permission">{help label='contact_persons_permission'}</label></td>
          <td>&nbsp;</td>
          <td>
            <select class="selbox" name="permission" id="permission" title="{#customers_contact_persons_permission#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
              <option value="all"{if $contact_person->get('permission') eq 'all'} selected="selected"{/if}>{#customers_contact_persons_permission_all#|escape}</option>
              <option value="group"{if $contact_person->get('permission') eq 'group'} selected="selected"{/if}>{#customers_contact_persons_permission_group#|escape}</option>
              <option value="mine"{if $contact_person->get('permission') eq 'mine'} selected="selected"{/if}>{#customers_contact_persons_permission_mine#|escape}</option>
            </select>
          </td>
        </tr>
        <tr>
          <td class="labelbox" colspan="3">{help label='branches_contacts'}</td>
        </tr>
        <tr>
          <td nowrap="nowrap" colspan="3" class="nopadding">
            {include file=`$templatesDir`_contact_data.html object=$contact_person predefined_contact_params=$contact_person->predefinedPersonContactParameters}
          </td>
        </tr>
        <tr>
          <td colspan="3">
            {strip}
            {if $action eq 'ajax_add'}
              <button type="button" name="saveButton1" class="button" onclick="ajaxEditContact(this.form)">{#save#|escape}</button>
              <button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() {ldelim} lb.deactivate(); {rdelim}, this)" title="{#help_cancel#|escape}">{#cancel#|escape}</button>
            {else}
              <button type="submit" class="button" name="addContactPerson" id="addContactPerson">{#add#|escape}</button>
              <button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() {ldelim} $('contact_persons_custom_panel').innerHTML = ''; {rdelim}, this)" title="{#help_cancel#|escape}">{#cancel#|escape}</button>
            {/if}
            {/strip}
          </td>
        </tr>
        <tr>
          <td class="t_footer" colspan="3"></td>
        </tr>
      </table>
    </form>