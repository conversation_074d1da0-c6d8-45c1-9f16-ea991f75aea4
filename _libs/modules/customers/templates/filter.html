<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=customers&amp;customers=filter&amp;{if $relation}relation={$relation}&amp;{/if}{if $event}event={$event}&amp;{/if}{if $mynzoom_settings_table}mynzoom_settings_table={$mynzoom_settings_table}&amp;{/if}{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}{if $session_param}session_param={$session_param}&amp;{/if}page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="customers" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers" method="post" enctype="multipart/form-data">
      {if $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          {if !$autocomplete_params || $autocomplete_params.select_multiple}
            {include file="`$theme->templatesDir`_select_items.html"
              pages=$pagination.pages
              total=$pagination.total
              session_param=$session_param|default:$pagination.session_param
            }
          {else}
            {assign var='hide_selection_stats' value=true}
          {/if}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          {if $event}
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#customers_company#|escape}</div></td>
          {elseif $filter_contactpersons}
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#customer#|escape}</div></td>
          {else}
          <td class="t_caption t_border {$sort.is_company.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.is_company.link}">{#customers_company_person#|escape}</div></td>
          {/if}
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#customers_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#customers_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#customers_added#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$customers item='customer'}
      {strip}
      {assign var='salutation' value=''}
      {if !$customer->get('is_company') && $customer->get('salutation')}
        {assign var='layout_salutation' value=$customer->getLayoutsDetails('salutation')}
        {if $layout_salutation.view}
          {capture assign='salutation'}salutation_{$customer->get('salutation')}{/capture}
          {capture assign='salutation'}{$smarty.config.$salutation|escape} {/capture}
        {/if}
      {/if}
      {capture assign='info'}
        <strong>{#customers_name#|escape}:</strong> {$salutation}{$customer->get('name')|escape}{if !$customer->get('is_company')} {$customer->get('lastname')|escape}{/if}<br />
        <strong>{#customers_type#|escape}:</strong> {if $customer->get('subtype') eq 'contact'}{#customers_contact_person#|escape}{else}{$customer->get('type_name')|escape} ({if $customer->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}){/if}<br />
        <strong>{#added#|escape}:</strong> {$customer->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$customer->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$customer->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$customer->get('modified_by_name')|escape}<br />
        {if $customer->isDeleted()}<strong>{#deleted#|escape}:</strong> {$customer->get('deleted')|date_format:#date_mid#|escape}{if $customer->get('deleted_by_name')} {#by#|escape} {$customer->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$customer->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $customer->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$customer->get('active')} t_inactive{/if}{if $customer->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$customer->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
            {else}
                <input type="checkbox"
                   onclick="setCheckAllBox(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                           {rdelim});"
                   name='items[]'
                   value="{$customer->get('id')}{if $relation}_{if $customer->get('is_company')}company{else}person{/if}{/if}"
                   title="{#check_to_include#|escape}" />
           {/if}
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          {if $event}
          <td class="t_border">{$customer->get('company_name')|escape}</td>
          {elseif $filter_contactpersons}
          <td class="t_border">{$customer->get('customer_name')|escape}</td>
          {else}
          <td class="t_border {$sort.is_company.isSorted}">{if $customer->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}</td>
          {/if}
          <td class="t_border {$sort.name.isSorted}">
            {if $filter_contactpersons}
              {if $customer->get('is_main')}<img src="{$theme->imagesUrl}small/check_yes.png" width="12" height="12" border="0" alt="{#customers_contact_person_is_main#|escape}" title="{#customers_contact_person_is_main#|escape}" />{/if}
              {if $customer->get('admit_VAT_credit')}<img src="{$theme->imagesUrl}finance_fixing.png" width="12" height="12" border="0" alt="{#customers_contact_persons_financial_person#|escape}" title="{#customers_contact_persons_financial_person#|escape}" />{/if}
            {/if}
            {$salutation}{$customer->get('name')|escape}{if !$customer->get('is_company')} {$customer->get('lastname')|escape}{/if}
            <div id="rf{$customer->get('id')}" style="display: none">{$salutation}{$customer->get('name')|escape}{if !$customer->get('is_company')} {$customer->get('lastname')|escape}{/if}</div>
          </td>
          <td class="t_border {$sort.type.isSorted}">{if $customer->get('subtype') eq 'contact'}{#customers_contact_person#|escape}{else}{$customer->get('type_name')|escape}{/if}</td>
          <td class="t_border {$sort.added.isSorted}">{$customer->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$customer exclude="edit, view, delete"}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="7">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      <table border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td>
          {strip}
          {if $smarty.request.autocomplete_filter}
            {if $autocomplete_params.select_multiple}
              <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
            {/if}
            <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
          {*
          {elseif $event}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}updateEventAssign(el.form,'{$module}',0);{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}updateEventAssign(el.form,'{$module}',1);{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          {elseif $mynzoom_settings_table}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}updateEventDefaultAssign(el.form,'{$mynzoom_settings_table}','{$module}',0);{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}updateEventDefaultAssign(el.form,'{$mynzoom_settings_table}','{$module}',1);{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          *}
          {else}
            <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}{if !$relation}updateReferers(el.form, 0){else}updateCustomersRelations(el.form, '{$relation}', 0){/if};{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim} return confirmAction('link', function(el) {ldelim}{if !$relation}updateReferers(el.form, 1){else}updateCustomersRelations(el.form, '{$relation}', 1){/if};{rdelim}, this, '{#confirm_link_customers#|escape:'quotes'|escape}'); {rdelim}else{ldelim}alert('{#alert_link_customers#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
          {/if}
          {/strip}
          </td>
        </tr>
      </table>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
