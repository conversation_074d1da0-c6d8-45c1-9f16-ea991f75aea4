<h1 id="taksMainTitle">{$title}</h1>

<div id="form_container" style="width: 100%!important;">

{include file=`$theme->templatesDir`actions_box.html}

<form name="tasks" action="{$submitLink}" method="post">
<input type="hidden" name="type" id="type" value="{$tasks[0]->get('type')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$tasks[0]->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table" style="width: inherit">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="1" cellpadding="3" border="0">
        <tr>
          <td class="labelbox">{help label='type'}</td>
          <td class="unrequired">&nbsp;</td>
          <td nowrap="nowrap">{mb_truncate_overlib text=$tasktype->get('name')|escape|default:"&nbsp;"}</td>
        </tr>
        <tr>
          <td colspan="3" class="nopadding">
            <table id="var_table_0" class="t_grouping_table">
              <tr>
                <td class="t_caption3" style="width: 20px"><div class="t_caption3_title">{#num#|escape}</div></td>
                {foreach key='key' from=$tasks[0]->get('multivars') item='var'}
                  <td class="t_caption3"{if $var.hidden} style="display: none;"{/if}>
                    <div class="t_caption3_title floatl"><a name="error_{$var.name|replace:$smarty.const.PH_ADDITIONAL_VAR_PREFIX:''}"></a><label for="{$var.name}"{if $messages->getErrors($var.name|replace:$smarty.const.PH_ADDITIONAL_VAR_PREFIX:'')} class="error"{/if}>{help label_content=$var.label text_content=$var.help}</label>{if $var.required}{#required#}{/if}</div>
                    {if $var.last_visible}
                      <div class="t_buttons">
                        <div id="var_table_0_plusButton" onclick="addField('var_table_0');" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                        <div id="var_table_0_minusButton"{if count($tasks) le 1} class="disabled"{/if} onclick="removeField('var_table_0');" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
                      </div>
                    {/if}
                  </td>
                {/foreach}
              </tr>
              {foreach name='i' from=$tasks item='task'}
              <tr>
                <td class="hright">
                  {if $smarty.foreach.i.iteration eq 1}
                    <a href="javascript:void(0);">{$smarty.foreach.i.iteration}</a>
                  {else}
                    <a href="javascript: disableField('var_table_0','{$smarty.foreach.i.iteration}')">{$smarty.foreach.i.iteration}</a>
                  {/if}
                </td>
                {foreach key='key' from=$task->get('multivars') item='var'}
                  <td nowrap="nowrap" style="vertical-align: top;{if $var.hidden} display: none;{/if}">
                    {if $var.type eq 'text'}
                      {include file='input_text.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'date'}
                      {include file='input_date.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'datetime'}
                      {include file='input_datetime.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'time'}
                      {include file='input_time.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'dropdown'}
                      {include file='input_dropdown.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        options=$var.options
                        optgroups=$var.optgroups
                        on_change=$var.on_change
                        onchange=$var.onchange
                        sequences=$var.sequences
                        readonly=$var.readonly
                        required=$var.required
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'textarea'}
                      {include file='input_textarea.html'
                        standalone=true
                        name=$var.name
                        index=$smarty.foreach.i.iteration
                        label=$var.label
                        value=$var.val
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'autocompleter'}
                      {if is_array($var.autocomplete)}
                        {assign var='autocomplete' value=$var.autocomplete}
                        {assign var='autocomplete_type' value=''}
                      {else}
                        {assign var='autocomplete' value=''}
                        {assign var='autocomplete_type' value=$var.autocomplete}
                      {/if}
                      {include file='input_autocompleter.html'
                        index=$smarty.foreach.i.iteration
                        standalone=true
                        autocomplete_buttons='clear'
                        autocomplete_buttons_hide='add search refresh'
                        name=$var.name
                        autocomplete=$autocomplete
                        autocomplete_type=$autocomplete_type
                        autocomplete_var_type = 'basic'
                        required=$var.required
                        label=$var.label
                        value=$var.value
                        value_code=$var.value_code
                        value_name=$var.value_name
                        value_autocomplete=$var.value_autocomplete
                        help=$var.help
                        readonly=$var.readonly
                        hidden=$var.hidden
                        origin='multiadd'
                        width=$var.width}
                    {elseif $var.type eq 'checkbox'}
                      <input type="checkbox" name="{$var.name}[{$smarty.foreach.i.iteration-1}]" id="{$var.name}_{$smarty.foreach.i.iteration}" value="{$var.option_value}" title="{$var.label}" onfocus="highlight(this)" onblur="unhighlight(this)"{if $var.option_value eq $var.val} checked="checked"{/if}{if $var.onclick} onclick="{$var.onclick}"{/if}{if $disabled || $var.disabled} disabled="disabled"{/if} />
                    {/if}
                  </td>
                {/foreach}
              </tr>
              {/foreach}
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
            <input type="hidden" name="after_action" id="after_action_list" value="list" />
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td class="t_footer"></td>
  </tr>
</table>
</form>
</div>
