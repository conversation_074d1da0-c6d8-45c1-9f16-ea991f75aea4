<?php

class Tasks_Statements_Viewer extends Viewer {
    public $template = 'statements.html';

    public function prepare() {
        require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheets.factory.php';
        $current_user_tasks_settings = $this->registry['currentUser']->getPersonalSettings('tasks');
        $visible_statements_screen_1 = (isset($current_user_tasks_settings['visible_statements_screen_1']) ? $current_user_tasks_settings['visible_statements_screen_1'] : 1);
        $visible_statements_screen_2 = (isset($current_user_tasks_settings['visible_statements_screen_2']) ? $current_user_tasks_settings['visible_statements_screen_2'] : 1);
        $visible_statements_screen_3 = (isset($current_user_tasks_settings['visible_statements_screen_3']) ? $current_user_tasks_settings['visible_statements_screen_3'] : 1);
        $show_owner_assigned_records = (isset($current_user_tasks_settings['show_owner_assigned_records']) ? $current_user_tasks_settings['show_owner_assigned_records'] : 0);
        $this->data['visible_statements_screen_1'] = $visible_statements_screen_1;
        $this->data['visible_statements_screen_2'] = $visible_statements_screen_2;
        $this->data['visible_statements_screen_3'] = $visible_statements_screen_3;

        if ($this->registry->get('action') == 'statements' && $visible_statements_screen_1) {
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $selected_date = $this->registry['request']->get('date');

            $calendar = new Calendars_Calendar($selected_date);

            $i18nfiles = array();
            $lang_file = sprintf('%s%s%s%s%s',
                PH_MODULES_DIR,
                'calendars/',
                'i18n/',
                $this->registry['lang'],
                '/calendars.ini');
            if (file_exists($lang_file)) {
                $i18n_files[] = $lang_file;
            }

            $this->loadCustomI18NFiles($i18n_files);

            //get calendar settings
            $calendar_settings = Calendars_Calendar::getSettings($this->registry);
            $this->data['calendar_settings'] = $calendar_settings;

            //define some variables
            $day_start = 60 * $calendar_settings['week_start_hour'];
            $day_end   = 60 * $calendar_settings['week_end_hour'] + 60;
            $day_hours = ($day_end - $day_start)/60;
            $monday_start = $calendar_settings['week_start_day'];
            $week_days_number = $calendar_settings['week_days_number'];
            //minimum event interval
            $min_interval = 30;
            $current_day_of_week = (($monday_start && $calendar->day_of_week == 0) ? $week_days_number : $calendar->day_of_week);

            $week_timesheets = array();
            $all_day_week_timesheets = array();
            $total_time_per_day = array();

            // goes through all the week days and checks the timesheets
            for ($i = 0; $i < $week_days_number; $i++) {
                $total_time_per_day[$i] = 0;
                $timesheets = array();
                $all_day_timesheets = array();
                $day_of_week = $i + $monday_start;
                $selected_date = $calendar->offsetDay($day_of_week - $current_day_of_week);

                $selected_day_start = $selected_date . ' 00:00:00';
                $selected_day_end = $selected_date . ' 23:59:59';

                $filters = array('where' => array(
                                                'tt.user_id="' . $this->registry['currentUser']->get('id') . '"',
                                                '(tt.startperiod<="' . $selected_day_end . '" AND tt.endperiod>="' . $selected_day_start . '")'
                                            ),
                                 'sanitize' => true
                                );

                // takes the timesheets for the current user
                $timesheets_objects = Tasks_Timesheets::search($this->registry, $filters);

                $timesheets_tasks_ids = array();

                // prepare the ids to take the additional information for them
                foreach ($timesheets_objects as $key_timesheet => $timesheet_obj) {
                    $timesheets_tasks_ids[] = $timesheet_obj->get('task_id');
                }

                $timesheets_parent_info = Tasks_Timesheets::getTimesheetParentInfo($this->registry, $timesheets_tasks_ids);

                if (! empty($timesheets_parent_info)) {
                    foreach ($timesheets_objects  as $key_timesheet => $timesheet_obj) {
                        if (isset($timesheets_parent_info[$timesheet_obj->get('task_id')])) {
                            $timesheets_objects[$key_timesheet]->set('customer', $timesheets_parent_info[$timesheet_obj->get('task_id')]['customer_name'], true);
                            if ($timesheets_parent_info[$timesheet_obj->get('task_id')]['type']==PH_TASK_SYSTEM_TYPE) {
                                $timesheets_objects[$key_timesheet]->set('parent_id', $timesheets_parent_info[$timesheet_obj->get('task_id')]['link_to'], true);
                                $timesheets_objects[$key_timesheet]->set('parent_model', $timesheets_parent_info[$timesheet_obj->get('task_id')]['origin'] . 's', true);
                            } else {
                                $timesheets_objects[$key_timesheet]->set('parent_id', $timesheets_parent_info[$timesheet_obj->get('task_id')]['id'], true);
                                $timesheets_objects[$key_timesheet]->set('parent_model', 'tasks', true);
                            }
                        } else {
                            unset($timesheets_objects[$key_timesheet]);
                        }
                    }
                }

                $time_per_day = 0;
                foreach ($timesheets_objects as $key => $obj) {

                    $timesheets[$key] = $obj->getAll();

                    $startperiod_time_format = strtotime($timesheets[$key]['startperiod']);
                    $endperiod_time_format = strtotime($timesheets[$key]['endperiod']);

                    if ($timesheets[$key]['period_type'] == 'period') {
                        $all_day_timesheets[] = $timesheets[$key];
                        $total_days_continuing = (int)((($endperiod_time_format - $startperiod_time_format)/(60*60*24)) + 1);
                        if ($timesheets[$key]['duration']%$total_days_continuing) {
                            $time_per_day += floor($timesheets[$key]['duration']/$total_days_continuing);
                            if ($startperiod_time_format == strtotime($selected_date)) {
                                $time_per_day += $timesheets[$key]['duration']%$total_days_continuing;
                            }
                        } else {
                            $time_per_day += $timesheets[$key]['duration']/$total_days_continuing;
                        }
                        unset($timesheets[$key]);
                        continue;
                    } else {
                        $timesheets[$key]['timesheet_start_hour'] = date("G", $startperiod_time_format);
                        $timesheets[$key]['timesheet_start_minutes'] = date("i", $startperiod_time_format);
                        $timesheets[$key]['timesheet_end_hour'] = date("G", $endperiod_time_format);
                        $timesheets[$key]['timesheet_end_minutes'] = date("i", $endperiod_time_format);

                        $timesheets[$key]['start'] = (int)(($startperiod_time_format - strtotime($selected_day_start))/60);
                        $timesheets[$key]['end'] = (int)(($endperiod_time_format - strtotime($selected_day_start))/60);

                        $time_per_day += $timesheets[$key]['duration'];
                    }

                    $timesheets[$key]['selected_date'] = $selected_date;
                }

                $total_time_per_day[$i] = $time_per_day;

                $week_timesheets[$i] = $timesheets;
                $all_day_week_timesheets[$i] = $all_day_timesheets;
            }

            $all_day_week_timesheets_count = 0;
            foreach ($all_day_week_timesheets as $timesheet_day) {
                if (count($timesheet_day) > $all_day_week_timesheets_count) {
                    $all_day_week_timesheets_count = count($timesheet_day);
                }
            }

            $day_week_timesheets_count = 0;
            foreach ($week_timesheets as $timesheet) {
                if (count($timesheet) > $day_week_timesheets_count) {
                    $day_week_timesheets_count = count($timesheet);
                }
            }

            foreach ($total_time_per_day as $time_key => $time_unformatted) {
                $formatted_minutes = sprintf("%d", $time_unformatted%60);
                $formatted_hours = sprintf("%d", ($time_unformatted-$formatted_minutes)/60);

                $formatted_time = sprintf("%d:%02d", $formatted_hours, $formatted_minutes);
                $total_time_per_day[$time_key] = $formatted_time;
            }

            //check if the date is in current week
            $current_date_month_year = date("Y-W");

            $selected_date_stamp = strtotime($selected_date);
            $selected_date_month_year = date("Y-W", $selected_date_stamp);

            $hide_next_week_button = false;
            if ($current_date_month_year <= $selected_date_month_year) {
                $hide_next_week_button = true;
            }

            $this->data['current_date_formated'] = date('Y-m-d');
            $this->data['day_start'] = $day_start;
            $this->data['day_hours'] = $day_hours;
            $this->data['day_end'] = $day_end;
            $this->data['week_timesheets'] = $week_timesheets;
            $this->data['all_day_week_timesheets'] = $all_day_week_timesheets;
            $this->data['all_day_week_timesheets_count'] = $all_day_week_timesheets_count;
            $this->data['day_week_timesheets_count'] = $day_week_timesheets_count;
            $this->data['total_time_per_day'] = $total_time_per_day;
            $this->data['monday_start'] = $monday_start;
            $this->data['week_days_number'] = $week_days_number;
            $this->data['hide_next_week_button'] = $hide_next_week_button;
            $this->data['first_week_day'] = $calendar->offsetDay($monday_start-$current_day_of_week);
            $this->data['last_week_day'] = $calendar->offsetDay($week_days_number-$current_day_of_week);
            $this->data['calendar'] = $calendar;
        }

        //subpanel prepare data
        $selected_subpanel = '';
        if ($this->registry->get('statements_subpanel')) {
            $selected_subpanel = $this->registry->get('statements_subpanel');
        } else {
            if ($visible_statements_screen_2) {
                $selected_subpanel = 'list_timesheets_models';
            } elseif ($visible_statements_screen_3) {
                $selected_subpanel = 'free_add_timesheets';
            }
        }

        if ($this->theme->isModern()) {
            $this->data['dont_wrap_content'] = true;
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }

        $this->data['selected_subpanel'] = $selected_subpanel;
        $statements_subpanel_template_name = '';
        if ($selected_subpanel == 'list_timesheets_models') {
            $statements_subpanel_template_name = '_statements_list_timesheets_models.html';

            $addable_records = array();
            $records_add_timesheets = Tasks_Timesheets::filterRecords($this->registry);
            foreach ($records_add_timesheets as $module => $records) {
                foreach ($records as $record) {
                    $record->set('timesheets', array(), true);
                    $record->set('total_timesheets', 0, true);
                    $record->getStopWatchData();
                    $addable_records[$record->get('system_task')] = $record;
                }
            }
            unset($records_add_timesheets);
            krsort($addable_records);
            $tasks_ids = array_keys($addable_records);

            $all_task_timesheets = array();
            if (!empty($tasks_ids)) {
                //take number of timesheets
                $filters_timesheets  = array('where' => array(
                                                 'tt.task_id IN (' . implode(',', $tasks_ids) . ')'
                                             ),
                                             'sort' => array('tt.added DESC'),
                                             'sanitize'  => true);
                $all_task_timesheets = Tasks_Timesheets::search($this->registry, $filters_timesheets);
            }

            $timesheets_per_task = array();
            foreach ($all_task_timesheets as $task_timesheet) {
                if (! isset($timesheets_per_task[$task_timesheet->get('task_id')])) {
                    $timesheets_per_task[$task_timesheet->get('task_id')] = array();
                }
                $timesheets_per_task[$task_timesheet->get('task_id')][] = $task_timesheet;
            }

            foreach ($timesheets_per_task as $key_idx => $timesheets_for_model) {
                $addable_records[$key_idx]->set('timesheets', $timesheets_for_model, true);
                $addable_records[$key_idx]->set('total_timesheets', count($timesheets_for_model), true);
            }

            $this->data['addable_records'] = $addable_records;
        } elseif ($selected_subpanel == 'free_add_timesheets') {
            $statements_subpanel_template_name = '_statements_free_add_timesheets.html';

            require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';
            //prepare offices
            $filters = array('model_lang' => $this->registry['lang']);
            $offices_list = Offices::search($this->registry, $filters);
            $offices = array();

            foreach($offices_list as $office) {
                $offices[] = array(
                    'active_option' => $office->get('active'),
                    'label' => $office->get('name'),
                    'option_value' => $office->get('id'));
            }

            $this->data['offices'] = $offices;
            $this->data['activities'] = Dropdown::getTasksTimesheetsActivities(array($this->registry));
            $this->data['current_user_office'] = $this->registry['currentUser']->get('office');
            $this->data['current_user_id'] = $this->registry['currentUser']->get('id');
        }
        $this->data['statements_subpanel'] = $this->templatesDir . $statements_subpanel_template_name;

        if ($this->registry->get('action') != 'statements') {
            $this->template = $statements_subpanel_template_name;
            $this->setFrameset($this->templatesDir . $statements_subpanel_template_name);
        }

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        if (isset($this->title)) {
            $title = $this->title;
        } else {
            $title = $this->i18n('statements');
        }
        if (isset($this->subtitle)) {
            $subtitle = $this->subtitle;
        } else {
            $subtitle = '';
        }

        $this->data['title'] = $title;
        $this->data['subtitle'] = $subtitle;
    }
}

?>
