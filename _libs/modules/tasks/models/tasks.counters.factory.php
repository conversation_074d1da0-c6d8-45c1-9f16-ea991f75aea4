<?php

require_once('tasks.counters.model.php');
require_once('tasks.model.php');

/**
 * Tasks_Counters model class
 */
Class Tasks_Counters extends Model_Factory {
    public static $modelName = 'Tasks_Counter';

    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#tc.active#', $sort)) {
                $sort = 'ORDER BY tc.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY tc.active desc, tc.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT (tc.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_TASKS_COUNTERS . ' AS tc ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TASKS_COUNTERS_I18N . ' AS tci18n' . "\n" .
                       '  ON (tc.id=tci18n.parent_id AND tci18n.lang="' . $model_lang . '")' . "\n";

        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to user to fetch added by info
            $sql['from'] .=  'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                             '  ON (tc.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to user to fetch modified by info
            $sql['from'] .=  'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (tc.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }

        //where clause
        $sql['where'] = $where . "\n";

        if (isset($filters['group']) && !empty($filters['group'])) {
            $sql['group'] = 'GROUP BY ' . $filters['group'] . "\n";
        } else {
            $sql['group'] = 'GROUP BY  tc.id';
        }

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#tc.active#', $sort)) {
                $sort = 'ORDER BY tc.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY tc.active desc, tc.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT tc.*, tci18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_TASKS_COUNTERS . ' AS tc ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TASKS_COUNTERS_I18N . ' AS tci18n' . "\n" .
                       '  ON (tc.id=tci18n.parent_id AND tci18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (tc.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to user to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (tc.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to user to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (tc.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        //where clause
        $sql['where'] = $where . "\n";

        if (isset($filters['group']) && !empty($filters['group'])) {
            $sql['group'] = 'GROUP BY ' . $filters['group'];
        } else {
            $sql['group'] = 'GROUP BY tc.id';
        }

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(tc.id) AS total ';
                $sql['limit'] = '';
                $sql['group'] = '';
                $sql['order'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * construct the where clause
     */
    public static function constructWhere(&$registry, $filters = array()) {
         $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            $current_user_id = $registry['currentUser']->get('id');
            foreach ($filters['where'] as $filter) {
                if(preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if(!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#tc.deleted#', $where)) {
            $where .= ' AND tc.deleted = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     *
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        if (!is_array($ids)) {
            $ids[] = $ids;
        }

        $filters = array('id' => $ids, 'sanitize' => true);
        $counters = self::search($registry, $filters);

        $ids_to_delete = array();
        if (!empty($counters)) {
            foreach($counters as $counter) {
                if (!$counter->get('types')) {
                    $ids_to_delete[] = $counter->get('id');
                }
            }
        }

        if (empty($ids_to_delete)) {
            $return_code = -1;
            return $return_code;
        }

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids_to_delete, DB_TABLE_TASKS_COUNTERS);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result) {
            $result_code = 1;
            if (count($ids) != count($ids_to_delete)) {
                //some of the items were not deleted
                $result_code = 2;
            }
        } else {
            //error during deletion
            $result_code = 0;
        }

        return $result_code;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_TASKS_COUNTERS);

        if (!$restored) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];

        if (!is_array($ids)) {
            $ids[] = $ids;
        }

        $filters = array('id' => $ids, 'sanitize' => true);
        $counters = self::search($registry, $filters);

        $ids_to_delete = array();
        if (!empty($counters)) {
            foreach($counters as $counter) {
                if (!$counter->get('types')) {
                    $ids_to_delete[] = $counter->get('id');
                }
            }
        }

        if (empty($ids_to_delete)) {
            $return_code = -1;
            return $return_code;
        }

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::purgeMultiple($registry, $ids_to_delete, DB_TABLE_TASKS_COUNTERS);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result) {
            $result_code = 1;
            if (count($ids) != count($ids_to_delete)) {
                //some of the items were not deleted
                $result_code = 2;
            }
        } else {
            //error during deletion
            $result_code = 0;
        }

        return $result_code;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];
        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");

        $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_TASKS_COUNTERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

}

?>
