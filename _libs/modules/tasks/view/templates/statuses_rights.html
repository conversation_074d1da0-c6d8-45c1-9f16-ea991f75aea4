<h1>{$title}</h1>
<div id="form_container">
  {include file=`$theme->templatesDir`actions_box.html}
  <form action="{$submitLink}" method="post">
    <input type="hidden" name="model_name" id="model_name" value="Task" />
    {assign var=var value=$table_users}
    <table id="var_group_{$var.grouping}" class="t_table t_list"{if $var.t_width} width="{$var.t_width}"{/if} border="0" cellpadding="2" cellspacing="0" style="border-collapse:collapse;">
      <tr>
        <td class="t_caption t_bordert_top_border" style="text-align:right">
          <div class="t_caption_title" style="width: 29px;">{#num#|escape}</div>
        </td>
      {foreach key='key' from=$var.labels item='label'}
        {capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
        <td class="t_caption t_border"{if $var.hidden[$key]} style="display: none;"{/if}>
          <div class="t_caption_title"{if $var.widths[$key]}style="width: {$var.widths[$key]}px;"{/if}>
            {if !$var.hidden[$key]}
              {if $var.last_visible_column eq $key}
                <div class="floatl"{if $var.widths[$key]} style="width: {$var.widths[$key]-30}px;"{/if}>
                  <a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]}{#required#}{/if}</label>
                </div>
                {capture assign='values_count'}{if !empty($var.values) && is_array($var.values)}{$var.values|@count}{else}0{/if}{/capture}
                {include file="_table_buttons.html"
                        var=$var
                        values_count=$values_count
                }
                {if !empty($var.floating_buttons)}
                  {include file="_table_buttons.html"
                          var=$var
                          values_count=$values_count
                          floating_buttons=true
                  }
                {/if}
              {else}
                <div {if $var.hidden[$key]}style="display: none"{/if}>
                  <a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]}{#required#}{/if}</label>
                </div>
              {/if}
            {/if}
          </div>
        </td>
      {/foreach}
      </tr>
      {foreach name='i' from=$var.values item='val' key=kk}
      <tr id="var_group_{$var.grouping}_{$smarty.foreach.i.iteration}"{if $var.floating_buttons} onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"{/if}>
        <td align="right" nowrap="nowrap" class="t_border t_top_border">
          <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row"{if empty($var.values) || $var.values|@count le 1} style="visibility: hidden;"{/if} onclick="confirmAction('delete_row', function() {ldelim} hideField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}'); {rdelim}, this);" />&nbsp;<a href="javascript: disableField('var_group_{$var.grouping}','{$smarty.foreach.i.iteration}')">{$smarty.foreach.i.iteration}</a>
        </td>
        {foreach name='j' key='key' from=$var.names item='name'}
        <td class="t_border t_top_border"{if $var.hidden[$key]} style="display: none;"{/if}{if $var.types[$key] eq 'autocompleter' || $var.types[$key] eq 'formula' || $var.types[$key] eq 'index'}  nowrap="nowrap"{/if}>
          {if $var.types[$key] eq 'formula'}
            {assign var=formula_value value=$var.formula.$kk[$key].value}
          {else}
            {assign var=formula_value value=$var.index.$kk[$key].formula}
          {/if}
          {if $var[$name].$kk.options}
            {assign var=options value=$var[$name].$kk.options}
          {else}
            {assign var=options value=$var[$name].options}
          {/if}
          {if ($name == 'users' || $name == 'groups')}
            {if $options|@count gt 8}<div class="scroll" style="width: 100%!important; border: 0px none; background-color: transparent;">{/if}
            {foreach name='cb' from=$options item='option'}
              {if (!isset($option.active_option) || $option.active_option == 1 || is_array($val[$key]) && in_array($option.option_value, $val[$key]))}
              <input type="checkbox" name="{$name}[{$smarty.foreach.i.iteration-1}][]" id="{$name}_{$smarty.foreach.cb.iteration}_{$smarty.foreach.i.iteration}" value="{$option.option_value|escape}" title="{$option.label|strip_tags:false|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if is_array($val[$key]) && in_array($option.option_value, $val[$key])} checked="checked"{/if}{if $var.readonly[$key]} onclick="deselectCheckboxes(this)"{/if} /> <label for="{$name}_{$smarty.foreach.cb.iteration}_{$smarty.foreach.i.iteration}" class="{if $option.class_name}{$option.class_name} {/if}{if (isset($option.active_option) &&  $option.active_option == 0 && !$var.disable_inactive_style)}inactive_option{/if}"{if (isset($option.active_option) && $option.active_option == 0 && !$var.disable_inactive_style)} title="{#inactive_option#}"{/if}>{if (isset($option.active_option) && $option.active_option == 0 && !$var.disable_inactive_style)}* {/if}{mb_truncate_overlib text=$option.label|default:"&nbsp;" length=25 break_words=true}</label>{if $var.options_align[$key] ne 'horizontal' && ! $smarty.foreach.cb.last}<br />{/if}
              {/if}
            {/foreach}
            {if $options|@count gt 8}</div>{/if}
          {else}
            {include file=input_`$var.types[$key]`.html
                standalone=true
                name=$name
                index=$smarty.foreach.i.iteration
                value=$val[$key]
                value_id=$var.values_id.$kk.$key
                label=$var.labels[$key]
                description=$var.descriptions[$key]
                readonly=$var.readonly[$key]
                calculate=$var.calculate[$key]
                hidden=$var.hidden[$key]
                autocomplete=$var.autocomplete[$name]
                js_methods=$var.js_methods[$key]
                origin='group'
                height=$var.height[$key]
                options=$options
                optgroups=$var[$name].optgroups
                on_change=$var[$name].on_change
                onchange=$var[$name].onchange
                sequences=$var[$name].sequences
                really_required=$var.required[$key]
                required=$var.required[$key]
                view_mode=$var[$name].view_mode
                thumb_width=$var[$name].thumb_width
                thumb_height=$var[$name].thumb_height
                height=$var.height[$key]
                formulas=$formulas
                formula_value=$formula_value
                source=$var.formula.$kk[$key].source
                indexes=$indexes
                date_value=$var.index.$kk[$key].date
                do_not_show_check_all_button=$var[$name].do_not_show_check_all_button
                do_not_show_check_none_button=$var[$name].do_not_show_check_none_button
                first_option_label=$var[$name].first_option_label
                options_align=$var.options_align[$key]
            }
          {/if}
        </td>
        {/foreach}
      </tr>
      {foreachelse}
      <tr id="var_group_{$var.grouping}_1"{if $var.floating_buttons} onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);"{/if}>
        <td align="right" nowrap="nowrap" class="t_border t_top_border">
          <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function() {ldelim} hideField('var_group_{$var.grouping}','1'); {rdelim}, this);" />&nbsp;
          <a href="javascript: disableField('var_group_{$var.grouping}','1')">1</a>
        </td>
        {foreach key='key' from=$var.names item='name'}
        <td class="t_border t_top_border"{if $var.hidden[$key]} style="display: none;"{/if}{if $var.types[$key] eq 'autocompleter' || $var.types[$key] eq 'formula' || $var.types[$key] eq 'index'}  nowrap="nowrap"{/if}>
          {if $var.types[$key] eq 'formula'}
            {assign var=formula_value value=$var.formula.1[$key].value}
          {else}
            {assign var=formula_value value=$var.index.1[$key].formula}
          {/if}
          {assign var=options value=$var[$name].options}
          {if ($name == 'users' || $name == 'groups')}
            {if $options|@count gt 8}<div class="scroll" style="width: 100%!important; border: 0px none; background-color: transparent;">{/if}
            {foreach name='cb' from=$options item='option'}
              {if (!isset($option.active_option) || $option.active_option == 1 || is_array($val[$key]) && in_array($option.option_value, $val[$key]))}
              <input type="checkbox" name="{$name}[0][]" id="{$name}_{$smarty.foreach.cb.iteration}_1" value="{$option.option_value|escape}" title="{$option.label|strip_tags:false|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if is_array($val[$key]) && in_array($option.option_value, $val[$key])} checked="checked"{/if}{if $var.readonly[$key]} onclick="deselectCheckboxes(this)"{/if} />
              <label for="{$name}_{$smarty.foreach.cb.iteration}_1" class="{if $option.class_name}{$option.class_name} {/if}{if (isset($option.active_option) &&  $option.active_option == 0 && !$var.disable_inactive_style)}inactive_option{/if}"{if (isset($option.active_option) && $option.active_option == 0 && !$var.disable_inactive_style)} title="{#inactive_option#}"{/if}>{if (isset($option.active_option) && $option.active_option == 0 && !$var.disable_inactive_style)}* {/if}{mb_truncate_overlib text=$option.label|default:"&nbsp;" length=25 break_words=true}</label>{if $var.options_align[$key] ne 'horizontal' && ! $smarty.foreach.cb.last}<br />{/if}
              {/if}
            {/foreach}
            {if $options|@count gt 8}</div>{/if}
          {else}
            {include file=input_`$var.types[$key]`.html
              standalone=true
              name=$name
              index=1
              value=''
              value_id=''
              label=$var.labels[$key]
              description=$var.descriptions[$key]
              readonly=$var.readonly[$key]
              calculate=$var.calculate[$key]
              hidden=$var.hidden[$key]
              autocomplete=$var.autocomplete[$name]
              js_methods=$var.js_methods[$key]
              origin='group'
              height=$var.height[$key]
              options=$var[$name].options
              optgroups=$var[$name].optgroups
              on_change=$var[$name].on_change
              onchange=$var[$name].onchange
              sequences=$var[$name].sequences
              really_required=$var.required[$key]
              required=$var.required[$key]
              view_mode=$var[$name].view_mode
              thumb_width=$var[$name].thumb_width
              thumb_height=$var[$name].thumb_height
              width=$var.width[$key]
              height=$var.height[$key]
              formulas=$formulas
              formula_value=$formula_value
              source=$var.formula.1[$key].source
              indexes=$indexes
              date_value=$var.index.1[$key].date
              do_not_show_check_all_button=$var[$name].do_not_show_check_all_button
              do_not_show_check_none_button=$var[$name].do_not_show_check_none_button
              first_option_label=$var[$name].first_option_label
            }
          {/if}
        </td>
        {/foreach}
      </tr>
      {/foreach}
    </table>
  <br />
  <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
  </form>
</div>
