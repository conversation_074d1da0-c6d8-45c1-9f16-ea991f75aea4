<input type="text" class="txtbox doubled" name="formula" id="formula" value="{$documents_counter->get('formula')|escape}" title="{#documents_counters_formula#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" readonly="readonly" />
<br />
<br />
<input type="checkbox" name="formula_options[]" id="formula_options0" value="[prefix]" onclick="manageCounterFormula(this)"{if $documents_counter->get('prefix_used')} checked="checked"{/if} /><label for="formula_options0">{#documents_counters_formula_prefix#|escape}: </label><input type="text" class="txtbox small" name="prefix" id="prefix" value="{$documents_counter->get('prefix')|escape}" title="{#documents_counters_formula_prefix#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" /><br />
<input type="checkbox" name="formula_options[]" id="formula_options1" value="[document_num]" onclick="manageCounterFormula(this)"{if $documents_counter->get('document_num')} checked="checked"{/if} /><label for="formula_options1">{#documents_counters_formula_document_num#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options2" value="[customer_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('customer_code')} checked="checked"{/if} /><label for="formula_options2">{#documents_counters_formula_customer_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options3" value="[project_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('project_code')} checked="checked"{/if} /><label for="formula_options3">{#documents_counters_formula_project_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options4" value="[office_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('office_code')} checked="checked"{/if} /><label for="formula_options4">{#documents_counters_formula_office_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options5" value="[user_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('user_code')} checked="checked"{/if} /><label for="formula_options5">{#documents_counters_formula_user_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options6" value="[document_type_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('document_type_code')} checked="checked"{/if} /><label for="formula_options6">{#documents_counters_formula_document_type_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options13" value="[trademark_code]" onclick="manageCounterFormula(this)"{if $documents_counter->get('trademark_code')} checked="checked"{/if} /><label for="formula_options13">{#documents_counters_formula_trademark_code#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options7" value="[transform_subnum]" onclick="manageCounterFormula(this)"{if $documents_counter->get('transform_subnum')} checked="checked"{/if} /><label for="formula_options7">{#documents_counters_formula_transform_subnum#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options8" value="[parent_doc_num]" onclick="manageCounterFormula(this)"{if $documents_counter->get('parent_doc_num')} checked="checked"{/if} /><label for="formula_options8">{#documents_counters_formula_parent_doc_num#|escape}</label><br />
<input type="checkbox" name="formula_options[]" id="formula_options9" value="[customer_num]" onclick="manageCounterFormula(this)"{if $documents_counter->get('customer_num')} checked="checked"{/if} /><label for="formula_options9">{#documents_counters_formula_customer_num#|escape}</label>
(<input type="checkbox" name="customer_year" id="customer_year" value="1" {if $documents_counter->get('customer_year')} checked="checked"{/if} /><label for="customer_year">{#documents_counters_formula_customer_year#|escape}</label>)<br />
<input type="checkbox" name="formula_options[]" id="formula_options11" value="[office_num]" onclick="manageCounterFormula(this)"{if $documents_counter->get('office_num')} checked="checked"{/if} /><label for="formula_options11">{#documents_counters_formula_office_num#|escape}</label>
(<input type="checkbox" name="office_year" id="office_year" value="1" {if $documents_counter->get('office_year')} checked="checked"{/if} /><label for="office_year">{#documents_counters_formula_office_year#|escape}</label>)<br />
<input type="checkbox" name="formula_options[]" id="formula_options12" value="[trademark_num]" onclick="manageCounterFormula(this)"{if $documents_counter->get('trademark_num')} checked="checked"{/if} /><label for="formula_options12">{#documents_counters_formula_trademark_num#|escape}</label>
(<input type="checkbox" name="trademark_year" id="trademark_year" value="1" {if $documents_counter->get('trademark_year')} checked="checked"{/if} /><label for="trademark_year">{#documents_counters_formula_trademark_year#|escape}</label>)<br />
<input type="checkbox" name="formula_options[]" id="formula_options10" value="[document_added]" onclick="manageCounterFormula(this)"{if $documents_counter->get('document_added')} checked="checked"{/if} /><label for="formula_options10">{#documents_counters_formula_document_added#|escape}</label>
({#documents_counters_formula_date_format#|escape}
<select class="selbox small" name="date_format" id="date_format" title="{#documents_counters_formula_date_format#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
  {foreach from=$documents_counter->get('date_format_options') item='date_format_settings'}
    <option value="{$date_format_settings.date_format}" class="{$date_format_settings.class_name|escape}"{if $documents_counter->get('date_format') eq $date_format_settings.date_format} selected="selected"{/if}>{$date_format_settings.label|escape}</option>
  {/foreach}
</select>
{#documents_counters_formula_date_delimiter#|escape}
<select class="selbox small" name="date_delimiter" id="date_delimiter" title="{#documents_counters_formula_date_delimiter#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="changeDateFormatDropdown(this)">
  <option value=""{if $documents_counter->get('date_delimiter') === ''} selected="selected"{/if}>{#documents_counters_empty_delimiter#|escape}</option>
  <option value="."{if $documents_counter->get('date_delimiter') == '.'} selected="selected"{/if}>.</option>
  <option value="/"{if $documents_counter->get('date_delimiter') === false || $documents_counter->get('date_delimiter') == '/'} selected="selected"{/if}>/</option>
  <option value="-"{if $documents_counter->get('date_delimiter') == '-'} selected="selected"{/if}>-</option>
</select>)
<br />
<br />
<table>
  <tr>
    <td>
      {#documents_counters_formula_delimiter#|escape}:<br />
      <select class="selbox small" name="delimiter" id="delimiter" title="{#documents_counters_formula_delimiter#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="manageCounterFormula(this)">
        <option value="">{#documents_counters_empty_delimiter#|escape}</option>
        <option value="."{if $documents_counter->get('delimiter') == '.'} selected="selected"{/if}>.</option>
        <option value=","{if $documents_counter->get('delimiter') == ','} selected="selected"{/if}>,</option>
        <option value="/"{if $documents_counter->get('delimiter') == '/'} selected="selected"{/if}>/</option>
        <option value="-"{if $documents_counter->get('delimiter') == '-'} selected="selected"{/if}>-</option>
        <option value="|"{if $documents_counter->get('delimiter') == '|'} selected="selected"{/if}>|</option>
      </select>
    </td>
    <td>
      {#documents_counters_formula_leading_zeroes#|escape}:<br />
      <select class="selbox small" name="leading_zeroes" id="leading_zeroes" title="{#documents_counters_formula_leading_zeroes#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="manageCounterFormula(this)">
        <option value="3"{if $documents_counter->get('leading_zeroes') == '3'} selected="selected"{/if}>3</option>
        <option value="4"{if $documents_counter->get('leading_zeroes') == '4'} selected="selected"{/if}>4</option>
        <option value="5"{if $documents_counter->get('leading_zeroes') == '5'} selected="selected"{/if}>5</option>
        <option value="6"{if !$documents_counter->get('leading_zeroes') || $documents_counter->get('leading_zeroes') == '6'} selected="selected"{/if}>6</option>
        <option value="7"{if $documents_counter->get('leading_zeroes') == '7'} selected="selected"{/if}>7</option>
        <option value="8"{if $documents_counter->get('leading_zeroes') == '8'} selected="selected"{/if}>8</option>
        <option value="9"{if $documents_counter->get('leading_zeroes') == '9'} selected="selected"{/if}>9</option>
        <option value="10"{if $documents_counter->get('leading_zeroes') == '10'} selected="selected"{/if}>10</option>
      </select>
    </td>
  </tr>
</table>
<br />
<br />
{#documents_counters_formula_note#}<br />
