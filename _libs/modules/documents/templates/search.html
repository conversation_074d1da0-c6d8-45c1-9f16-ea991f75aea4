<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;documents=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="documents" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{$basic_vars_labels.full_num|default:#documents_full_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.custom_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.custom_num.link}">{$basic_vars_labels.custom_num|default:#documents_custom_num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#documents_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{$basic_vars_labels.type|default:#documents_type#|escape}</div></td>
          <td class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer|default:#documents_customer#|escape}</div></td>
          <td class="t_caption t_border {$sort.department.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.department.link}">{$basic_vars_labels.department|default:#documents_department#|escape}</div></td>
          <td class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#documents_status#|escape}</div></td>
          <td class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.tags.link}">{#documents_tags#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#documents_added#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$documents item='document'}
      {strip}
      {capture assign='info'}
        <strong><u>{$basic_vars_labels.full_num|default:#documents_full_num#|escape}:</u></strong> {$document->get('full_num')|numerate:$document->get('direction')}<br />
        <strong>{$basic_vars_labels.name|default:#documents_name#|escape}:</strong> {$document->get('name')|mb_truncate:$smarty.const.PH_MAX_TRUNCATE_ABOUT|escape}<br />
        <strong>{$basic_vars_labels.type|default:#documents_type#|escape}:</strong> {$document->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$document->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$document->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$document->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$document->get('status_modified_by_name')|escape}<br />
        {if $document->isDeleted()}<strong>{#deleted#|escape}:</strong> {$document->get('deleted')|date_format:#date_mid#|escape}{if $document->get('deleted_by_name')} {#by#|escape} {$document->get('deleted_by_name')|escape}{/if}<br />{/if}
        {if $document->get('archived_by')}<strong>{#archived#|escape}:</strong> {$document->get('archived')|date_format:#date_mid#|escape}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$document->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $document->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='document_status'}
        {if $document->get('status') eq 'opened'}
          {#help_documents_status_opened#}
        {elseif $document->get('status') eq 'locked'}
          {#help_documents_status_locked#}
        {elseif $document->get('status') eq 'closed'}
          {#help_documents_status_closed#}
        {/if}
        {if $document->get('substatus_name')}
          <br />
          {#help_documents_substatus#}{$document->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$document assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      {if !$document->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}{if !$document->get('active')} t_inactive{/if}{if $document->get('archived_by')} attention{/if}{if $document->get('deleted_by')} t_deleted{/if}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$document->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="9" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$document disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$document->get('active')} t_inactive{/if}{if $document->get('archived_by')} attention{/if}{if $document->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});"
                   type="checkbox"
                   name='items[]'
                   value="{$document->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($document->get('id'), $selected_items.ids) ||
                       (@$selected_items.select_all eq 1 && @!in_array($document->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright" nowrap="nowrap">
          {if $document->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=attachments&amp;attachments={$document->get('id')}{if $document->get('archived_by')}&amp;archive=1{/if}">
                <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$document->get('id')}{if $document->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
          {/if}
          {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.full_num.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$document->get('id')}{if $document->get('archived_by')}&amp;archive=1{/if}">{$document->get('full_num')|numerate:$document->get('direction')}</a></td>
          <td class="t_border {$sort.custom_num.isSorted} {$row_link_class}"{$row_link}>{$document->get('custom_num')|default:"&nbsp;"}</td>
          <td class="t_border {$sort.name.isSorted} {$row_link_class}"{$row_link}>{$document->get('name')|mb_truncate:$smarty.const.PH_MAX_TRUNCATE_ABOUT|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.type.isSorted} {$row_link_class}"{$row_link}>{$document->get('type_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.customer.isSorted}"><a href="{$smarty.server.PHP_SELF}?{$module_param}=customers&amp;customers=view&amp;view={$document->get('customer')}" title="{#view#|escape}: {$document->get('customer_name')|escape}">{$document->get('customer_name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.department.isSorted} {$row_link_class}"{$row_link}>{$document->get('department_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape caption=#help_documents_status#|escape width=250}{if $document->checkPermissions('setstatus')} onclick="changeStatus({$document->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $document->get('status') != 'closed' && $document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: <strong>{$document->get('deadline')|date_format:#date_mid#}</strong>!
            {/if}
            {if $document->get('status') != 'closed' && $document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: <strong>{$document->get('validity_term')|date_format:#date_mid#}</strong>!
            {/if}
          {/capture}
          {if $document->get('status') != 'closed' && (($document->get('deadline') && $document->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($document->get('validity_term') && $document->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=$document_expired|escape caption=#documents_expired#|escape width=250} />
          {/if}
          {if $document->get('substatus_name')}
            {if $document->get('icon_name')}
              <img src="{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$document->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}documents_{$document->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$document->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}documents_{$document->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}documents_status_{$document->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.tags.isSorted}" {if $document->getModelTags() && $document->get('available_tags_count') gt 0 && $document->checkPermissions('tags_view') && $document->checkPermissions('tags_edit')} onclick="changeTags({$document->get('id')}, 'documents')" style="cursor: pointer;" title="{#tags_change#}"{/if}>
            {if $document->get('model_tags')|@count gt 0 && $document->checkPermissions('tags_view')}
              {foreach from=$document->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$document->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$document}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="12">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html
               tags=$tags
               include="tags,multistatus,multiprint,purge"
               session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
