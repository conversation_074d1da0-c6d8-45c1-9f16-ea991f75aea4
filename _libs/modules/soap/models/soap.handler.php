<?php

class Soap_Handler {

    /**
     * Global registry
     */
    public $registry = '';

    /**
     * Handler constructor
     */
    public function __construct() {
        $this->registry = $GLOBALS['registry'];

        //get language files
        $dir = PH_MODULES_DIR . 'soap/plugins/' . $this->registry['request']->get('plugin') . '/i18n/' . $this->registry['lang'] . '/';
        $files = FilesLib::readDir($dir, true, 'files_only', 'ini', true);
        //load files
        $this->registry['translater']->loadFile($files);
    }


    /**
    * Translates variables stored in the common or module specific i18n files
    *
    * @param string $param - the variable to be translated
    * @param array $placeholders - array of placeholders to replace in the translated param
    * @return string - the translation in UTF8
    */
    protected function i18n($param, $placeholders = array()) {
        $translated =  $this->registry['translater']->translate($param);

        //replace placeholders
        if (count($placeholders) > 0){
            if (preg_match('#%#', $translated)) {
                $translated = vsprintf($translated, $placeholders);
            } else {
                foreach ($placeholders as $find => $replace) {
                    $translated = str_replace('['.$find.']',$replace, $translated);
                }
            }
        }

        return $translated;
    }
}
