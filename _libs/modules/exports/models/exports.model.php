<?php

/**
 * Exports model class
 */
Class Export extends Model {
    public $modelName = 'Export';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //load needed lang files
        $i18n_files = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'exports/i18n/', $registry['lang'], '/exports.ini'),
            sprintf('%s%s%s%s%s%s', PH_MODULES_DIR, 'exports/plugins/',  $this->get('type'), '/i18n/', $registry['lang'], '/exports.ini'),
        );

        //manage the settings
        if ($this->get('settings')) {
            $settings = preg_split('/(\n|\r|\r\n)/', $this->get('settings'));
            foreach ($settings as $s) {
                //row started with # is comment
                if (empty($s) || $s[0]=='#') {
                    continue;
                }
                list($key, $value) = preg_split('/\s*\:=\s*/', $s);
                if (!$this->isDefined($key)) {
                    $this->set($key, $value, true);
                }
            }
        }

        $this->prepareSettings();

        $this->loadI18NFiles($i18n_files);
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - controller action
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        return $this->valid;
    }

    /**
     * Function to prepare settings from the database in a separate array
     *
     * @return array with settings
     */
    public function prepareSettings() {
        $settings = array();
        $instructions = preg_split('/(\n|\r|\r\n)/', $this->get('settings'));
        foreach ($instructions as $instruction) {
            if (preg_match('#^\##', $instruction) || !preg_match('#\:\=#', $instruction)) {
                continue;
            }
            list($key, $value) = preg_split('#\s*\:=\s*#', $instruction);
            $settings[$key] = $value;
        }

        $this->set('settings', $settings, true);
        return $settings;
    }

    /**
     * Function to load the settings from the model as defined constants
     *
     * @return bool
     */
    public function loadPreparedSettings() {
        foreach ($this->get('settings') as $key => $value) {
            if (!defined(strtoupper($key))) {
                if (preg_match('#^\\\[a-z]#', $value)) {
                    //the value is a special char, eval it first
                    $value = EvalString::evaluate($this->registry, '"' . $value . '";');
                    define(strtoupper($key), "$value");
                } else {
                    define(strtoupper($key), $value);
                }
            }
        }

        return true;
    }

    /**
     * Launches export plugin
     *
     * @return bool - true if valid, false if invalid
     */
    public function launch() {
        //if there are files for this plugin
        if (file_exists(PH_MODULES_DIR . "exports/plugins/" . $this->get('type') . "/custom.export.php")) {
            //load the plugin settings as constants
            $this->loadPreparedSettings();

            //load the plugin
            require_once PH_MODULES_DIR . "exports/plugins/" . $this->get('type') . "/custom.export.php";

            //if the Custom_Export class and his method export() exists
            if (class_exists('Custom_Export') && method_exists('Custom_Export', 'export')) {
                //execute the export
                $this->registry['export_type'] = $this->get('type');
                Custom_Export::$settings = $this->get('settings');
                $export_result = Custom_Export::export($this->registry);

                //if the export is successful
                if (empty($export_result['error'])) {
                    //uncheck the selected (i.e. exported) items by removing them from the session
                    $this->registry['session']->remove($this->registry['request']->get('session_param'), 'selected_items');
                    exit;
                } else {
                    //show error messages
                    $this->registry['messages']->setError($this->i18n('error_export_failed'),'',-1);
                    foreach($export_result['error'] as $err_msg) {
                        $this->registry['messages']->setError($err_msg, '');
                    }
                    $this->registry['messages']->insertInSession($this->registry);
                }
            } else {
                //show error message
                $this->registry['messages']->setError($this->i18n('error_export_failed'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);
            }
        } else {
            //there are no files for this plugin
            $this->registry['messages']->setError($this->i18n('error_export_failed'),'',-1);
            $this->registry['messages']->setError($this->i18n('error_export_invalid_plugin'),'');

            $this->registry['messages']->insertInSession($this->registry);
        }
    }

}

?>