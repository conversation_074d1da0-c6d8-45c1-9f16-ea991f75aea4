<?php

/**
 * Offices model class
 */
Class Office extends Model {
    public $modelName = 'Office';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
    }

    /**
     * Checks the validity of the model
     *
     * @param $action string - action to check model for
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        // Get some commonly used vars
        $id   = $this->get('id');
        $code = $this->get('code');

        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        if (!$code) {
            if (!$id) {
                $code = $this->setCode();
            } elseif ($this->isDefined('code') && !$this->registry['config']->getParam('offices', 'code_not_unique')) {
                //check settings, for some installations code should not be unique (Bug: 2920)
                $this->raiseError('error_no_code', 'code');
            }
        }
        if ($code) {
            // If there are any other offices with the same code
            $query = 'SELECT `id` FROM `' . DB_TABLE_OFFICES .'` WHERE `code`= \'' . General::slashesEscape($code) . '\'';
            if ($id) {
                $query .= " AND `id` != {$id}";
            }
            $query .= ' LIMIT 1';
            $code_exists = $this->registry['db']->GetOne($query);
            if ($code_exists) {
                // Set an error
                $this->raiseError('error_code_not_unique', 'code');
            }
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_OFFICES . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new office base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        //update administrator access to added model
        $this->updateAdminRole();

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_OFFICES . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }


    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        $set['code']        = sprintf("code='%s'", $this->get('code'));
        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')){
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name'] = sprintf("name='%s'", $this->get('name'));
        if ($this->isDefined('description')) {
            $update['description'] = sprintf("description='%s'", $this->get('description'));
        }

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        $query2 = 'INSERT INTO ' . DB_TABLE_OFFICES_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Update administratr role
     *
     * @return bool - result of the operation
     */
    public function updateAdminRole() {
        if (defined('DB_TABLE_FINANCE_OFFICES_PERMISSIONS')) {
            $db = $this->registry['db'];
            $insert['parent_id'] = 'parent_id=1';
            $insert['origin'] = 'origin="role"';
            $insert['office_id'] = sprintf("office_id='%d'", $this->get('id'));

            //query to insert administrator acces to this model
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_FINANCE_OFFICES_PERMISSIONS . "\n" .
                      'SET ' . implode(', ', $insert) . "\n";

            $db->Execute($query);

            if ($db->ErrorMsg()) {
                return false;
            } else {
                return true;
            }
        }

        return true;
    }
}

?>
