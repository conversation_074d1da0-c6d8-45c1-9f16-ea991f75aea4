<?php

/**
 * Projects_Section model class
 */
Class Projects_Section extends Model {
    public $modelName = 'Projects_Section';

    public $width = 16;

    public $height = 16;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
    }
    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_section_name_specified', 'name');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //prepare main data from post
        $set['name']          = sprintf("name='%s'", $this->get('name'));
        $set['description']   = sprintf("description='%s'", $this->get('description'));
        $set['position'] = sprintf("position='%s'", $this->get('position'));
        $set['lang']          = sprintf("lang='%s'", $this->get('model_lang'));
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['modified']      = sprintf("modified=now()");
        $set['modified_by']   = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        }

        //start transaction
        $db->StartTrans();
        $query = 'SELECT id FROM ' . DB_TABLE_PROJECTS_SECTIONS . "\n" .
                  'ORDER BY id DESC LIMIT 1' . "\n";
        $id = @intval($db->GetOne($query)) + 1;

        $set['id']      = sprintf("id=%d", $id);

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_PROJECTS_SECTIONS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new section base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        $this->set('id', $id, true);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edit model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //prepare main data from post
        $insert['id']            = sprintf("id=%d", $this->get('id'));
        $insert['lang']          = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['name']          = sprintf("name='%s'", $this->get('name'));
        $insert['description']   = sprintf("description='%s'", $this->get('description'));
        $insert['modified']      = sprintf("modified=now()");
        $insert['modified_by']   = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        $insert['added']         = sprintf("added=now()");
        $insert['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        $update['name']          = sprintf("name='%s'", $this->get('name'));
        $update['description']   = sprintf("description='%s'", $this->get('description'));
        $update['modified']      = sprintf("modified=now()");
        $update['modified_by']   = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('position')) {
            $insert['position'] = sprintf("position='%s'", $this->get('position'));
            $update['position'] = sprintf("position='%s'", $this->get('position'));
        }

        if ($this->isDefined('group')) {
            $update['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        if ($this->isDefined('active')) {
            $update['active'] = sprintf("active=%d", $this->get('active'));
        }

        //start transaction
        $db->StartTrans();

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_PROJECTS_SECTIONS . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query1);

        if ($this->isDefined('active') || $this->isDefined('group')) {
            $update['active'] = sprintf("active=%d", $this->get('active'));

            //update all the records for the languages
            $query2 = 'UPDATE ' . DB_TABLE_PROJECTS_SECTIONS . "\n" .
                      'SET active=' . $this->get('active') . ', `group`=' . $this->get('group') . "\n" .
                      'WHERE id=' . $this->get('id');

            $db->Execute($query2);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks model translations
     *
     * @return bool - array of available languages
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT ps.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_PROJECTS_SECTIONS . ' AS ps' . "\n";

        //where clause
        $sql['where'] = 'WHERE ps.id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY ps.added' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Checks if image is selected and uploads it to the server
     *
     * @return bool - if the action was successful or not
     */
    public function imageCreate() {
        if (! empty($_FILES['icon_file']['name'])) {
            $tmp_file_name = $_FILES['icon_file']['tmp_name'];
            $file_info = getimagesize($tmp_file_name);
            $file_extension = FilesLib::getImageType($file_info[2]);
            if ($file_extension) {
                $id = $this->get('id');
                if (! is_dir(PH_PROJECTS_SECTIONS_DIR)) {
                    FilesLib::createDir(PH_PROJECTS_SECTIONS_DIR, 0777, true);
                }
                $filename = 'section_' . $id . '.' . $file_extension;
                $restrictions = array('max_width' => $this->width, 'max_height' => $this->height);
                $result_upload = FilesLib::uploadFile($_FILES['icon_file'], PH_PROJECTS_SECTIONS_DIR, $filename, $restrictions);
                if ($result_upload) {
                    $result = $this->updateTableIcon($id, $file_extension);
                    chmod(PH_PROJECTS_SECTIONS_DIR . $filename, 0777);
                    if ($result) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * Updates projects_section table when an ison is uploaded
     *
     * @int id - the id of the section to be updated
     * @string $file_extension - the file extension of the uploaded file
     * @return reource result - result of the operation
     */
    public function updateTableIcon($id, $file_extension='', $delete_file='') {
        if ($delete_file) {
            $file_name = '';
        } else {
            $file_name = 'section_' . $id . '.' . $file_extension;
        }

        $db = $this->registry['db'];
        $db->StartTrans();
        $query = 'UPDATE ' . DB_TABLE_PROJECTS_SECTIONS . "\n" .
                 ' SET icon_name="' . $file_name . '"' . "\n" .
                 ' WHERE id="' . $id . '"';
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        if ($result && $delete_file) {
            $icon_file_delete = PH_PROJECTS_SECTIONS_DIR . $delete_file;
            unlink($icon_file_delete);
        }

        return $result;
    }


}

?>
