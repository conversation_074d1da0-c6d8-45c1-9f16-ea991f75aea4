projects_types = Tipuri proiecte
projects_types_name = Nume
projects_types_name_plural = Nume (pentru lista și meniu)
projects_types_section = Secțiune
projects_types_counter = Numarator
projects_types_add_counter = Adăuga noi
projects_types_status = Status
projects_types_status_active = Activ
projects_types_status_inactive = Inactiv
projects_types_added_by = Adăugat de
projects_types_modified_by = Modificat de
projects_types_added = Adăugat la
projects_types_modified = Modificat la
projects_types_add = Adăugare tip proiecte
projects_types_edit = Editare tip proiecte
projects_types_translate = Traducere tip proiect
projects_types_view = Vizualizare tip proiect
projects_types_description = Descriere
projects_types_generate_system_task = Generare raport
projects_types_count_projects = Număr proiecte
projects_types_default_name = Nume proiect
projects_types_group = Grup
projects_types_default_user_group = [Grup default al utilizatorului curent]
projects_types_basic_settings = Setări de bază
projects_types_counter_settings = Setările counte
projects_types_additional_settings_of_fields = Setări suplimentare de câmpuri
projects_types_tasks_and_completion = Sarcini și completare
projects_types_default_settings = Setări default
projects_types_validate = Câmpuri obligatorii
projects_types_validate_unique = Câmpuri unice
projects_types_validate_unique_current_year = Numai pentru anul curent
projects_types_requires_completed_minitasks = Sarcinile mini finalizate
projects_types_requires_completed_documents = Documente finalizate
projects_types_requires_completed_tasks = Sarcini finalizate
projects_types_requires_completed_events = Evenimente finalizate
projects_types_related_customers_types = Tipuri contragenți pentru AC

projects_layouts_name = Nume
projects_layouts_type = Tip
projects_layouts_customer = Contragent
projects_layouts_code = Cod
projects_layouts_num = Număr
projects_layouts_manager = Conducător
projects_layouts_date_start = Început
projects_layouts_date_end = Sfârșit
projects_layouts_priority = Prioritate
projects_layouts_parent_project = Subproiect la
projects_layouts_referers = Legătură cu proiecte
projects_layouts_budget = Buget
projects_layouts_work_period = Durata
projects_layouts_finished_part = % executare
projects_layouts_description = Descriere
projects_layouts_notes = Note

message_projects_types_add_success = Adăugate cu succes de tip proiect
message_projects_types_edit_success = Editare cu succes de tip proiect
message_projects_types_translate_success = Traducere cu succes de tip proiect

error_projects_types_edit_failed = Editare tip proiect FĂRĂ SUCCES:
error_projects_types_add_failed = Adăugare tip proiect FĂRĂ SUCCES:
error_projects_types_translate_failed = Traducere tip proiect FĂRĂ SUCCES:

error_no_typename_specified = Vă rugăm să introduceți nume!
error_no_typename_plural_specified = Vă rugăm să introduceți nume (pentru lista și meniu)!
error_no_type_specified = Selectați tip proiect
error_no_such_project_type = Nu puteți vizualiza această înregistrare!

help_projects_types_generate_system_task = Generare automată de sarcină tip ”raport” la crearea raporturilor din acest tip
help_projects_types_default_name = Câmpul definește nume default la adăugarea proiectului din tipul respectiv. În numele se pot include următoarele variabile, care la introducerea unui proiect nou vor fi înlocuite cu valorile respective: <br /><strong>[project_code]</strong> - cod proiect<br /><strong>[customer_name]</strong> - nume client
help_projects_types_validate_unique_current_year = Se verifică unicitatea câmpurilor setate față de înregistrările adăugate în cursul anului curent sau față de toate ?
help_projects_types_requires_completed_minitasks = Cerință să nu existe sarcini mici neexecutate pentru a putea fi finalizat.
help_projects_types_requires_completed_documents = Înregistrarea nu poate fi finalizată dacă la ea există documente nefinalizate.
help_projects_types_requires_completed_tasks = Înregistrarea nu poate fi finalizată dacă are sarcini neexecutate.
help_projects_types_requires_completed_events = Înregistrarea nu poate fi finalizată dacă are evenimente nefinalizate.
