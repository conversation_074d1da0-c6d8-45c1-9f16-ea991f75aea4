<?php

class Projects_Counters_Translate_Viewer extends Viewer {
    public $template = 'counters_translate.html';

    public function prepare() {
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTranslations();

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //get the basic translation language of the model
        $model_translations = $this->model->getTranslations();
        //basic model lang is the first language the model has been translated to
        //prepare the basic language model
        $filters = array('where' => array('pc.id = ' . $this->model->get('id')),
                         'model_lang' => $model_translations[0],
                         'sanitize' => true);
        $this->data['base_model'] = Projects_Counters::searchOne($this->registry, $filters);
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('projects_counters_translate');
    }
}

?>
