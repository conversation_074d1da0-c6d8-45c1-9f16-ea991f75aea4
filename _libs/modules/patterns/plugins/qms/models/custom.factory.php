<?php

/**
 * Custom plugin to prepare data for print/genaration for BGService
 */
Class Custom_Factory extends Model_Factory {
    public $folderName = 'qms';
    public static $registry;
    public static $model;

    /**
     * Prepares some custom data for footer
     *
     * @param object $registry - registry object
     * @param object $model - document, finance document etc.
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareHeaderFooter(&$registry, &$model, &$pattern, &$params = array()) {
        //the plugin for header/footer works only for documents
        if ($model->modelName != 'Document') {
            return false;
        }
        $model_type = $model->get('type');

        //IMPORTANT: the pattern plugin should be configured with these settings:
        //           type := header
        //           nom_type := 18
        //           nom_var_name := nom_var_name
        $nom_type = $params['settings']['nom_type'];
        $nom_var_name = $params['settings']['nom_var_name'];

        $db = &$registry['db'];
        //get the corresponding nomenclature
        $query = 'SELECT nc.model_id FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc ' . "\n" .
                 'INNER JOIN ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                 '  ON (nc.var_id=fm.id)' . "\n" .
                 'WHERE fm.name="' . $nom_var_name . '"' . "\n" .
                 '      AND fm.model="Nomenclature"' . "\n" .
                 '      AND fm.model_type=' . $nom_type . "\n" .
                 '      AND nc.value=' . $model_type;
        $nom_id = $registry['db']->GetOne($query);

        if ($nom_id) {
            //IMPORTANT: this array contains variables names, they should not be reconfigured in fields_meta
            //           this array is hard-coded and should not be reconfigured
            $variable_names = array('current_version', 'date_current');
            $query = 'SELECT fm.name, nc.value FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc ' . "\n" .
                     'INNER JOIN ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
                     '  ON (nc.var_id=fm.id)' . "\n" .
                     'WHERE nc.model_id="' . $nom_id . '"' . "\n" .
                     '      AND fm.model="Nomenclature"' . "\n" .
                     '      AND fm.model_type=' . $nom_type . "\n" .
                     '      AND fm.name IN ("' . implode('", "', $variable_names) . '")';
            $data = $registry['db']->GetAssoc($query);

            $query = 'SELECT code FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id= ' . $nom_id;
            $data['code'] = $registry['db']->GetOne($query);
        } else {
            return false;
        }

        foreach ($data as $placeholder => $replacement) {
            $model->extender->add($params['settings']['type'] . '_' . $placeholder, $replacement);
        }

        return true;
    }
}

?>
