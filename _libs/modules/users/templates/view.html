{* tree initialization *}
<script type='text/javascript'>
  var func1 = function() {ldelim}initTree('groups'){rdelim}
  var func2 = function() {ldelim}initTree('departments'){rdelim}
  Event.observe(window, 'load', func1);
  Event.observe(window, 'load', func2);
</script>

<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_main_data_switch"><div class="switch_{if $smarty.cookies.user_main_data_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{#users_main_data#|escape}</div></td>
        </tr>
        <tr id="user_main_data"{if $smarty.cookies.user_main_data_box eq 'off'} style="display: none"{/if}>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox">{help label='salutation'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {capture assign='salutation'}salutation_{$user->get('salutation')}{/capture}
                  {$smarty.config.$salutation|escape}
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="firstname">{help label='firstname'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {$user->get('firstname')|escape}
                  <input type="hidden" name="firstname" id="firstname" value="{$user->get('firstname')|escape}" title="{#users_firstname#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="lastname">{help label='lastname'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {$user->get('lastname')|escape}
                  <input type="hidden" name="lastname" id="lastname" value="{$user->get('lastname')|escape}" title="{#users_lastname#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="code">{help label='code'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {$user->get('code')|escape}
                  <input type="hidden" name="code" id="code" value="{$user->get('code')|escape}" title="{#users_code#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="invoice_code">{help label='invoice_code'}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$user->get('invoice_code')|escape|default:'&nbsp;'}
                  <input type="hidden" name="invoice_code" id="invoice_code" value="{$user->get('invoice_code')|escape}" title="{#users_invoice_code#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="email">{help label='email'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {$user->get('email')|escape}
                  <input type="hidden" name="email" id="email" value="{$user->get('email')|escape}" title="{#users_email#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="auth">{help label='auth'}</label></td>
                <td class="required">{#required#}</td>
                <td nowrap="nowrap">
                  {if $user->get('auth') ne 'ldap'}{#users_auth_standard#}{else}{#users_auth_ldap#}{/if}
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="username">{help label='username'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  {$user->get('username')|escape}
                  <input type="hidden" name="username" id="username" value="{$user->get('username')|escape}" title="{#users_username#|escape}" />
                </td>
              </tr>
              <tr{if $user->get('auth') eq 'ldap'} style="display: none"{/if}>
                <td class="labelbox">{help label='password'}</td>
                <td class="required">{if $action eq 'add'}{#required#}{else}&nbsp;{/if}</td>
                <td>
                  ********
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="role">{help label='role'}</label></td>
                <td class="required">{#required#}</td>
                <td>
                  <a href="{$smarty.server.SCRIPT_NAME}?launch=roles&amp;roles=view&amp;view={$user->get('role')}" title="{#view#}: {$user->get('role_name')|escape}">{$user->get('role_name')|escape|default:"&nbsp;"}</a>
                  <input type="hidden" name="role" id="role" value="{$user->get('role')|escape}" title="{#users_role#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='permissions_origin'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $user->get('permissions_origin') eq 'self'}
                    {#users_self#}
                  {else}
                    {#users_role#}
                  {/if}
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_system_data_switch"><div class="switch_{if $smarty.cookies.user_system_data_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{#users_system_data#|escape}</div></td>
        </tr>
        <tr id="user_system_data"{if $smarty.cookies.user_system_data_box eq 'off'} style="display: none"{/if}>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox">{help label='employee'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $employee}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$user->get('employee')}" title="{#view#}: {$employee->get('name')|escape} {$employee->get('lastname')|escape}">
                    {if !$employee->isDeleted() && $employee->isActivated()}
                      {$employee->get('name')|escape} {$employee->get('lastname')|escape}
                    {else}
                      <span class="inactive_option" title="{#inactive_option#}">*{$employee->get('name')|escape} {$employee->get('lastname')|escape}</span>
                    {/if}
                    </a>
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='office'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>{$user->get('office_name')|escape|default:"&nbsp;"}</td>
              </tr>
              <tr>
                <td class="labelbox">{help label='working_hours'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>{if $user->get('working_hours') gt 0}{$user->get('working_hours')|@round:2} {#hours#}{else}&nbsp;{/if}</td>
              </tr>
              <tr>
                <td class="labelbox">{help label='customer'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $user->get('default_customer')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$user->get('default_customer')}" title="{#view#}: {$user->get('default_customer_name')|escape}">{$user->get('default_customer_name')|escape|default:"&nbsp;"}</a>
                    {if $user->get('default_branch')}
                      <span class="labelbox">{help label_content=$user->getBranchLabels('users_branch')|escape}</span>
                      {if !$user->get('default_branch_active')}
                        <span class="inactive_option" title="{#inactive_option#}"> *{$user->get('default_branch_name')|escape}</span>
                      {else}
                        {$user->get('default_branch_name')|escape}
                      {/if}
                    {/if}
                    {if $user->get('default_person')}
                      <span class="labelbox">{help label_content=#users_contact_person#|escape}</span>
                      {if !$user->get('default_person_active')}
                        <span class="inactive_option" title="{#inactive_option#}"> *{$user->get('default_person_name')|escape}</span>
                      {else}
                        {$user->get('default_person_name')|escape}
                      {/if}
                    {/if}
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label_content=#default_trademark# text_content=#help_default_trademark#}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $user->get('default_trademark')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$user->get('default_trademark')}" title="{#view#}: {$user->get('default_trademark_name')|escape}">{$user->get('default_trademark_name')|escape|default:"&nbsp;"}</a>
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='project'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $user->get('default_project')}
                    <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$user->get('default_project')}" title="{#view#}: {$user->get('project_name')|escape}">{$user->get('project_name')|escape|default:"&nbsp;"}</a>
                  {else}
                    &nbsp;
                  {/if}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_group'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>{$default_group}</td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_department'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>{$default_department}</td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_company'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$company|default:'&nbsp;'}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_cashbox'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$cashbox|default:'&nbsp;'}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_bank_account'}</td>
                <td>&nbsp;</td>
                <td>
                  {$bank_account|default:'&nbsp;'}
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='default_warehouse'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$warehouse|default:'&nbsp;'}
                </td>
              </tr>
{* the mapped drives currently are disabled
              <tr>
                <td class="labelbox"><label for="mapped_drives">{help label='mapped_drives'}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$user->get('mapped_drives')|escape|nl2br}
                  <input type="hidden" class="txtbox" name="mapped_drives" id="mapped_drives" value="{$user->get('mapped_drives')|escape}" />
                </td>
              </tr>
*}
            </table>
          </td>
        </tr>
        <tr>
          <td colspan="5" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_login_data_switch"><div class="switch_{if $smarty.cookies.user_login_data_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{#users_login_data#|escape}</div></td>
        </tr>
        <tr id="user_login_data"{if $smarty.cookies.user_login_data_box eq 'off'} style="display: none"{/if}>
          <td colspan="5" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox"><label for="last_login">{help label='last_login'}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {if $user->get('last_login') ne '0000-00-00 00:00:00'}{$user->get('last_login')|date_format:#date_mid#|escape}{else}{#users_no_login_yet#|escape}{/if}
                  <input type="hidden" name="last_login" id="last_login" value="{$user->get('last_login')}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox"><label for="remote_addr">{help label='remote_addr'}</label></td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$user->get('remote_addr')|default:#users_no_login_yet#|escape}
                  <input type="hidden" name="remote_addr" id="remote_addr" value="{$user->get('remote_addr')}" />
                </td>
              </tr>
            </table>
          </td>
        </tr>
{include file=`$templatesDir`_departments.html}
{include file=`$templatesDir`_groups.html}
        <tr>
          <td colspan="3" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="user_email_data_switch"><div class="switch_{if $smarty.cookies.user_login_data_box eq 'off'}expand{else}collapse{/if}"></div><div class="t_caption2_title">{#users_email_data#|escape}</div></td>
        </tr>
        <tr id="user_email_data"{if $smarty.cookies.user_email_data_box eq 'off'} style="display: none"{/if}>
          <td id="mail_content" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0" width="100%">
              <tr>
                <td class="labelbox">{help label='display_name'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>
                  {$user->get('display_name')|escape}
                  <input type="hidden" name="display_name" id="display_name" value="{$user->get('display_name')|escape}" title="{#users_display_name#|escape}" />
                </td>
              </tr>
              <tr>
                <td class="labelbox">{help label='signature'}</td>
                <td class="unrequired">&nbsp;</td>
                <td>&nbsp;</td>
              </tr>
              <tr>
                <td colspan="3">
                {$editor_content}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$user exclude='groups'}
</div>
