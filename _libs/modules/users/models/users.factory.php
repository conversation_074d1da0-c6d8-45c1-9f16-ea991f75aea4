<?php

require_once('users.model.php');

/**
 * Users model class
 */
Class Users extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'User';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param array $sql -
     * @return array - IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause (always sort the users by the flag is_portal)
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#u\.is_portal#', $sort)) {
                $sort = 'u.is_portal ASC, ' . $sort;
            }
            if (!preg_match('#u\.active#', $sort)) {
                $sort = 'u.active DESC, ' . $sort;
            }
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = 'ORDER BY u.active DESC, u.is_portal ASC, ui18n.firstname ASC, ui18n.lastname ASC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT (u.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                       '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n";

        if (preg_match('#oi18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'oi18n.name') {
            //relate to offices
            $sql['from'] .=  'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                             '  ON (u.office=oi18n.parent_id AND oi18n.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to user to fetch added by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (u.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to user to fetch modified by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (u.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#u\.is_portal#', $sort)) {
                $sort = 'u.is_portal ASC, ' . $sort;
            }
            if (!preg_match('#u\.active#', $sort)) {
                $sort = 'u.active DESC, ' . $sort;
            }
            $sort = 'ORDER BY ' . $sort;
        } else {
            $sort = 'ORDER BY u.active DESC, u.is_portal ASC, ui18n.firstname ASC, ui18n.lastname ASC';
        }

        //select clause
        $sql['select'] = 'SELECT u.*, ri18n.name as role_name, ui18n.*, oi18n.name as office_name, oi18n.parent_id as office_id, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ci18n.name, " ", ci18n.lastname) as employee_name, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_USERS . ' AS u' . "\n";
        //relate to user statistics
        if (!empty($filters['get_stats'])) {
            $sql['select'] .= ', us.*';
            $sql['from'] .= 'JOIN ' . DB_TABLE_USERS_STATS . ' AS us' . "\n" .
                            '  ON us.parent_id = u.id'. "\n";
        }
        $sql['from'] .='LEFT JOIN ' . DB_TABLE_ROLES_I18N . ' AS ri18n' . "\n" .
                       '  ON (u.role=ri18n.parent_id AND ri18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                       '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to employees to fetch the employee name
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (u.employee=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                        //relate to user to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (u.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to user to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (u.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //RELATE TO OFFICES
                       'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' AS oi18n' . "\n" .
                       '  ON (u.office=oi18n.parent_id AND oi18n.lang="' . $lang . '")' . "\n" .
                        //relate to user to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (u.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        // exception to take the default department name
        if ($registry->get('get_defaut_department_name')) {
            $sql['select'] .= ', depi18n.name as default_department_name' . "\n";
            $sql['from']   .= "\n" . 'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                                     '  ON (u.default_department=depi18n.parent_id AND depi18n.lang="' . $lang . '")' . "\n";
        }

        $sql['where'] = $where . "\n";

        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(u.id) AS total';
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            if ($registry['currentUser']) {
                $current_user_id = $registry['currentUser']->get('id');
            }
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                if (isset($current_user_id)) {
                    $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                }
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#u.deleted#', $where)) {
            $where .= ' AND u.deleted = 0';
        }
        if ($registry->get('module')
         && (!in_array($registry->get('module'),array('auth', 'users'))
         || in_array($registry->get('action'), array('filter', 'ajax_select')))
         && !preg_match('#u.hidden#', $where)) {
            $where .= ' AND u.hidden != 1';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     *
     * @param object $registry - the main registry
     * @param array $filters -
     * @param string $sessionParam -
     * @return array
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionParam = 'list_') {
        $sessionParam = strtolower($sessionParam . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     *
     * @param object $registry - the main registry
     * @return object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }


    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //do not allow change status of the logged user
        $where[] = 'id!=' . $registry['currentUser']->get('id');

        //exclude the readonly models
        $where[] = 'id!=' . PH_USERS_ADMIN;

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']      = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_USERS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //additional where
        //do not allow change status of the logged user
        $where[] = 'id!=' . $registry['currentUser']->get('id');

        //exclude the readonly models
        $where[] = 'id!=' . PH_USERS_ADMIN;

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_USERS, 'id', $where);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //do not allow change status of the logged user
        $where[] = 'id!=' . $registry['currentUser']->get('id');

        //exclude the readonly models
        $where[] = 'id!=' . PH_USERS_ADMIN;

        //multiple restore is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_USERS, 'id', $where);

        if (!$restored) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //additiona where
        //do not allow change status of the logged user
        $where[] = 'id!=' . $registry['currentUser']->get('id');

        //exclude the readonly models
        $where[] = 'id!=' . PH_USERS_ADMIN;

        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, DB_TABLE_USERS, 'id', $where);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }


    /**
     * Checks if certain user is in the same group or subgroup of the currently logged user
     *
     * @param object $registry - the main registry
     * @param string $group_id - group ID
     * @return bool - true - groups match, false - groups do not match
     */
    public static function checkUserGroup(&$registry, $group_id) {

        //no group to check
        if (!$group_id) {
            return true;
        }

        //no groups assigned
        if (!$registry['currentUser']->get('groups')) {
            return false;
        }

        if (in_array($group_id, $registry['currentUser']->get('groups'))) {
            return true;
        }

/*
        //check for the currently logged user
        $currentUserId = $registry['currentUser']->get('id');

        //get all group ids assigned to the user
        $query  = 'SELECT ug.group_id FROM ' . DB_TABLE_USERS_GROUPS . ' AS ug ' . "\n";
        $query .= 'WHERE ' . General::buildClause('ug.parent_id', $currentUserId) . "\n";
        $user_group_ids = $registry['db']->GetCol($query);



        //get all descendant group ids of the user groups
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $user_descendant_ids = array();
        foreach ($user_group_ids as $id) {
            $user_descendant_ids = array_merge($user_descendant_ids, Groups::getTreeDescendantsIds($registry, $id));
            //check if the current user is member of the descendant groups
            if (in_array($group_id, array_unique($user_descendant_ids))) {
                return true;
            }
        }
*/
        return false;
    }

    /**
     * Unlocks the locked records of the users
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the users
     * @return bool - result of the operations
     */
    public static function unlock(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        $where = array(General::buildClause('lock_by', $ids));

        $query = 'DELETE FROM ' . DB_TABLE_LOCKED_RECORDS . "\n" .
                 'WHERE ' . implode(' AND ', $where);
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Get users whom not to send emails with specified template
     *
     * @param object $registry - the main registry
     * @param string $template - name of email template
     * @param string $section - section in which the parameter is defined
     * @param string $value - value of personal setting
     * @return array - ids of users
     */
    public static function getUsersNoSend(&$registry, $template, $section = 'emails', $value = '0') {

        $query = 'SELECT es.user_id '.
                 'FROM ' . DB_TABLE_PERSONAL_SETTINGS . ' AS es '.
                 'WHERE es.name ="' . $template . '" AND value="' . $value . '" AND section="' . $section . '"' . "\n" .
                 'UNION' . "\n" .
                 'SELECT u.id FROM ' . DB_TABLE_USERS . ' u' . "\n" .
                 'WHERE u.active = 0';
        $records = $registry['db']->GetCol($query);

        return $records;
    }

    /**
     * Get users who have a certain value of a certain setting
     * in a certan section in their personal settings.
     *
     * @param object $registry - the main registry
     * @param string $param - parameter from the personal settings DB table
     * @param string $section - section in which the parameter is defined
     * @param string $value - value of personal setting
     * @return array - ids of users
     */
    public static function getUsersSendSettings(&$registry, $param, $section = 'emails', $value = 0) {

        $query = 'SELECT u.id '.
                 'FROM ' . DB_TABLE_USERS . ' AS u LEFT JOIN ' . DB_TABLE_PERSONAL_SETTINGS . ' AS ps ' .
                 '  ON (u.id=ps.user_id) ' .
                 'WHERE u.active = 1 AND ps.name ="' . $param . '" AND value!="' . $value . '" AND section="' . $section . '";';
        $records = $registry['db']->GetCol($query);

        return $records;
    }
}

?>
