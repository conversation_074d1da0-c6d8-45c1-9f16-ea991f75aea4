<?php

/**
 * Custom Import class to import information from
 * a spreadsheet file that contains data for payments
 */
Class Custom_Import extends Model_Factory {
    /**
     * Set the name of the model
     */
    public static $modelName = 'Import';

    /**
     * Set allowed file extensions
     */
    public static $allowed_file_extensions = array('xls', 'xlsx');

    /**
     * The data, extracted from the file and prepared for import
     */
    private static $_data = array();

    /**
     * Warning messages for each data row
     */
    private static $_warnings = array();

    /**
     * The registry
     */
    private static $_registry = array();

    /**
     * Simple function to add errors into the Messages object
     *
     * @param string $msg_key - name of label to use for translation
     * @param array|string $msg_params - optional values for replacement in label
     * @param string $key - key for error message
     * @param number $position - position for error message
     */
    static function error($msg_key, $msg_params = array(), $key = '', $position = 0) {
        if (!is_array($msg_params)) {
            $msg_params = array($msg_params);
        }
        self::$_registry['messages']->setError(
            vsprintf(self::$_registry['translater']->translate($msg_key), $msg_params),
            $key,
            $position
        );
    }

    /**
     * Make validation and prepare the data for import
     */
    static function validate() {
        // Check the file
        $file = &$_FILES['uploaded_file'];
        if (empty($file) || $file['error'] == 4) {
            self::error('error_aspajurlpayments_no_file_for_import');
        } elseif (!empty($file['error'])) {
            self::error('error_aspajurlpayments_file_upload');
        } elseif (!in_array(pathinfo($file['name'], PATHINFO_EXTENSION), self::$allowed_file_extensions)) {
            self::error('error_aspajurlpayments_invalid_file', array(pathinfo($file['name'], PATHINFO_EXTENSION), implode(', ', self::$allowed_file_extensions)));
        }

        // If there are any errors with the file
        if (self::$_registry['messages']->getErrors()) {
            // Validation fails
            return false;
        }

        // Build a spreadsheet reader
        $sr = new Spreadsheet_Manager($file['tmp_name'], array('take_only_data' => true));

        // Try to select the first sheet
        if ($sr->selectWorksheet(0)) {
            // Prepare the sheet parameters
            $first_col = 'A';
            $last_col  = 'AI';
            // Start from the first row (to get the columns labels, used for error messaging)
            $start_row = '1';

            // Get the data from the sheet
            $raw_data = $sr->readActiveCellsBetweenColumnsByRows($first_col, $last_col, $start_row, '', 'row');

            // Prepare to collect the columns labels from the file
            $header_labels = array();

            // Process the raw data
            foreach ($raw_data as $row => $data_row) {
                // Trim all cell values
                $data_row = array_map("trim", $data_row);

                // If this is the headers row
                if ($row == 1) {
                    // Just get the header labels
                    self::$_data['labels'] = array(
                        'operation_num'     => $data_row['B'],
                        'payment_date'      => $data_row['E'],
                        'bank_account_code' => $data_row['F'] . '/' . $data_row['H'],
                        'invoice_num'       => $data_row['Q'],
                        'payment_sum'       => $data_row['Y']
                    );
                    // And move to the next row
                    continue;
                }

                // If the data row is empty
                if (count(array_filter($data_row)) == 0) {
                    // There are no more rows to import so exit
                    return true;
                }

                /**
                 * Parse a data row
                 */
                // Get: operation_num
                if ($data_row['B'] == '') {
                    continue;
                } else {
                    self::$_data['operations_nums'][] = self::$_data['rows'][$row]['operation_num'] = $data_row['B'];
                }

                // Get: payment_date
                preg_match('/(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $data_row['E'], $matches);
                self::$_data['rows'][$row]['payment_date'] = (!isset($matches[1]) || !isset($matches[2]) || !isset($matches[3]) ? '' : sprintf('%s-%s-%s', $matches[3], $matches[2], $matches[1]));

                // Get: bank_account_code
                self::$_data['bank_account_codes'][] = self::$_data['rows'][$row]['bank_account_code'] = $data_row['F'] . stristr($data_row['H'], ';', true);

                // Get: invoice_num
                preg_match('/;\s*(\d*)\s*;[^;]*$/', $data_row['Q'], $matches);
                self::$_data['invoices_nums'][] = self::$_data['rows'][$row]['invoice_num'] = (isset($matches[1]) ? $matches[1] : '');

                // Get: payment_sum
                self::$_data['rows'][$row]['payment_sum'] = str_replace(',', '.', $data_row['Y']);
            }
        } else {
            // Error: can't select worksheet
            self::error('error_aspajurlpayments_select_worksheet');
            return false;
        }

        return true;
    }

    /**
     * Function to do the import of the file
     *
     * @param object $registry - the registry
     * @return boolean - result of the operation
     */
    public static function import(&$registry) {
        // Extend server usage
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        // Prepare the registry for local usage
        self::$_registry = &$registry;

        // Make validation and prepare the data for import
        $is_valid = self::validate();

        // Default result: success
        $result = true;

        // Prepare to collect log data for the import
        $log = array();

        // Prepare the messages object
        $messages = &$registry['messages'];

        // Prepare the translater object
        $translater = &$registry['translater'];

        // If the file is valid
        if ($is_valid) {
            if (!empty(self::$_data['rows'])) {
                // Get the database object
                $db = $registry['db'];

                // Ignore permissions of layouts
                // TODO: check if this is necessary
                $registry->set('edit_all', true, true);

                // Include required files
                // TODO: check if all files are required
                require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
                require_once PH_MODULES_DIR . 'finance/models/finance.payments.audit.php';
//                 $translater->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance.ini', 'custom');
//                 $translater->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_incomes_reasons.ini', 'custom');
//                 $translater->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $registry['lang'] . '/finance_payments.ini', 'custom');
                require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php');

                // Get the invoices
                $filters = array(
                    'where' => array(
                        'fir.type        = ' . PH_FINANCE_TYPE_INVOICE,
                        'fir.num         IN (\'' . implode('\', \'', self::$_data['invoices_nums']) . '\')',
                        'fir.annulled_by = 0',
                        'fir.status      = \'finished\'',
                    )
                );
                $invoices_arr = Finance_Incomes_Reasons::search($registry, $filters);
                $invoices = array();
                foreach ($invoices_arr as $i) {
                    $invoices[$i->get('num')] = $i;
                }

                // Get bank accounts data
                $query = "
                    SELECT code                                    AS code,
                        CONCAT(company, '_', office, '_bank_', id) AS company_data
                      FROM " . DB_TABLE_FINANCE_BANK_ACCOUNTS . "
                      WHERE code       IN ('" . implode("', '", self::$_data['bank_account_codes']) . "')
                        AND active     = 1
                        AND deleted_by = 0";
                $banks_company_data = $db->GetAssoc($query);

                // Get payments operations
                // IMPORTANT: operation numbers are unique within a month, starting from 1 each month
                //            the import stores the operation number as is in the spreadsheet file
                //            the check for already imported payment is by YEAR-MONTH-OPERATION_NUMBER
                $query = "
                    SELECT CONCAT(DATE_FORMAT(fp.issue_date, '%Y%m'), fpi.note), fpi.parent_id
                      FROM " . DB_TABLE_FINANCE_PAYMENTS . " AS fp
                      JOIN " . DB_TABLE_FINANCE_PAYMENTS_I18N . " AS fpi
                        ON (fp.type          = 'BP'
                          AND fp.annulled_by = 0
                          AND fpi.parent_id  = fp.id
                          AND fpi.lang       = '{$registry['lang']}'
                          AND fpi.note       IN ('" . implode("', '", self::$_data['operations_nums']) . "'))";
                $payments_operations = $db->GetAssoc($query);

                // Get the labels of the columns from the file
                $labels = self::$_data['labels'];

                // Go through each row
                foreach (self::$_data['rows'] as $row => $data) {
                    // Check payment date
                    if ($data['payment_date'] == '') {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_payment_date'), $labels['payment_date']);
                    }

                    // Check bank account code
                    if ($data['bank_account_code'] == '') {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_bank_account_code'), $labels['bank_account_code']);
                    }

                    // Check invoice
                    if ($data['invoice_num'] == '') {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_invoice_num'), $labels['invoice_num']);
                    } else if (!isset($invoices[$data['invoice_num']])) {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_invoice'), $data['invoice_num']);
                    } else {
                        $invoice_payment_status = $invoices[$data['invoice_num']]->get('payment_status');
                        if ($invoice_payment_status == 'paid') {
                            self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_invoice_paid'), $data['invoice_num']);
                        } else if ($invoice_payment_status == 'nopay') {
                            self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_invoice_nopay'), $data['invoice_num']);
                        } else {
                            $invoice = $invoices[$data['invoice_num']];
                        }
                    }

                    //check container (bank account)
                    $bank_company_data = $banks_company_data[$data['bank_account_code']];
                    if (empty($bank_company_data) || !preg_match('#^(\d+)_(\d+)_(cash|bank)_(\d+)$#', $bank_company_data, $matches)) {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_container'), $data['bank_account_code'], $labels['bank_account_code']);
                    } else {
                        $office = @$matches[2];
                        if ($office == 0 && $invoice) {
                            $office = $invoice->get('office');
                        }
                        $bank_company_data = sprintf('%d_%d_%s_%d', @$matches[1], $office, @$matches[3], @$matches[4]);
                    }

                    // Check payment sum
                    if (empty($data['payment_sum'])) {
                        self::$_warnings[$row][] = sprintf($translater->translate('warning_aspajurlpayments_no_payment_sum'), $labels['payment_sum']);
                    }

                    // If this payment is already imported
                    if (isset($payments_operations[General::strftime('%Y%m', $data['payment_date']) . $data['operation_num']])) {
                        // Log it
                        $log['payment_operations_already_imported'][$data['operation_num']] = $data['operation_num'];
                    }

                    // If there are warnings for this row or the current payment is already imported
                    if (!empty(self::$_warnings[$row]) || isset($log['payment_operations_already_imported'][$data['operation_num']])) {
                        // Log this unsuccessfully added payment
                        $log['payment_operations_failed'][] = $data['operation_num'];

                        // Skip this row
                        continue;
                    }

                    // Prepare the invoice for adding a payment
                    $invoice->set('payment_reason', sprintf($translater->translate('aspajurlpayments_payment_reason'), $data['operation_num']), true);
                    $invoice->set('payment_container', $bank_company_data, true);
                    $invoice->set('payment_date', $data['payment_date'], true);
                    $invoice->set('payment_sum', $data['payment_sum'], true);
                    $invoice->set('container_rate', 1, true);
                    $invoice->set('container_currency', $invoice->get('currency'), true);
                    // The payment currency and the bank account currency are equal,
                    // so the container amount (i.e. the payment sum converted into the currency of the bank ackount)
                    // is the same as the payment sum
                    $invoice->set('container_amount', $data['payment_sum'], true);

                    // Unsanitize the invoice
                    $invoice->unsanitize();

                    // Try to add payment (i.e. import payment)
                    $payment_added = true;
                    if ($payment_id = $invoice->addPayment()) {
                        // Write history
                        Finance_Incomes_Reasons_History::saveData(
                            $registry,
                            array(
                                'action_type' => 'addpayment',
                                'new_model'   => $invoice,
                                'old_model'   => $invoice
                             )
                        );

                        // Save the Ajur L operation num into the payment
                        // (this will be used to check if the payment is already imported once)
                        $query = "
                            UPDATE " . DB_TABLE_FINANCE_PAYMENTS_I18N . "
                              SET note = '{$data['operation_num']}'
                              WHERE parent_id = {$payment_id}
                                AND lang      = '{$registry['lang']}'";
                        $db->Execute($query);

                        // Log successfully added payments
                        $log['payment_operations_success'][$invoice->get('id')][] = $payment_id;
                    } else {
                        // Set that the adding of the payment failed
                        $payment_added = false;

                        // Set warning
                        self::$_warnings[$row][] = $translater->translate('warning_aspajurlpayments_addpayment_failed');

                        // Log this unsuccessfully added payment
                        $log['payment_operations_failed'][] = $data['operation_num'];
                    }

                    // Unsanitize the invoice
                    $invoice->sanitize();

                    // Clear all standard messages
                    $messages->flush();
                }

                // If there are successfull imports and failed imports
                if (!empty($log['payment_operations_success']) && !empty($log['payment_operations_failed'])) {
                    // Set message: partial import
                    $messages->setMessage($translater->translate('message_aspajurlpayments_import_success_partial'));
                } else if (!empty($log['payment_operations_success'])) {
                    // If there are only successfull imports
                    // Set message: import successfull
                    $messages->setMessage($translater->translate('message_aspajurlpayments_import_success'));
                } else {
                    // Set warning: no data to import
                    $messages->setWarning($translater->translate('warning_aspajurlpayments_no_data_prepared_to_import'));
                    if (!empty($log['payment_operations_already_imported'])) {
                        if (count($log['payment_operations_already_imported']) == count(self::$_data['rows'])) {
                            $messages->setWarning($translater->translate('warning_aspajurlpayments_already_imported_all'));
                        } else {
                            $messages->setWarning($translater->translate('warning_aspajurlpayments_already_imported_part'));
                        }
                    }
                }

                // Format log data for successfull payments
                if (!empty($log['payment_operations_success'])) {
                    $log_invoices_payments_success = $log['payment_operations_success'];
                    $log['payment_operations_success'] = array();
                    foreach ($log_invoices_payments_success as $invoice_id => $invoice_payments) {
                        $log['payment_operations_success'][] = sprintf('(invoice:%d|payments:%s)', $invoice_id, implode(',', $invoice_payments));
                    }
                }

                // Add all collected warnings by rows
                if (!empty(self::$_warnings)) {
                    foreach (self::$_warnings as $row => $warnings) {
                        $messages->setWarning(sprintf($translater->translate('warning_aspajurlpayments_row'), $row, self::$_data['rows'][$row]['operation_num']));
                        foreach ($warnings as $warning) {
                            $messages->setWarning($warning);
                        }
                    }
                }
            } else {
                // Warning: no data to import
                $messages->setWarning($translater->translate('warning_aspajurlpayments_no_data_to_import'));
            }
        } else {
            // Error: import failed
            self::error('error_aspajurlpayments_import_failed', '', '', -1);
        }

        // If there are any errors
        if ($errors = $messages->getErrors()) {
            // Set the result of the import to: false
            $result = false;

            // Log the errors
            if (!isset($log['errors'])) {
                $log['errors'] = array();
            }
            $log['errors'] = array_merge($log['errors'], $errors);
        }

        // If there are any warnings
        if ($warnings = $messages->getWarnings()) {
            // Log the warnings
            if (!isset($log['warnings'])) {
                $log['warnings'] = array();
            }
            $log['warnings'] = array_merge($log['warnings'], $warnings);
        }

        // If there are any messages
        if ($msgs = $messages->getMessages()) {
            // Log the messages
            if (!isset($log['messages'])) {
                $log['messages'] = array();
            }
            $log['messages'] = array_merge($log['messages'], $msgs);
        }

        // Log the import
        $logdata = array(
            'import_type' => 'asp_ajurl_payments',
            'file'        => (!empty($_FILES['uploaded_file']) ? $_FILES['uploaded_file'] : ''),
            'success'     => intval($result),
            'log'         => $log
        );
        Imports::importLog($registry, $logdata);

        return $result;
    }
}

?>
