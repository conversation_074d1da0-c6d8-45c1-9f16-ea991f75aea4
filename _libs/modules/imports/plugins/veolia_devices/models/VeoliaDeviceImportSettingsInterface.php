<?php
interface VeoliaDeviceImportSettingsInterface
{


    public function searchNomenclature(&$registry, $rowData);
    public function getStartCol() : string;
    public function getLastCol() : string;
    public function getStartRow() : string;
    public function getHeaderColumns() : array;
    public function getDateFormat() : string;
    public function getDateColumn() : string;
    public function getFillMapping() : array;
    public function hasFilterMethod() : bool;
    public function filterData($rawData);
    public function getHeaderRow();

}
