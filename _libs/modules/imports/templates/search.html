<h1>{$title}</h1>
<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=imports&amp;imports=search&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  hide_rpp=true
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border t_sortable {$sort.type.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.type.link}">{#imports_name#|escape}</div></td>
          <td class="t_caption t_border t_sortable {$sort.file.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.file.link}">{#imports_upload_file#|escape}</div></td>
          <td class="t_caption t_border t_sortable {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{#import_log_status#|escape}</div></td>
          <td class="t_caption t_border t_sortable {$sort.log_message.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.log_message.link}">{#import_log_messages#|escape}</div></td>
          <td class="t_caption t_border t_sortable {$sort.user.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.user.link}">{#import_log_user#|escape}</div></td>
          <td class="t_caption t_sortable {$sort.date.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.date.link}">{#import_log_date#|escape}</div></td>
        </tr>

      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$log item='l'}
        <tr class="{cycle values='t_odd,t_even'} {if $l->get('success')}green{else}red{/if}">
          <td class="t_border hright">
            {if $l->get('files_count')}
              <a href="#">
                <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                  onmouseover="showFiles(this, 'imports', 'imports', {$l->get('id')})"
                  onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.type.isSorted}">{$l->get('name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.file.isSorted}">{$l->get('file_name')|escape|default:'&nbsp;'}</td>
          <td class="t_border {$sort.status.isSorted}">{if $l->get('success')}{#import_log_ok#|escape}{else}{#import_log_failed#|escape}{/if}</td>
          <td class="t_border t_word_wrap {$sort.log_message.isSorted}">{$l->get('log')|regex_replace:'#(?<=\S),(?=[^\s\d])#':"\n"|nl2br}</td>
          <td class="t_border {$sort.user.isSorted}">{$l->get('added_by_name')|escape}</td>
          <td class="{$sort.date.isSorted}">{$l->get('added')|date_format:#date_mid#}</td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="8">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="8"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html session_param=$session_param|default:$pagination.session_param exclude='multiedit'}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  hide_rpp=true
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
