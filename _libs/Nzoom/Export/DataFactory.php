<?php

namespace Nzoom\Export;

use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Provider\ExportTableProviderInterface;

/**
 * Factory for creating ExportData instances from models and database queries
 *
 * Supports both traditional array-based data creation and streaming data creation
 * for memory-efficient processing of large datasets. Can optionally include
 * related table data through configurable table providers.
 */
class DataFactory
{
    /**
     * Application registry
     */
    private \Registry $registry;

    /**
     * Number of models to process in each chunk
     */
    private int $chunkSize = 50;

    /**
     * Table provider for extracting related tables from models
     */
    private ?ExportTableProviderInterface $tableProvider = null;

    /**
     * Reference column configuration (set when withModelTableProvider is called)
     */
    private ?array $referenceColumn = null;

    /**
     * Create a new data factory instance
     *
     * @param \Registry $registry Application registry
     */
    public function __construct(\Registry $registry)
    {
        $this->registry = $registry;
    }

    /**
     * Set table provider for extracting related tables from models
     *
     * @param ExportTableProviderInterface|null $tableProvider Table provider instance or null to disable
     */
    public function setTableProvider(?ExportTableProviderInterface $tableProvider): void
    {
        $this->tableProvider = $tableProvider;
    }

    /**
     * Check if table extraction is enabled
     *
     * @return bool True if table provider is configured
     */
    public function isTablesEnabled(): bool
    {
        return $this->tableProvider !== null;
    }

    /**
     * Configure ModelTableProvider with reference column for table exports
     *
     * @param string $referenceColumnName Reference column field name (e.g., 'full_num', 'code')
     * @param string $referenceColumnLabel Reference column display label (e.g., 'Full Num', 'Code')
     * @param array $options Additional provider options (include_empty_tables, date_format, etc.)
     * @return self For method chaining
     */
    public function withModelTableProvider(string $referenceColumnName, string $referenceColumnLabel, array $options = []): self
    {
        $provider = new \Nzoom\Export\Provider\ModelTableProvider($this->registry, $referenceColumnName, $referenceColumnLabel, $options);

        // Store reference column info for use in createRecordFromModel
        $this->referenceColumn = [
            'name' => $referenceColumnName,
            'label' => $referenceColumnLabel
        ];

        $this->setTableProvider($provider);
        return $this;
    }

    /**
     * Create ExportData instance from array of models (traditional approach)
     *
     * @param \Model[] $models Array of model instances to export
     * @param \Outlook $outlook Database query configuration
     * @return ExportData Complete export data with all records loaded
     */
    public function __invoke(array $models, \Outlook $outlook): ExportData
    {
        // Extract model type from first model if available
        $modelType = 'Export';
        if (!empty($models)) {
            $firstModel = reset($models);
            $modelType = $this->extractModelTypeFromModel($firstModel);
        }

        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with model type and header
        $exportData = new ExportData($modelType, $header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);

        // Process models in chunks for memory efficiency
        $chunks = array_chunk($models, $this->chunkSize);
        foreach ($chunks as $chunk) {
            $this->processModelsChunk($chunk, $exportData, $header);
        }

        return $exportData;
    }

    /**
     * Create header from outlook
     *
     * @param \Outlook $outlook The outlook
     * @return ExportHeader The created header
     */
    private function createHeaderFromOutlook(\Outlook $outlook): ExportHeader
    {
        $header = new ExportHeader('f0f0f0', ['font-weight' => 'bold']);
        $fields = $outlook->get('current_custom_fields');

        if (!empty($fields)) {
            foreach ($fields as $field) {
                // Skip fields that explicitly have position set to 0 or false
                if (isset($field['position']) && !$field['position']) {
                    // position explicitly set to 0 or false means not visible
                    continue;
                }

                try {
                    $type = $this->mapFieldTypeToExportType($field['field_type'] ?? 'text');
                    $width = !empty($field['column_width']) ? (int)$field['column_width'] : null;

                    $header->addColumn(new ExportColumn(
                        $field['name'],
                        $field['label'],
                        $type,
                        '',
                        $width
                    ));
                } catch (\InvalidArgumentException $e) {
                    // Skip unsupported field types
                    continue;
                }
            }
        }

        return $header;
    }

    /**
     * Process a chunk of models
     *
     * @param \Model[] $models Array of models
     * @param ExportData $exportData The export data to add records to
     * @param ExportHeader $header The header to validate against
     */
    private function processModelsChunk(array $models, ExportData $exportData, ExportHeader $header): void
    {
        foreach ($models as $model) {
            $exportData->addRecord($this->createRecordFromModel($model, $header), false);
        }
    }

    /**
     * Create a record from a model
     *
     * @param \Model $model The model
     * @param ExportHeader $header The header to get columns from
     * @return ExportRecord The created record
     */
    private function createRecordFromModel(\Model $model, ExportHeader $header): ExportRecord
    {
        $metadata = [
            'id' => $model->get('id')
        ];

        // Store reference column value separately in metadata (only when ModelTableProvider is used)
        if ($this->referenceColumn) {
            $referenceValue = $this->getModelReferenceValue($model);
            if ($referenceValue !== null) {
                $metadata[$this->referenceColumn['name']] = $referenceValue;
            }
        }

        $record = new ExportRecord($metadata);

        foreach ($header as $column) {
            $varName = $column->getVarName();

            // Use the new method to get the value with arguments
            $value = $model->getExportVarValueWithArgs($varName);

            // Get type and format from the column (from outlook configuration)
            $type = $column->getType();
            $format = $column->getFormat();

            // Try to get more specific type information from the model
            if (method_exists($model, 'getExportVarType')) {
                $modelType = $model->getExportVarType($varName);
                if (!empty($modelType)) {
                    $type = $modelType;
                }
            }

            $record->addValue($varName, $value, $type, $format);
        }

        // Extract tables if table provider is configured
        if ($this->tableProvider && $this->isTablesEnabled()) {
            $this->extractTablesForRecord($record, $model);
        }

        return $record;
    }

    /**
     * Get reference column value from a model using various methods
     *
     * @param \Model $model The model to get reference value from
     * @return string|null The reference value or null if not available
     */
    private function getModelReferenceValue(\Model $model): ?string
    {
        if (!$this->referenceColumn) {
            return null;
        }

        $referenceColumnName = $this->referenceColumn['name'];

        // Try direct field access first
        $value = $model->get($referenceColumnName);
        if (!empty($value)) {
            return $value;
        }

        // Try model-specific methods if direct access fails
        $methodName = 'get' . ucfirst($referenceColumnName);
        if (method_exists($model, $methodName)) {
            $value = $model->$methodName();
            if (!empty($value)) {
                return $value;
            }
        }

        return null;
    }

    /**
     * Extract tables for a record using the configured table provider
     *
     * @param ExportRecord $record The export record to add tables to
     * @param \Model $model The source model
     */
    private function extractTablesForRecord(ExportRecord $record, \Model $model): void
    {
        try {
            $tableCollection = $this->tableProvider->getTablesForRecord($model);

            if ($tableCollection && $tableCollection->hasTables()) {
                $record->setTableCollection($tableCollection);
            }
        } catch (\Exception $e) {
            // Log error but don't fail the export
            \General::log($this->registry, 'export_table_extraction_warning',
                'Failed to extract tables for model ID ' . $model->get('id') . ': ' . $e->getMessage());
        }
    }

    /**
     * Map field type to export value type
     *
     * @param string $fieldType The field type
     * @return string The export value type
     * @throws \InvalidArgumentException If the field type is not supported
     */
    private function mapFieldTypeToExportType(string $fieldType): string
    {
        $typeMap = [
            'text' => ExportValue::TYPE_STRING,
            'textarea' => ExportValue::TYPE_STRING,
            'dropdown' => ExportValue::TYPE_STRING,
            'radio' => ExportValue::TYPE_STRING,
            'checkbox_group' => ExportValue::TYPE_STRING,
            'date' => ExportValue::TYPE_DATE,
            'datetime' => ExportValue::TYPE_DATETIME,
            'autocompleter' => ExportValue::TYPE_STRING,
            'time' => ExportValue::TYPE_STRING,
            '' => ExportValue::TYPE_STRING,
        ];

        if (!isset($typeMap[$fieldType])) {
            throw new \InvalidArgumentException(sprintf(
                'Unsupported field type "%s". Supported types are: %s',
                $fieldType,
                implode(', ', array_keys($typeMap))
            ));
        }

        return $typeMap[$fieldType];
    }

    /**
     * Set the chunk size for model processing
     *
     * @param int $chunkSize Number of models to process per chunk
     */
    public function setChunkSize(int $chunkSize): void
    {
        $this->chunkSize = $chunkSize;
    }

    /**
     * Create streaming ExportData instance that loads records lazily from database
     *
     * @param string $factoryClass Factory class name for database queries (e.g., 'Documents', 'Finance_Incomes')
     * @param array $filters Database filters for the model search
     * @param \Outlook $outlook Database query configuration
     * @param int $pageSize Number of records to load per page (default: 200)
     * @return ExportData Streaming export data instance
     */
    public function createStreaming(string $factoryClass, array $filters, \Outlook $outlook, int $pageSize = 200): ExportData
    {
        // Extract model type from factory class name
        $modelType = $this->extractModelTypeFromFactoryClass($factoryClass);

        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with model type and header
        $exportData = new ExportData($modelType, $header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);

        // Create record provider function
        $recordProvider = function (int $offset, int $limit) use ($factoryClass, $filters, $header) {
            // Add pagination to filters
            $paginatedFilters = array_merge($filters, [
                'limit' => $limit,
                'offset' => $offset
            ]);

            // Get models for this page
            $models = $factoryClass::search($this->registry, $paginatedFilters);

            // Convert models to export records
            $records = [];
            foreach ($models as $model) {
                $records[] = $this->createRecordFromModel($model, $header);
            }

            return $records;
        };

        // Configure lazy loading
        $exportData->setRecordProvider($recordProvider, $pageSize);

        return $exportData;
    }

    /**
     * Create cursor-based streaming ExportData for optimal performance with very large datasets
     *
     * @param string $factoryClass Factory class name for database queries
     * @param array $filters Database filters for the model search
     * @param \Outlook $outlook Database query configuration
     * @param int $pageSize Number of records to load per page (default: 1000)
     * @param string $cursorField Field to use for cursor pagination (default: 'id')
     * @return ExportData Streaming export data instance with cursor-based pagination
     */
    public function createCursorStreaming(string $factoryClass, array $filters, \Outlook $outlook, int $pageSize = 1000, string $cursorField = 'id'): ExportData
    {
        // Extract model type from factory class name
        $modelType = $this->extractModelTypeFromFactoryClass($factoryClass);

        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create export data with model type and header
        $exportData = new ExportData($modelType, $header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
        ]);

        // Track the last cursor value
        $lastCursorValue = 0;

        // Create cursor-based record provider function
        $recordProvider = function (int $offset, int $limit) use ($factoryClass, $filters, $header, $cursorField, &$lastCursorValue) {
            // For cursor pagination, we ignore offset and use the cursor field
            $cursorFilters = $filters;

            // Add cursor condition
            if ($lastCursorValue > 0) {
                $whereConditions = $cursorFilters['where'] ?? [];
                $whereConditions[] = "{$cursorField} > {$lastCursorValue}";
                $cursorFilters['where'] = $whereConditions;
            }

            // Add limit and ordering
            $cursorFilters['limit'] = $limit;
            $cursorFilters['order'] = "{$cursorField} ASC";

            // Get models for this page
            $models = $factoryClass::search($this->registry, $cursorFilters);

            if (empty($models)) {
                return [];
            }

            // Update cursor for next iteration
            $lastModel = end($models);
            $lastCursorValue = $lastModel->get($cursorField);

            // Convert models to export records
            $records = [];
            foreach ($models as $model) {
                $records[] = $this->createRecordFromModel($model, $header);
            }

            return $records;
        };

        // Configure lazy loading
        $exportData->setRecordProvider($recordProvider, $pageSize);

        return $exportData;
    }

    /**
     * Extract model type from factory class name
     * Examples: Documents -> Documents, Finance_Incomes_Reasons -> Finance_Incomes_Reasons
     *
     * @param string $factoryClass The factory class name
     * @return string The extracted model type
     */
    private function extractModelTypeFromFactoryClass(string $factoryClass): string
    {
        // Remove leading backslash if present
        $factoryClass = ltrim($factoryClass, '\\');

        // Return the class name as-is since it represents the model type
        return $factoryClass;
    }

    /**
     * Extract model type from a model instance
     *
     * @param \Model $model The model instance
     * @return string The extracted model type
     */
    private function extractModelTypeFromModel(\Model $model): string
    {
        // Get the model class name
        $modelClass = get_class($model);

        // Remove leading backslash if present
        $modelClass = ltrim($modelClass, '\\');

        // For model instances, we need to convert from model name to factory name
        // Example: Document -> Documents, Finance_Incomes_Reason -> Finance_Incomes_Reasons
        // This follows the pattern where factory names are typically plural

        // Try to determine the factory class name from the model
        // This is a simplified approach - in practice, you might need more sophisticated logic
        if (method_exists($model, 'getFactoryClass')) {
            return $this->extractModelTypeFromFactoryClass($model->getFactoryClass());
        }

        // Fallback: use the model class name directly
        return $modelClass;
    }

    /**
     * Create empty ExportData with headers only (template mode)
     *
     * @param string $modelType Model type for the export
     * @param \Outlook $outlook Database query configuration
     * @return ExportData Empty export data with headers only
     */
    public function createEmpty(string $modelType, \Outlook $outlook): ExportData
    {
        // Create header from outlook
        $header = $this->createHeaderFromOutlook($outlook);

        // Create empty export data with just the header
        $exportData = new ExportData($modelType, $header, [
            'generated_at' => new \DateTime(),
            'generated_by' => $this->registry['currentUser']->get('id'),
            'template_mode' => true
        ]);

        return $exportData;
    }
}
