# nZoom Export System

A modern, extensible export system built with the Adapter Pattern for handling various export formats with streaming support and advanced features.

## Overview

The export system provides a flexible, high-performance solution for exporting data in multiple formats. It features:

- **🎯 Adapter Pattern**: Clean separation of concerns with dedicated adapters for each format
- **🚀 Streaming Support**: Memory-efficient processing of large datasets
- **⚙️ Advanced Configuration**: Format-specific options and constraints
- **📊 Multiple Formats**: Excel (XLSX/XLS), CSV, JSON with extensible architecture
- **🔧 Developer Friendly**: Comprehensive API with examples and documentation
- **🔄 Backward Compatible**: Existing code continues to work seamlessly

## Architecture

### Core Components

1. **ExportService**: Main service class for coordinating exports
2. **ExportFormatAdapterInterface**: Contract for all export format adapters
3. **AbstractExportFormatAdapter**: Base class with common functionality
4. **ExportFormatFactory**: Factory for creating and managing adapters
5. **Data Entities**: ExportData, ExportRecord, ExportValue for structured data
6. **Streaming Components**: FileStreamer, PointerFileStreamer, GeneratorFileStreamer

### File Structure

```
_libs/Nzoom/Export/
├── Adapter/
│   ├── ExportFormatAdapterInterface.php
│   ├── AbstractExportFormatAdapter.php
│   ├── ExcelExportFormatAdapter.php
│   ├── CsvExportFormatAdapter.php
│   └── JsonExportFormatAdapter.php
├── Entity/
│   ├── ExportData.php
│   ├── ExportRecord.php
│   ├── ExportValue.php
│   ├── ExportHeader.php
│   └── ExportColumn.php
├── Streamer/
│   ├── FileStreamer.php
│   ├── PointerFileStreamer.php
│   ├── GeneratorFileStreamer.php
│   └── StreamHeaders.php
├── Factory/
│   └── ExportFormatFactory.php
├── Provider/
│   ├── ExportTableProviderInterface.php
│   └── ModelTableProvider.php
├── Examples/
│   ├── ExampleRunner.php
│   ├── QuickTest.php
│   ├── ExcelConstrainedSizingExample.php
│   ├── StreamingUsage.php
│   ├── TableExportUsage.php
│   └── TableFilteringExample.php
├── docs/
│   ├── README.md
│   ├── table-export.md
│   ├── model-table-provider.md
│   ├── excel-constrained-sizing.md
│   └── testing.md
├── ExportService.php
├── DataFactory.php
└── ExportActionFactory.php
```

## Quick Start

### Basic Export

```php
use Nzoom\Export\ExportService;

// Create export service
$exportService = new ExportService($registry, 'customers', 'customers', 'xlsx');

// Create export data from database
$outlook = \Outlooks::getOutlook($registry, false, 0, $userId, $roleId, 'customers', 'customers');
$filters = ['where' => ['status = "active"']];
$exportData = $exportService->createExportData($outlook, $filters, 'Customers');

// Export to browser
$exportService->export('customers.xlsx', $exportData);
```

### Advanced Usage with Options

```php
// Excel export with constrained sizing
$exportService = new ExportService($registry, 'invoices', 'invoices', 'xlsx');

$exportData = $exportService->createExportData($outlook, $filters, 'Finance_Incomes');

$options = [
    'max_column_width' => 40.0,  // Prevent extremely wide columns
    'max_row_height' => 80.0,    // Limit row height
    'chunk_size' => 50         // Process in chunks for memory efficiency
];

$exportService->export('invoices.xlsx', $exportData, $options);
```

### CSV Export with Custom Delimiter

```php
$exportService = new ExportService($registry, 'products', 'products', 'csv');

$exportData = $exportService->createExportData($outlook, $filters, 'Products');

$options = [
    'delimiter' => 'semicolon'  // Use semicolon instead of comma
];

$exportService->export('products.csv', $exportData, $options);
```

### Using the Factory Pattern

```php
use Nzoom\Export\Factory\ExportFormatFactory;

$factory = new ExportFormatFactory($registry, 'orders', 'orders');

// Create Excel adapter with specific configuration
$excelAdapter = $factory->createAdapter('xlsx', [
    'max_column_width' => 50.0,
    'chunk_size' => 50
]);

// Export directly using adapter
$excelAdapter->export('php://output', 'xlsx', $exportData, $options);
```

### Checking Supported Formats

```php
$exportService = new ExportService($registry, 'reports', 'reports');

// Get all supported formats
$formats = $exportService->getSupportedFormats();
// Returns: ['xlsx', 'xls', 'csv', 'json']

// Check if specific format is supported
if ($exportService->isFormatSupported('xlsx')) {
    echo "Excel format is available";
}
```

## Available Export Formats

### Excel (XLSX/XLS) - ExcelExportFormatAdapter

**Supported Extensions**: `xlsx`, `xls`

**Advanced Features**:
- ✅ **Constrained Sizing**: Prevents extremely wide columns and tall rows
- ✅ **Memory Optimization**: Chunked processing for large datasets
- ✅ **Cell Formatting**: Automatic formatting based on data types (text, numbers, dates)
- ✅ **Named Ranges**: Excel-compatible named ranges for columns
- ✅ **Auto-sizing**: Intelligent column width calculation with constraints
- ✅ **Progress Tracking**: Built-in logging for large export operations

**Configuration Options**:
```php
$options = [
    'max_column_width' => 50.0,    // Maximum column width (10.0-255.0)
    'max_row_height' => 100.0,     // Maximum row height (15.0-500.0)
    'chunk_size' => 50,          // Records per processing chunk
    'extension' => 'xlsx'          // File format (xlsx/xls)
];
```

### CSV - CsvExportFormatAdapter

**Supported Extensions**: `csv`

**Features**:
- ✅ **Flexible Delimiters**: Comma, semicolon, tab support
- ✅ **Proper Escaping**: RFC 4180 compliant CSV formatting
- ✅ **UTF-8 Support**: Full Unicode character support
- ✅ **Streaming**: Memory-efficient processing

**Configuration Options**:
```php
$options = [
    'delimiter' => 'comma'  // 'comma', 'semicolon', 'tab'
];
```

### JSON - JsonExportFormatAdapter

**Supported Extensions**: `json`

**Features**:
- ✅ **Pretty Printing**: Human-readable JSON formatting
- ✅ **UTF-8 Support**: Full Unicode character support
- ✅ **Streaming**: Memory-efficient processing
- ✅ **Structured Data**: Maintains data types and relationships

**Configuration Options**:
```php
$options = [
    'pretty_print' => true  // Enable/disable pretty printing
];
```

## Extending the System

### Creating a Custom Adapter

1. Implement `ExportFormatAdapterInterface` or extend `AbstractExportFormatAdapter`
2. Implement the static `supportsFormat` method
3. Place the adapter in the `_libs/Nzoom/Export/Adapter/` directory or register it manually

```php
<?php
namespace Nzoom\Export\Adapter;

use Nzoom\Export\Entity\ExportData;

class PdfExportFormatAdapter extends AbstractExportFormatAdapter
{
    public function export($destination, string $type, ExportData $data, array $options = []): void
    {
        // PDF generation implementation
        $pdf = new \TCPDF();

        // Process export data
        foreach ($data as $record) {
            // Add record to PDF
        }

        $pdf->Output($destination, 'F');
    }

    public static function supportsFormat(string $format): bool
    {
        return strtolower($format) === 'pdf';
    }

    public function getSupportedExtensions(): array
    {
        return ['pdf'];
    }

    public function getMimeType(string $format = null): string
    {
        return 'application/pdf';
    }
}

// Place in _libs/Nzoom/Export/Adapter/ - automatically discovered
```

### Automatic Discovery

The factory automatically discovers adapters by:

1. **Scanning the adapter directory** for files ending with `*Adapter.php`
2. **Checking interface implementation** - must implement `ExportFormatAdapterInterface`
3. **Validating static method** - must have `supportsFormat` static method
4. **Caching results** for performance

### Benefits of New Approach

- **No manual mapping** - adapters are discovered automatically
- **Static validation** - no need to instantiate objects for format checking or getting extensions
- **Extensible** - just add new adapter files to the directory
- **Performance** - discovery results cached, supported formats cached with proper state tracking
- **Type safety** - strict interface and method validation
- **Fail fast** - throws exceptions immediately for unsupported formats
- **Clean contracts** - methods always return valid results or throw exceptions
- **Namespace aware** - uses `__NAMESPACE__` for future-proof refactoring

## Streaming & Performance

### Memory-Efficient Processing

The export system is designed for handling large datasets efficiently:

```php
// Streaming export for large datasets
$exportService = new ExportService($registry, 'orders', 'orders', 'xlsx');

// Create streaming data (loads records in chunks)
$exportData = $exportService->createStreamingExportData($outlook, $filters, 'Orders', 500);

// Export with memory optimization
$options = ['chunk_size' => 50];
$exportService->export('large_orders.xlsx', $exportData, $options);
```

### FileStreamer Components

- **FileStreamer**: Abstract base for streaming files to browser
- **PointerFileStreamer**: Streams from file pointers with chunked reading
- **GeneratorFileStreamer**: Streams from PHP generators for memory efficiency
- **StreamHeaders**: Manages HTTP headers for file downloads

### Performance Features

- ✅ **Chunked Processing**: Configurable chunk sizes for optimal memory usage
- ✅ **Garbage Collection**: Automatic memory cleanup during processing
- ✅ **Progress Tracking**: Built-in logging for large export operations
- ✅ **Streaming Output**: Direct browser streaming without temp files
- ✅ **Memory Optimization**: Intelligent memory limit management

## Migration Guide

### Current API (Recommended)

```php
// Modern approach using ExportService with type
$exportService = new ExportService($registry, 'customers', 'customers', 'xlsx');
$exportData = $exportService->createExportData($outlook, $filters, 'Customers');
$exportService->export('customers.xlsx', $exportData, $options);
```

### Legacy Support

The system maintains backward compatibility with existing code. All previous export methods continue to work seamlessly.

## API Reference

### ExportService

**Constructor**:
```php
new ExportService(\Registry $registry, string $module, string $controller, string $type = null)
```

**Key Methods**:
- `export(string $filename, ExportData $data, array $options = []): void`
- `createExportData(\Outlook $outlook, array $filters, string $factoryClass): ExportData`
- `createExportDataWithTables(\Outlook $outlook, array $filters, string $factoryClass): ExportData`
- `getSupportedFormats(): array`
- `isFormatSupported(string $format): bool`

### Data Entities

- **ExportData**: Container for export data with streaming support
- **ExportRecord**: Individual record with key-value data
- **ExportValue**: Typed value with formatting information
- **ExportHeader**: Column definitions and metadata
- **ExportColumn**: Individual column configuration

## System Benefits

### Architecture
- ✅ **Modular Design**: Each format handled by dedicated adapter
- ✅ **Clean Separation**: Business logic separated from format-specific code
- ✅ **Extensible**: Add new formats without modifying existing code
- ✅ **Type Safe**: Strict interfaces and validation

### Performance
- ✅ **Memory Efficient**: Streaming support for large datasets
- ✅ **Optimized Processing**: Chunked processing with garbage collection
- ✅ **Lazy Loading**: Components created only when needed
- ✅ **Caching**: Adapter discovery and format support cached

### Developer Experience
- ✅ **Simple API**: Intuitive methods with clear parameters
- ✅ **Comprehensive Documentation**: Examples and usage patterns
- ✅ **Error Handling**: Detailed error messages and logging
- ✅ **Backward Compatible**: Existing code continues to work

## Best Practices

### For New Development
```php
// Recommended approach
$exportService = new ExportService($registry, 'module', 'controller', 'xlsx');
$exportData = $exportService->createExportData($outlook, $filters, 'FactoryClass');
$exportService->export('filename.xlsx', $exportData, $options);
```

### For Large Datasets
```php
// Use streaming for memory efficiency
$exportData = $exportService->createExportData($outlook, $filters, 'FactoryClass', 500);
$options = ['max_column_width' => 40.0, 'max_row_height' => 100.0];
$exportService->export('large_export.xlsx', $exportData, $options);
```

### With Table Data
```php
// Include related table data in separate Excel sheets
$exportData = $exportService->createExportDataWithTables($outlook, $filters, 'Documents');
$exportService->export('documents_with_tables.xlsx', $exportData);
```

## Documentation

For detailed feature documentation, see the [docs](docs/) directory:

- **[Table Export](docs/table-export.md)** - Auto-discovery of related table data
- **[Model Table Provider](docs/model-table-provider.md)** - Integration guide for automatic table extraction
- **[Excel Constrained Sizing](docs/excel-constrained-sizing.md)** - Control column and row sizes
- **[Testing Guide](docs/testing.md)** - Testing and troubleshooting

## Examples

The [Examples](Examples/) directory contains working code samples:

- **[ExampleRunner.php](Examples/ExampleRunner.php)** - Run all examples easily
- **[QuickTest.php](Examples/QuickTest.php)** - Quick test suite for examples
- **[ExcelConstrainedSizingExample.php](Examples/ExcelConstrainedSizingExample.php)** - Excel sizing constraints
- **[StreamingUsage.php](Examples/StreamingUsage.php)** - Memory-efficient streaming
- **[TableExportUsage.php](Examples/TableExportUsage.php)** - Table export functionality
- **[TableFilteringExample.php](Examples/TableFilteringExample.php)** - Table filtering behavior

### Running Examples

```php
// Quick test to verify examples work
\Nzoom\Export\Examples\QuickTest::runAll($registry);

// Run all standalone examples (no external data required)
\Nzoom\Export\Examples\ExampleRunner::runStandaloneExamples($registry);

// Run specific example category
\Nzoom\Export\Examples\ExampleRunner::runExcelConstrainedSizingExamples($registry);

// Run all examples with data
\Nzoom\Export\Examples\ExampleRunner::runAllExamples($registry, $outlook, $filters);
```

## Current Status

### ✅ Implemented
- Excel export with constrained sizing
- CSV export with flexible delimiters
- JSON export with pretty printing
- Streaming support for large datasets
- Memory optimization and chunked processing
- Comprehensive error handling and logging
- Backward compatibility with existing code

### ❌ Not Implemented
- PDF export adapter (placeholder exists)
- Export templates and custom styling
- Background/queued export processing
- Export compression (ZIP support)

### 🔧 Areas for Enhancement
- Unit test coverage
- Performance benchmarking
- Additional export formats (XML, ODS)
- Export validation and sanitization
