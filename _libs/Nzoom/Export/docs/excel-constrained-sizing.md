# Excel Constrained Sizing Feature

## Overview

The Excel Export Format Adapter now includes constrained sizing functionality to prevent extremely wide columns and tall rows that can make Excel files difficult to navigate and read.

## Problem Solved

Previously, when exporting data with very long text content, Excel's auto-sizing feature would create:
- Extremely wide columns (up to 255 Excel units)
- Very tall rows when text wrapped
- Poor user experience when viewing the exported files

## Solution

The new constrained sizing feature:
1. **Applies maximum column width limits** - Columns wider than the limit are capped and text wrapping is enabled
2. **Applies maximum row height limits** - Rows taller than the limit are capped
3. **Maintains readability** - Text wrapping ensures content is still visible
4. **Configurable constraints** - Limits can be customized per export

## Configuration Options

### New Export Options

| Option | Type | Default | Range | Description |
|--------|------|---------|-------|-------------|
| `max_column_width` | float | 50.0 | 10.0 - 255.0 | Maximum column width in Excel units |
| `max_row_height` | float | 100.0 | 15.0 - 500.0 | Maximum row height in points |
| `locale` | string | '' (auto) | See supported locales | Language for formatting and functions |

### Excel Units Reference

- **Column Width**: Excel units (approximately 1 unit = 7 pixels for default font)
- **Row Height**: Points (1 point = 1/72 inch)

### Supported Locales

The locale setting affects number formatting, date formatting, and Excel function names:

| Locale Code | Language | Example Number | Example Date |
|-------------|----------|----------------|--------------|
| `en_us` | English (US) | 1,234.56 | 12/31/2023 |
| `de` | German | 1.234,56 | 31.12.2023 |
| `fr` | French | 1 234,56 | 31/12/2023 |
| `es` | Spanish | 1.234,56 | 31/12/2023 |
| `it` | Italian | 1.234,56 | 31/12/2023 |
| `pt_br` | Portuguese (Brazil) | 1.234,56 | 31/12/2023 |
| `sv` | Swedish | 1 234,56 | 2023-12-31 |
| `no` | Norwegian | 1 234,56 | 31.12.2023 |
| `da` | Danish | 1.234,56 | 31-12-2023 |
| `fi` | Finnish | 1 234,56 | 31.12.2023 |

**Auto Mode**: When locale is set to empty string or not specified, the system automatically uses the application's current language setting.

### Recommended Values

| Use Case | Column Width | Row Height | Description |
|----------|--------------|------------|-------------|
| **Tight** | 20-30 | 40-60 | Compact layout, minimal scrolling |
| **Medium** | 35-50 | 70-100 | Balanced readability and space |
| **Loose** | 60-80 | 120-150 | More space for content |

## Usage Examples

### Basic Usage (Default Constraints)

```php
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;

$adapter = new ExcelExportFormatAdapter($registry, 'customers', 'customers');

// Uses default constraints: max_column_width=50.0, max_row_height=100.0
$adapter->export('customers.xlsx', 'xlsx', $exportData);
```

### Custom Constraints

```php
$options = [
    'max_column_width' => 30.0,  // Narrower columns
    'max_row_height' => 60.0,    // Shorter rows
    'chunk_size' => 50
];

$adapter->export('customers.xlsx', 'xlsx', $exportData, $options);
```

### Custom Locale

```php
$options = [
    'max_column_width' => 40.0,
    'max_row_height' => 80.0,
    'locale' => 'de'  // German formatting
];

$adapter->export('customers.xlsx', 'xlsx', $exportData, $options);
```

### Using with ExportService

```php
use Nzoom\Export\ExportService;

$exportService = new ExportService($registry, 'products', 'products', 'xlsx');

$options = [
    'max_column_width' => 40.0,
    'max_row_height' => 80.0
];

$exportService->export('products.xlsx', $exportData, $options);
```

### Format Options Integration

The constraints are now part of the format options:

```php
$adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');
$formatOptions = $adapter->getFormatOptions();

// Will include:
// - max_column_width: number input (10.0-255.0, default 50.0)
// - max_row_height: number input (15.0-500.0, default 100.0)
```

## How It Works

### Column Width Constraints

1. **Auto-sizing enabled** - Initially, all columns are set to auto-size
2. **Width calculation** - PhpSpreadsheet calculates optimal column widths
3. **Constraint application** - Columns exceeding the maximum are capped
4. **Text wrapping** - Constrained columns automatically enable text wrapping
5. **Auto-size disabled** - Constrained columns have auto-size disabled to maintain the limit

### Row Height Constraints

1. **Multiline detection** - Rows are analyzed for multiline content (line breaks or long text)
2. **Fixed height assignment** - Rows with multiline content get a fixed height (3 lines worth)
3. **Visual indication** - Taller rows indicate that content is long/multiline
4. **Constraint application** - Fixed heights respect the maximum row height limit
5. **Content preservation** - Text wrapping ensures content remains visible

### Text Wrapping

When a column is constrained:
- `setWrapText(true)` is automatically applied to the entire column
- Content flows to multiple lines within the cell
- Row height may increase (up to the row height limit)

### Cell Alignment

All cells are automatically styled with:
- **Vertical alignment: Top** - Content aligns to the top of cells for better readability
- Consistent appearance across all rows, especially beneficial for multiline content

## Implementation Details

### Key Methods

- `extractSizingOptions()` - Extracts constraints from export options
- `applyColumnWidthConstraints()` - Applies column width limits and text wrapping
- `applyRowHeightConstraints()` - Detects multiline content and applies fixed row heights
- `applyVerticalAlignment()` - Sets vertical alignment to top for all cells
- `finalizeExportDataColumns()` - Orchestrates the constrained sizing process

### Row Height Calculation Details

Since PhpSpreadsheet doesn't automatically calculate row heights for wrapped text, our simplified implementation:

1. **Detects multiline content** - Checks for explicit line breaks (`\n`) or long text (>100 chars) with text wrapping
2. **Applies fixed height** - Sets rows with multiline content to 3 lines worth of height
3. **Uses font metrics** - Calculates height as font size × 3.6 (3 lines × 1.2 line spacing)
4. **Respects constraints** - Final height is limited by the maximum row height setting
5. **Preserves default for single-line** - Rows with simple content use Excel's default height

**Formula**: `Multiline Row Height = min(Font Size × 3.6, Max Row Height)`

### Performance Considerations

- **Memory efficient** - Constraints are applied after auto-sizing calculation
- **Minimal overhead** - Only processes columns that exceed limits
- **Chunk processing** - Large exports still use chunked processing

## Migration Guide

### Existing Code

No changes required for existing code. The feature uses sensible defaults:

```php
// This continues to work as before, but now with reasonable column/row limits
$adapter->export('file.xlsx', 'xlsx', $exportData);
```

### Opting Out

To disable constraints (restore old behavior):

```php
$options = [
    'max_column_width' => 255.0,  // Excel maximum
    'max_row_height' => 500.0     // Very high limit
];

$adapter->export('file.xlsx', 'xlsx', $exportData, $options);
```

## Benefits

1. **Better User Experience** - Excel files are easier to navigate
2. **Consistent Layout** - Predictable column and row sizes
3. **Preserved Content** - Text wrapping ensures no data is lost
4. **Configurable** - Constraints can be adjusted per use case
5. **Backward Compatible** - Existing code works without changes

## Testing

Use the provided example script to test different constraint levels:

```php
use Nzoom\Export\Examples\ExcelConstrainedSizingExample;

// Run all examples to see the difference
ExcelConstrainedSizingExample::runAllExamples($registry);
```

This will generate several Excel files showing the effect of different constraint settings.
