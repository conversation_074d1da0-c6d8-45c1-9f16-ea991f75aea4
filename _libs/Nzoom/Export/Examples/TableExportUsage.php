<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportTable;
use Nzoom\Export\Entity\ExportTableCollection;
use Nzoom\Export\Provider\ModelTableProvider;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;

/**
 * Examples demonstrating table export functionality
 *
 * Shows how to export data with related tables included as separate Excel sheets.
 * Tables are automatically discovered from model grouping data.
 */
class TableExportUsage
{
    /**
     * Basic table export using ExportService (recommended approach)
     */
    public static function basicTableExport(\Registry $registry, \Outlook $outlook, array $filters): void
    {
        // Create export service with Excel format
        $exportService = new \Nzoom\Export\ExportService($registry, 'documents', 'documents', 'xlsx');

        // Create export data with tables automatically included
        $exportData = $exportService->createExportDataWithTables($outlook, $filters, 'Documents');

        // Export to browser with tables in separate sheets
        $exportService->export('documents_with_tables.xlsx', $exportData);
    }

    /**
     * Using DataFactory directly for more control
     */
    public static function dataFactoryTableExport(\Registry $registry, array $models, \Outlook $outlook): void
    {
        $dataFactory = new \Nzoom\Export\DataFactory($registry);

        // Configure table provider with custom options
        $dataFactory->withModelTableProvider('full_num', 'Full Num', [
            'include_empty_tables' => false,
            'date_format' => 'd.m.Y'
        ]);

        // Create export data with tables
        $exportData = $dataFactory($models, $outlook);

        // Export using adapter directly
        $adapter = new ExcelExportFormatAdapter($registry, 'documents', 'export');
        $adapter->export('php://output', 'xlsx', $exportData);
    }

    /**
     * Streaming export with tables for large datasets
     */
    public static function streamingTableExport(\Registry $registry, \Outlook $outlook, array $filters): void
    {
        // Create export service
        $exportService = new \Nzoom\Export\ExportService($registry, 'documents', 'documents', 'xlsx');

        // Create streaming export data with tables (memory efficient)
        $exportData = $exportService->createExportDataWithTables($outlook, $filters, 'Documents', 500);

        // Export with streaming
        $exportService->export('large_documents_with_tables.xlsx', $exportData);
    }

    /**
     * Conditional table inclusion based on user preference
     */
    public static function conditionalTableExport(\Registry $registry, \Outlook $outlook, array $filters, bool $includeTables): void
    {
        $exportService = new \Nzoom\Export\ExportService($registry, 'documents', 'documents', 'xlsx');

        if ($includeTables) {
            // Include tables in export
            $exportData = $exportService->createExportDataWithTables($outlook, $filters, 'Documents');
            $filename = 'documents_detailed.xlsx';
        } else {
            // Basic export without tables
            $exportData = $exportService->createExportData($outlook, $filters, 'Documents');
            $filename = 'documents_basic.xlsx';
        }

        $exportService->export($filename, $exportData);
    }
}
