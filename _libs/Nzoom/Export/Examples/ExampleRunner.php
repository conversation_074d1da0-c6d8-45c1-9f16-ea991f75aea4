<?php

namespace Nzoom\Export\Examples;

/**
 * Example runner for testing all export examples
 * 
 * This class provides a convenient way to run all export examples
 * for testing and demonstration purposes.
 */
class ExampleRunner
{
    /**
     * Run all Excel constrained sizing examples
     */
    public static function runExcelConstrainedSizingExamples(\Registry $registry): void
    {
        echo "=== Excel Constrained Sizing Examples ===\n\n";
        
        try {
            ExcelConstrainedSizingExample::runAllExamples($registry);
            echo "✅ Excel constrained sizing examples completed successfully\n\n";
        } catch (\Exception $e) {
            echo "❌ Excel constrained sizing examples failed: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Run streaming usage examples (requires outlook and filters)
     */
    public static function runStreamingExamples(\Registry $registry, \Outlook $outlook, array $filters = []): void
    {
        echo "=== Streaming Usage Examples ===\n\n";
        
        try {
            echo "1. Traditional usage example:\n";
            // Note: This requires actual models, so we'll skip for demo
            echo "   (Skipped - requires actual model data)\n\n";
            
            echo "2. Streaming usage example:\n";
            StreamingUsage::streamingUsage($registry, $outlook);
            echo "\n";
            
            echo "3. Large dataset streaming example:\n";
            StreamingUsage::largeDatasetStreaming($registry, $outlook);
            echo "\n";
            
            echo "4. Streaming with progress example:\n";
            StreamingUsage::streamingWithProgress($registry, $outlook);
            echo "\n";
            
            echo "✅ Streaming examples completed successfully\n\n";
        } catch (\Exception $e) {
            echo "❌ Streaming examples failed: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Run table export examples (requires outlook and filters)
     */
    public static function runTableExportExamples(\Registry $registry, \Outlook $outlook, array $filters = []): void
    {
        echo "=== Table Export Examples ===\n\n";
        
        try {
            echo "1. Basic table export:\n";
            TableExportUsage::basicTableExport($registry, $outlook, $filters);
            echo "\n";
            
            echo "2. Streaming table export:\n";
            TableExportUsage::streamingTableExport($registry, $outlook, $filters);
            echo "\n";
            
            echo "3. Conditional table export (with tables):\n";
            TableExportUsage::conditionalTableExport($registry, $outlook, $filters, true);
            echo "\n";
            
            echo "4. Conditional table export (without tables):\n";
            TableExportUsage::conditionalTableExport($registry, $outlook, $filters, false);
            echo "\n";
            
            echo "✅ Table export examples completed successfully\n\n";
        } catch (\Exception $e) {
            echo "❌ Table export examples failed: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Run table filtering examples (requires outlook)
     */
    public static function runTableFilteringExamples(\Registry $registry, \Outlook $outlook): void
    {
        echo "=== Table Filtering Examples ===\n\n";
        
        try {
            echo "1. Mixed tables example:\n";
            TableFilteringExample::mixedTablesExample($registry, $outlook);
            echo "\n";
            
            echo "2. Empty value types example:\n";
            TableFilteringExample::emptyValueTypesExample($registry, $outlook);
            echo "\n";
            
            echo "3. Disable filtering example:\n";
            TableFilteringExample::disableFilteringExample($registry, $outlook);
            echo "\n";
            
            echo "✅ Table filtering examples completed successfully\n\n";
        } catch (\Exception $e) {
            echo "❌ Table filtering examples failed: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Run all examples that don't require external data
     */
    public static function runStandaloneExamples(\Registry $registry): void
    {
        echo "=== Running Standalone Examples ===\n\n";
        
        // Excel constrained sizing examples (generates test data internally)
        self::runExcelConstrainedSizingExamples($registry);
        
        echo "=== Standalone Examples Complete ===\n\n";
        echo "Note: Other examples require actual database data and outlook configuration.\n";
        echo "To run those examples, call the specific methods with appropriate parameters.\n\n";
    }

    /**
     * Run all examples with provided data
     */
    public static function runAllExamples(\Registry $registry, \Outlook $outlook, array $filters = []): void
    {
        echo "=== Running All Export Examples ===\n\n";
        
        // Run standalone examples first
        self::runExcelConstrainedSizingExamples($registry);
        
        // Run examples that require data
        self::runStreamingExamples($registry, $outlook, $filters);
        self::runTableExportExamples($registry, $outlook, $filters);
        self::runTableFilteringExamples($registry, $outlook);
        
        echo "=== All Examples Complete ===\n\n";
    }

    /**
     * Display usage information
     */
    public static function showUsage(): void
    {
        echo "=== Export Examples Usage ===\n\n";
        echo "Available example categories:\n\n";
        echo "1. Excel Constrained Sizing Examples:\n";
        echo "   ExampleRunner::runExcelConstrainedSizingExamples(\$registry);\n\n";
        echo "2. Streaming Usage Examples:\n";
        echo "   ExampleRunner::runStreamingExamples(\$registry, \$outlook, \$filters);\n\n";
        echo "3. Table Export Examples:\n";
        echo "   ExampleRunner::runTableExportExamples(\$registry, \$outlook, \$filters);\n\n";
        echo "4. Table Filtering Examples:\n";
        echo "   ExampleRunner::runTableFilteringExamples(\$registry, \$outlook);\n\n";
        echo "5. Run All Standalone Examples:\n";
        echo "   ExampleRunner::runStandaloneExamples(\$registry);\n\n";
        echo "6. Run All Examples:\n";
        echo "   ExampleRunner::runAllExamples(\$registry, \$outlook, \$filters);\n\n";
    }
}
