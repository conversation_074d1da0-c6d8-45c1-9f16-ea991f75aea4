<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportRecord;

/**
 * Example demonstrating the new constrained sizing feature for Excel exports
 *
 * This example shows how to use the maximum column width and row height
 * constraints to prevent extremely wide columns and tall rows in Excel exports.
 */
class ExcelConstrainedSizingExample
{
    /**
     * Example 1: Basic usage with default constraints
     */
    public static function basicConstrainedExport(\Registry $registry)
    {
        // Create adapter
        $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');

        // Create test data with very long text that would normally create wide columns
        $exportData = self::createTestDataWithLongText();

        // Export with default constraints (max column width: 50, max row height: 100)
        $adapter->export('test_basic_constrained.xlsx', 'xlsx', $exportData);

        echo "Basic constrained export completed. Check test_basic_constrained.xlsx\n";
    }

    /**
     * Example 2: Custom constraints
     */
    public static function customConstrainedExport(\Registry $registry)
    {
        // Create adapter
        $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');

        // Create test data
        $exportData = self::createTestDataWithLongText();

        // Export with custom constraints
        $options = [
            'max_column_width' => 30.0,  // Narrower columns
            'max_row_height' => 50.0,    // Shorter rows
            'chunk_size' => 50
        ];

        $adapter->export('test_custom_constrained.xlsx', 'xlsx', $exportData, $options);

        echo "Custom constrained export completed. Check test_custom_constrained.xlsx\n";
    }

    /**
     * Example 3: Comparison - unconstrained vs constrained
     */
    public static function comparisonExport(\Registry $registry)
    {
        // Create adapter
        $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');

        // Create test data
        $exportData = self::createTestDataWithLongText();

        // Export with very high constraints (essentially unconstrained)
        $unconstrainedOptions = [
            'max_column_width' => 255.0,  // Maximum Excel allows
            'max_row_height' => 500.0     // Very high
        ];

        $adapter->export('test_unconstrained.xlsx', 'xlsx', $exportData, $unconstrainedOptions);

        // Export with reasonable constraints
        $constrainedOptions = [
            'max_column_width' => 40.0,
            'max_row_height' => 80.0
        ];

        $adapter->export('test_constrained.xlsx', 'xlsx', $exportData, $constrainedOptions);

        echo "Comparison exports completed. Compare test_unconstrained.xlsx vs test_constrained.xlsx\n";
    }

    /**
     * Example 4: Testing different constraint levels
     */
    public static function constraintLevelsTest(\Registry $registry)
    {
        $adapter = new ExcelExportFormatAdapter($registry, 'test', 'test');
        $exportData = self::createTestDataWithLongText();

        $constraintLevels = [
            'tight' => ['max_column_width' => 20.0, 'max_row_height' => 40.0],
            'medium' => ['max_column_width' => 35.0, 'max_row_height' => 70.0],
            'loose' => ['max_column_width' => 60.0, 'max_row_height' => 120.0]
        ];

        foreach ($constraintLevels as $level => $options) {
            $filename = "test_constraints_{$level}.xlsx";
            $adapter->export($filename, 'xlsx', $exportData, $options);
            echo "Exported {$filename} with {$level} constraints\n";
        }
    }

    /**
     * Create test data with very long text content
     */
    private static function createTestDataWithLongText(): ExportData
    {
        // Create headers
        $header = new ExportHeader([
            'id' => 'ID',
            'short_text' => 'Short Text',
            'long_text' => 'Very Long Text Column',
            'description' => 'Description',
            'multiline_text' => 'Multiline Content'
        ]);

        // Create records with varying text lengths
        $records = [];

        // Record 1: Extremely long text
        $records[] = new ExportRecord([
            'id' => '1',
            'short_text' => 'Short',
            'long_text' => str_repeat('This is a very long text that would normally make the column extremely wide in Excel. ', 10),
            'description' => 'Normal description text',
            'multiline_text' => "Line 1\nLine 2\nLine 3\nLine 4\nLine 5"
        ]);

        // Record 2: Medium length text
        $records[] = new ExportRecord([
            'id' => '2',
            'short_text' => 'Medium',
            'long_text' => str_repeat('Medium length text content. ', 5),
            'description' => 'Another description with some more text to test column sizing',
            'multiline_text' => "First line\nSecond line\nThird line"
        ]);

        // Record 3: Mixed content
        $records[] = new ExportRecord([
            'id' => '3',
            'short_text' => 'Mix',
            'long_text' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.',
            'description' => 'Short desc',
            'multiline_text' => "A\nB\nC\nD\nE\nF\nG\nH\nI\nJ"
        ]);

        // Record 4: Very short content
        $records[] = new ExportRecord([
            'id' => '4',
            'short_text' => 'S',
            'long_text' => 'Short',
            'description' => 'Brief',
            'multiline_text' => "One\nTwo"
        ]);

        // Record 5: Another long text example with many line breaks
        $records[] = new ExportRecord([
            'id' => '5',
            'short_text' => 'Test',
            'long_text' => 'This is another example of extremely long text content that would typically cause Excel columns to become very wide, making the spreadsheet difficult to navigate and read. The new constrained sizing feature should handle this gracefully.',
            'description' => 'Testing the constrained sizing feature with various text lengths',
            'multiline_text' => "Line 1: This is a test of row height calculation\nLine 2: When text wraps to multiple lines\nLine 3: The row height should automatically adjust\nLine 4: But be constrained by the maximum limit\nLine 5: This helps maintain readability\nLine 6: While preserving all content\nLine 7: Through proper text wrapping\nLine 8: And height calculation"
        ]);

        // Record 6: Test extreme multiline content
        $records[] = new ExportRecord([
            'id' => '6',
            'short_text' => 'Extreme',
            'long_text' => str_repeat('Very long text that will wrap multiple times when constrained. ', 8),
            'description' => 'Testing extreme cases for both width and height constraints',
            'multiline_text' => implode("\n", array_map(function($i) { return "Line $i: " . str_repeat("Text ", 10); }, range(1, 15)))
        ]);

        return new ExportData('TestData', $header, $records);
    }

    /**
     * Run all examples
     */
    public static function runAllExamples(\Registry $registry)
    {
        echo "Running Excel Constrained Sizing Examples...\n\n";

        echo "1. Basic constrained export:\n";
        self::basicConstrainedExport($registry);
        echo "\n";

        echo "2. Custom constrained export:\n";
        self::customConstrainedExport($registry);
        echo "\n";

        echo "3. Comparison export:\n";
        self::comparisonExport($registry);
        echo "\n";

        echo "4. Constraint levels test:\n";
        self::constraintLevelsTest($registry);
        echo "\n";

        echo "All examples completed!\n";
        echo "Check the generated .xlsx files to see the difference in column widths and row heights.\n";
    }
}
