<?php

namespace Nzoom\Export\Examples;

use Nzoom\Export\DataFactory;
use Nzoom\Export\Entity\ExportData;

/**
 * Examples demonstrating streaming export functionality for large datasets
 *
 * Shows how to use DataFactory's streaming capabilities to process large amounts
 * of data efficiently without loading everything into memory at once.
 */
class StreamingUsage
{
    /**
     * Traditional usage with array of models (backward compatible)
     */
    public static function traditionalUsage(\Registry $registry, array $models, \Outlook $outlook): void
    {
        $dataFactory = new DataFactory($registry);

        // Create export data from array of models (all loaded in memory)
        $exportData = $dataFactory($models, $outlook);

        // Process all records
        foreach ($exportData as $record) {
            echo "Processing record ID: " . $record->getMetadataValue('id') . "\n";
        }

        echo "Total records: " . $exportData->count() . "\n";
    }

    /**
     * Streaming usage for large datasets (memory efficient)
     */
    public static function streamingUsage(\Registry $registry, \Outlook $outlook): void
    {
        $dataFactory = new DataFactory($registry);

        // Define database filters
        $filters = [
            'where' => ['status = "finished"'],
            'order' => 'id DESC'
        ];

        // Create streaming export data (loads records in chunks)
        $exportData = $dataFactory->createStreaming(
            'Finance_Incomes',  // Factory class name
            $filters,           // Database filters
            $outlook,           // Outlook configuration
            1000               // Page size (default: 200)
        );

        echo "Total records available: " . $exportData->count() . "\n";
        echo "Using lazy loading: " . ($exportData->isLazy() ? 'Yes' : 'No') . "\n";

        // Process records one by one (memory efficient)
        $processedCount = 0;
        foreach ($exportData as $record) {
            echo "Processing record ID: " . $record->getMetadataValue('id') . "\n";
            $processedCount++;

            // Break early if needed
            if ($processedCount >= 100) {
                echo "Processed first 100 records, stopping...\n";
                break;
            }
        }
    }

    /**
     * Example 3: Large dataset streaming with bigger page size
     */
    public static function largeDatasetStreaming(\Registry $registry, \Outlook $outlook)
    {
        $dataFactory = new DataFactory($registry);

        $filters = [
            'where' => ['status = "finished"'],
            'order' => 'id ASC'  // Consistent ordering for reliable pagination
        ];

        // Create streaming with larger page size for better performance
        $exportData = $dataFactory->createStreaming(
            'Finance_Incomes',  // Factory class name
            $filters,           // Database filters
            $outlook,           // Outlook configuration
            2000               // Larger page size for better performance
        );

        echo "Using large page size streaming\n";
        echo "Total records: " . $exportData->count() . "\n";

        // Process all records efficiently
        $processedCount = 0;
        foreach ($exportData as $record) {
            $processedCount++;

            // Show progress every 5000 records
            if ($processedCount % 5000 === 0) {
                echo "Processed {$processedCount} records...\n";
            }
        }

        echo "Completed processing {$processedCount} records\n";
    }

    /**
     * Example 4: Export to file with streaming (memory efficient)
     */
    public static function streamingFileExport(\Registry $registry, \Outlook $outlook, string $filename)
    {
        $dataFactory = new DataFactory($registry);

        $filters = [
            'where' => ['status = "finished"'],
            'order' => 'id DESC'
        ];

        $exportData = $dataFactory->createStreaming('Finance_Incomes', $filters, $outlook, 500);

        // Open file for writing
        $file = fopen($filename, 'w');

        // Write header
        $header = $exportData->getHeader();
        fputcsv($file, $header->getLabels());

        // Write records one by one (memory efficient)
        foreach ($exportData as $record) {
            fputcsv($file, $record->getRawValues());
        }

        fclose($file);

        echo "Exported " . $exportData->count() . " records to {$filename}\n";
    }

    /**
     * Example 5: Progress tracking with streaming
     */
    public static function streamingWithProgress(\Registry $registry, \Outlook $outlook)
    {
        $dataFactory = new DataFactory($registry);

        $filters = ['where' => ['status = "finished"']];
        $exportData = $dataFactory->createStreaming('Finance_Incomes', $filters, $outlook, 100);

        $totalRecords = $exportData->count();
        $processedRecords = 0;

        echo "Starting export of {$totalRecords} records...\n";

        foreach ($exportData as $record) {
            // Process record
            $processedRecords++;

            // Show progress every 1000 records
            if ($processedRecords % 1000 === 0) {
                $percentage = round(($processedRecords / $totalRecords) * 100, 2);
                echo "Progress: {$processedRecords}/{$totalRecords} ({$percentage}%)\n";
            }
        }

        echo "Export completed!\n";
    }
}
