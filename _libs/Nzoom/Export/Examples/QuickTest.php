<?php

namespace Nzoom\Export\Examples;

/**
 * Quick test to verify examples are working
 * 
 * This provides a simple way to test that the export examples
 * are functioning correctly without requiring complex setup.
 */
class QuickTest
{
    /**
     * Quick test of Excel constrained sizing
     */
    public static function testExcelConstrainedSizing(\Registry $registry): bool
    {
        try {
            echo "Testing Excel constrained sizing example...\n";
            
            // Run basic constrained export
            ExcelConstrainedSizingExample::basicConstrainedExport($registry);
            
            // Check if file was created
            if (file_exists('test_basic_constrained.xlsx')) {
                echo "✅ Excel file created successfully\n";
                
                // Clean up test file
                unlink('test_basic_constrained.xlsx');
                
                return true;
            } else {
                echo "❌ Excel file was not created\n";
                return false;
            }
            
        } catch (\Exception $e) {
            echo "❌ Test failed: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Test ExampleRunner functionality
     */
    public static function testExampleRunner(\Registry $registry): bool
    {
        try {
            echo "Testing ExampleRunner...\n";
            
            // Test standalone examples
            ExampleRunner::runStandaloneExamples($registry);
            
            echo "✅ ExampleRunner completed successfully\n";
            return true;
            
        } catch (\Exception $e) {
            echo "❌ ExampleRunner test failed: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Run all quick tests
     */
    public static function runAll(\Registry $registry): void
    {
        echo "=== Quick Test Suite ===\n\n";
        
        $results = [];
        
        // Test Excel constrained sizing
        $results['excel_sizing'] = self::testExcelConstrainedSizing($registry);
        echo "\n";
        
        // Test ExampleRunner
        $results['example_runner'] = self::testExampleRunner($registry);
        echo "\n";
        
        // Summary
        echo "=== Test Results ===\n";
        $passed = 0;
        $total = count($results);
        
        foreach ($results as $test => $result) {
            $status = $result ? '✅ PASS' : '❌ FAIL';
            echo "{$status}: {$test}\n";
            if ($result) $passed++;
        }
        
        echo "\nSummary: {$passed}/{$total} tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All tests passed! Examples are working correctly.\n";
        } else {
            echo "⚠️  Some tests failed. Check the output above for details.\n";
        }
    }
}
