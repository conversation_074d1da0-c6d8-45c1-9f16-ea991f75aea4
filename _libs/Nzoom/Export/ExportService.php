<?php

namespace Nzoom\Export;

use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Factory\ExportFormatFactory;
use Nzoom\I18n\I18nAwareTrait;

/**
 * Main service class for handling data exports
 *
 * Provides a unified interface for exporting data in various formats (Excel, CSV, JSON)
 * using the adapter pattern. Supports both regular and streaming exports for large datasets.
 */
class ExportService
{
    use I18nAwareTrait;

    /**
     * Application registry
     */
    protected \Registry $registry;

    /**
     * Module name for the export context
     */
    protected string $module;

    /**
     * Controller name for the export context
     */
    protected string $controller;

    /**
     * Model name (optional, used for legacy compatibility)
     */
    protected ?string $modelName = null;

    /**
     * Model factory name (optional, used for legacy compatibility)
     */
    protected ?string $modelFactoryName = null;

    /**
     * Export format factory for creating adapters
     */
    protected ?ExportFormatFactory $formatFactory = null;

    /**
     * Export format type (xlsx, csv, json, etc.)
     */
    private string $type;

    /**
     * Cached export adapter instance
     */
    private ?ExportFormatAdapterInterface $adapter = null;

    /**
     * Reference column mapping by model class for table exports
     */
    private static array $referenceColumnMap = [
        'Document' => ['name' => 'full_num', 'label' => 'Full Num'],
        'Customer' => ['name' => 'code', 'label' => 'Code'],
    ];

    /**
     * Create a new export service instance
     *
     * @param \Registry $registry Application registry
     * @param string $module Module name for export context
     * @param string $controller Controller name for export context
     * @param string $type Export format type (xlsx, csv, json, etc.)
     */
    public function __construct(\Registry $registry, string $module, string $controller, string $type)
    {
        $this->registry = $registry;
        $this->module = $module;
        $this->controller = $controller;
        $this->type = $type;
        $this->setTranslator($registry['translater']);
    }

    /**
     * Set the model name (for legacy compatibility)
     *
     * @param string $modelName Model name
     * @return self For method chaining
     */
    public function setModelName(string $modelName): self
    {
        $this->modelName = $modelName;
        return $this;
    }

    /**
     * Set the model factory name (for legacy compatibility)
     *
     * @param string $modelFactoryName Model factory class name
     * @return self For method chaining
     */
    public function setModelFactoryName(string $modelFactoryName): self
    {
        $this->modelFactoryName = $modelFactoryName;
        return $this;
    }

    /**
     * Create export action configuration for grid exports
     *
     * @param string $module_check Module to check permissions for
     * @param array $types Export type options
     * @param array $typeSections Export section options
     * @return array|null Export action configuration or null if not permitted
     */
    public function createExportAction(string $module_check, array $types, array $typeSections): ?array
    {
        $factory = new ExportActionFactory(
            $this->registry,
            $this->module,
            $this->controller,
            $this->modelName,
            $this->modelFactoryName,
            $this->registry['translater']
        );

        return $factory($module_check, $types, $typeSections);
    }

    /**
     * Create export data from database query results
     *
     * @param \Outlook $outlook Database query configuration
     * @param array $filters Additional query filters
     * @param string $modelClass Model class name for data extraction
     * @param int $pageSize Number of records to process per chunk (default: 200)
     * @return ExportData Streaming export data instance
     */
    public function createExportData(\Outlook $outlook, array $filters, string $modelClass, int $pageSize = 200): ExportData
    {
        $dataFactory = new DataFactory($this->registry);
        return $dataFactory->createStreaming($modelClass, $filters, $outlook, $pageSize);
    }

    /**
     * Create export data with additional table data included
     *
     * @param \Outlook $outlook Database query configuration
     * @param array $filters Additional query filters
     * @param string $factoryClass Factory class name for data extraction
     * @param int $pageSize Number of records to process per chunk (default: 200)
     * @return ExportData Streaming export data instance with table data
     */
    public function createExportDataWithTables(\Outlook $outlook, array $filters, string $factoryClass, int $pageSize = 200): ExportData
    {
        $dataFactory = new DataFactory($this->registry);

        // Get reference column configuration for the model class
        $referenceColumn = $this->getReferenceColumnForModel($factoryClass::$modelName);
        $dataFactory->withModelTableProvider($referenceColumn['name'], $referenceColumn['label']);

        return $dataFactory->createStreaming($factoryClass, $filters, $outlook, $pageSize);
    }

    /**
     * Create a generator-based file streamer for memory-efficient streaming
     *
     * @param callable $generatorFunction Function that returns a generator yielding data chunks
     * @param string $filename Filename to present to the browser
     * @param string $mimeType MIME type for the content (default: application/octet-stream)
     * @param int|null $totalSize Total size in bytes if known (for progress indication)
     * @return \Nzoom\Export\Streamer\GeneratorFileStreamer
     */
    public function createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType = 'application/octet-stream', ?int $totalSize = null): \Nzoom\Export\Streamer\GeneratorFileStreamer
    {
        return new \Nzoom\Export\Streamer\GeneratorFileStreamer($generatorFunction, $filename, $mimeType, $totalSize);
    }

    /**
     * Export data to browser in the configured format
     *
     * @param string $filename Filename to present to the browser
     * @param ExportData $data Export data to process
     * @param array $options Export options (e.g., max_column_width, max_row_height for Excel)
     * @return void This method streams to browser and exits
     * @throws \Exception If export fails
     */
    public function export(string $filename, ExportData $data, array $options = []): void
    {
        try {
            // Generate export to temporary stream
            $tempStream = $this->createTempStream();

            $adapter = $this->getAdapter();
            $adapter->export($tempStream, $this->type, $data, $options);

            // Stream to browser
            $this->streamToBrowser($tempStream, $filename, $adapter->getMimeType());
        } catch (\InvalidArgumentException $e) {
            $this->handleExportError('Unsupported export format: ' . $this->type);
        } catch (\Exception $e) {
            $this->handleExportError('Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Create a temporary stream for export generation
     *
     * @return resource A writable temporary stream
     * @throws \Exception If temp stream cannot be created
     */
    private function createTempStream()
    {
        $tempStream = fopen('php://temp', 'w+');

        if ($tempStream === false) {
            throw new \Exception('Failed to create temporary stream for export');
        }

        return $tempStream;
    }

    /**
     * Stream data to the browser using PointerFileStreamer
     *
     * @param resource $stream The stream containing the export data
     * @param string $downloadFilename Filename to present to the browser
     * @param string $mimeType MIME type for the file
     * @return void
     * @throws \Exception If stream cannot be sent
     */
    private function streamToBrowser($stream, string $downloadFilename, string $mimeType): void
    {
        if (!is_resource($stream)) {
            throw new \Exception('Invalid stream provided for streaming');
        }

        try {
            // Create and use PointerFileStreamer
            $streamer = new \Nzoom\Export\Streamer\PointerFileStreamer(
                $stream,
                $downloadFilename,
                $mimeType
            );

            // Stream the data (this will handle cleanup and exit)
            $streamer->stream();

        } finally {
            // Clean up stream
            if (is_resource($stream)) {
                fclose($stream);
            }
        }
    }

    /**
     * Get the export format factory (creates automatically if needed)
     *
     * @return ExportFormatFactory Format factory instance
     */
    public function getFormatFactory(): ExportFormatFactory
    {
        if (!isset($this->formatFactory)) {
            $this->formatFactory = new ExportFormatFactory($this->registry, $this->module, $this->controller);
        }

        return $this->formatFactory;
    }

    /**
     * Set a custom export format factory
     *
     * @param ExportFormatFactory $formatFactory Custom factory instance
     * @return self For method chaining
     */
    public function setFormatFactory(ExportFormatFactory $formatFactory): self
    {
        $this->formatFactory = $formatFactory;
        return $this;
    }

    /**
     * Get the export adapter for the configured type (creates automatically if needed)
     *
     * @return ExportFormatAdapterInterface Export adapter instance
     */
    public function getAdapter(): ExportFormatAdapterInterface
    {
        if (!isset($this->adapter)) {
            $this->adapter = $this->getFormatFactory()->createAdapter($this->type, ['extension' => $this->type]);
        }

        return $this->adapter;
    }

    /**
     * Get all supported export formats
     *
     * @return array List of supported format strings (e.g., ['xlsx', 'csv', 'json'])
     */
    public function getSupportedFormats(): array
    {
        return $this->getFormatFactory()->getSupportedFormats();
    }

    /**
     * Check if a specific format is supported
     *
     * @param string $format Format to check (e.g., 'xlsx', 'csv')
     * @return bool True if format is supported
     */
    public function isFormatSupported(string $format): bool
    {
        return $this->getFormatFactory()->isFormatSupported($format);
    }

    /**
     * Get reference column configuration for a model class
     *
     * @param string $modelClass Model class name (e.g., 'Document', 'Customer')
     * @return array Reference column configuration with 'name' and 'label' keys
     * @throws \InvalidArgumentException If no mapping exists for the model class
     */
    private function getReferenceColumnForModel(string $modelClass): array
    {
        if (isset(self::$referenceColumnMap[$modelClass])) {
            return self::$referenceColumnMap[$modelClass];
        }

        throw new \InvalidArgumentException("No reference column mapping found for model class: {$modelClass}");
    }

    /**
     * Generate export filename with proper extension
     *
     * @param string|array|null $prefix Filename prefix or array containing prefix
     * @param string $extension File extension without dot
     * @return string Complete filename with extension
     */
    protected function getExportFilename($prefix, string $extension): string
    {
        $filename = $prefix;

        // Handle array or null cases
        if (is_array($filename)) {
            $filename = $filename[0] ?? '';
        }

        if (empty($filename)) {
            $filename = strtolower($this->module . '_' . $this->controller . '_export_' . date('Y-m-d_H-i-s'));
        }

        // Add extension if not present
        if (substr($filename, -(strlen($extension) + 1)) !== '.' . $extension) {
            $filename .= '.' . $extension;
        }

        return $filename;
    }

    /**
     * Handle export errors with proper logging and response
     *
     * @param string $message Error message to display
     * @param int $statusCode HTTP status code (default: 400)
     * @return void This method may exit for AJAX requests
     */
    protected function handleExportError(string $message, int $statusCode = 400): void
    {
        // Log the error if logger is available
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Export error: ' . $message);
        }

        // Set error message in registry for AJAX response
        $this->registry->set('ajax_result', json_encode([
            'error' => $message,
            'status' => 'error',
            'timestamp' => date('Y-m-d H:i:s')
        ]), true);

        // If this is an AJAX request, send appropriate headers and exit
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

            http_response_code($statusCode);
            header('Content-Type: application/json');
            echo $this->registry->get('ajax_result');
            exit;
        }

        // For non-AJAX requests, let the controller handle the response
    }
}
