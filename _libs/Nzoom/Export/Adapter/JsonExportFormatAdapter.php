<?php

namespace Nzoom\Export\Adapter;

use Exception;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * JSON export format adapter
 *
 * Handles export to JSON format with configurable formatting options and data structures
 */
class JsonExportFormatAdapter extends AbstractExportFormatAdapter
{
    /**
     * @var string Default output structure
     */
    private string $outputStructure = 'array';

    /**
     * @var bool Whether to include metadata
     */
    private bool $includeMetadata = false;

    /**
     * {@inheritdoc}
     */
    public function export($file, string $type, ExportData $exportData, array $options = []): void
    {
        try {
            // Extract JSON options from parameters
            $this->extractJsonOptions($options);

            // Validate file parameter and determine save target
            $saveTarget = $this->validateAndPrepareSaveTarget($file);

            // Validate the export type
            $type = strtolower(trim($type));
            if (!in_array($type, $this->getSupportedExtensions())) {
                throw new Exception("Unsupported export type: {$type}. Supported types: " . implode(', ', $this->getSupportedExtensions()));
            }

            // Create and write JSON content
            $this->writeJsonContent($saveTarget, $exportData, $options);

        } catch (Exception $e) {
            // Log the error using General::log
            \General::log($this->registry, 'json_export_error', 'JSON export error: ' . $e->getMessage() . "\n" . $e->getTraceAsString());

            // Re-throw the exception for caller to handle
            throw new Exception('Error generating JSON file: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Extract JSON-specific options from export options
     *
     * @param array $options Export options
     * @return void
     */
    private function extractJsonOptions(array $options): void
    {
        if (isset($options['output_structure']) && is_string($options['output_structure'])) {
            $this->outputStructure = $options['output_structure'];
        } elseif (isset($this->configuration['output_structure']) && is_string($this->configuration['output_structure'])) {
            $this->outputStructure = $this->configuration['output_structure'];
        }

        if (isset($options['include_metadata'])) {
            $this->includeMetadata = (bool) $options['include_metadata'];
        } elseif (isset($this->configuration['include_metadata'])) {
            $this->includeMetadata = (bool) $this->configuration['include_metadata'];
        }
    }

    /**
     * Write JSON content to the target
     *
     * @param string|resource $saveTarget The file path or file pointer
     * @param ExportData $exportData The export data
     * @param array $options Export options
     * @return void
     * @throws Exception If file operations fail
     */
    private function writeJsonContent($saveTarget, ExportData $exportData, array $options): void
    {
        // Prepare data structure based on output format
        $data = $this->prepareJsonData($exportData);

        // Get JSON encoding options
        $jsonOptions = $this->getJsonOptions($options);

        // Encode to JSON
        $jsonData = json_encode($data, $jsonOptions);
        if ($jsonData === false) {
            throw new Exception('Failed to encode data to JSON: ' . json_last_error_msg());
        }

        // Write to file or file pointer
        if (is_string($saveTarget)) {
            $result = file_put_contents($saveTarget, $jsonData);
            if ($result === false) {
                throw new Exception("Cannot write to file: {$saveTarget}");
            }
        } else {
            $result = fwrite($saveTarget, $jsonData);
            if ($result === false) {
                throw new Exception("Cannot write to file pointer");
            }
        }

        // Log completion
        $recordCount = iterator_count($exportData);
        \General::log($this->registry, 'json_export_completed', "JSON export completed: {$recordCount} records processed");
    }

    /**
     * Prepare JSON data structure from ExportData
     *
     * @param ExportData $exportData The export data
     * @return array The prepared data structure
     */
    private function prepareJsonData(ExportData $exportData): array
    {
        switch ($this->outputStructure) {
            case 'object':
                return $this->prepareObjectStructure($exportData);
            case 'nested':
                return $this->prepareNestedStructure($exportData);
            case 'array':
            default:
                return $this->prepareArrayStructure($exportData);
        }
    }

    /**
     * Prepare array structure (default) - array of objects
     *
     * @param ExportData $exportData The export data
     * @return array
     */
    private function prepareArrayStructure(ExportData $exportData): array
    {
        $data = [];
        $recordCount = 0;
        $header = $exportData->getHeader();
        $columns = $header->getColumns();

        foreach ($exportData as $record) {
            $recordData = $this->processExportRecord($record, $columns);
            $data[] = $recordData;

            $recordCount++;

            // Perform garbage collection periodically for large exports
            if ($recordCount % 1000 === 0 && gc_enabled()) {
                gc_collect_cycles();
            }
        }

        return $this->includeMetadata ? $this->addMetadata($data, $exportData) : $data;
    }

    /**
     * Prepare object structure - single object with data array
     *
     * @param ExportData $exportData The export data
     * @return array
     */
    private function prepareObjectStructure(ExportData $exportData): array
    {
        // Get the array structure without metadata first
        $originalIncludeMetadata = $this->includeMetadata;
        $this->includeMetadata = false;
        $arrayData = $this->prepareArrayStructure($exportData);
        $this->includeMetadata = $originalIncludeMetadata;

        $result = [
            'data' => $arrayData
        ];

        if ($this->includeMetadata) {
            $result['metadata'] = $this->getMetadata($exportData);
        }

        return $result;
    }

    /**
     * Prepare nested structure - organized by columns
     *
     * @param ExportData $exportData The export data
     * @return array
     */
    private function prepareNestedStructure(ExportData $exportData): array
    {
        $header = $exportData->getHeader();
        $columns = $header->getColumns();
        $result = [];

        // Initialize structure
        foreach ($columns as $column) {
            $result[$column->getVarName()] = [];
        }

        // Fill data
        foreach ($exportData as $record) {
            $exportValues = $record->getValues();
            foreach ($exportValues as $index => $exportValue) {
                $column = $columns[$index] ?? null;
                if ($column) {
                    $result[$column->getVarName()][] = $this->formatValueForJson($exportValue);
                }
            }
        }

        return $this->includeMetadata ? $this->addMetadata($result, $exportData) : $result;
    }

    /**
     * Process a single ExportRecord and return formatted data
     *
     * @param ExportRecord $record The export record
     * @param array $columns Array of ExportColumn objects for mapping
     * @return array Associative array of formatted values
     */
    private function processExportRecord(ExportRecord $record, array $columns): array
    {
        $recordData = [];
        $exportValues = $record->getValues();

        // Map values to column names using the provided columns
        foreach ($exportValues as $index => $exportValue) {
            $column = $columns[$index] ?? null;
            if ($column) {
                $columnName = $column->getVarName();
                $recordData[$columnName] = $this->formatValueForJson($exportValue);
            } else {
                // Fallback to numeric index if column not found
                $recordData["col_$index"] = $this->formatValueForJson($exportValue);
            }
        }

        return $recordData;
    }

    /**
     * Format an ExportValue for JSON output
     *
     * @param ExportValue $exportValue The export value
     * @return mixed The formatted value
     */
    private function formatValueForJson(ExportValue $exportValue)
    {
        $value = $exportValue->getValue();
        $type = $exportValue->getType();

        // Handle null values
        if ($value === null) {
            return null;
        }

        // Format based on type
        switch ($type) {
            case ExportValue::TYPE_BOOLEAN:
                return (bool) $value;

            case ExportValue::TYPE_INTEGER:
                return (int) $value;

            case ExportValue::TYPE_FLOAT:
                return (float) $value;

            case ExportValue::TYPE_DATE:
            case ExportValue::TYPE_DATETIME:
                return $this->formatDateValue($value, $type);

            case ExportValue::TYPE_STRING:
            default:
                return (string) $value;
        }
    }

    /**
     * Format date value for JSON
     *
     * @param mixed $value The date value
     * @param string $type The value type
     * @return string Formatted date string (ISO 8601)
     */
    private function formatDateValue($value, string $type): string
    {
        try {
            if ($value instanceof \DateTimeInterface) {
                return $type === ExportValue::TYPE_DATE
                    ? $value->format('Y-m-d')
                    : $value->format('c'); // ISO 8601 format
            } elseif (is_string($value) && strtotime($value) !== false) {
                $dateTime = new \DateTime($value);
                return $type === ExportValue::TYPE_DATE
                    ? $dateTime->format('Y-m-d')
                    : $dateTime->format('c'); // ISO 8601 format
            }
        } catch (\Exception $e) {
            // If date formatting fails, return original value
        }

        return (string) $value;
    }

    /**
     * Add metadata to the data structure
     *
     * @param array $data The data array
     * @param ExportData $exportData The export data
     * @return array Data with metadata
     */
    private function addMetadata(array $data, ExportData $exportData): array
    {
        return [
            'data' => $data,
            'metadata' => $this->getMetadata($exportData)
        ];
    }

    /**
     * Get metadata for the export
     *
     * @param ExportData $exportData The export data
     * @return array Metadata array
     */
    private function getMetadata(ExportData $exportData): array
    {
        $header = $exportData->getHeader();
        $columns = $header->getColumns();

        $columnInfo = [];
        foreach ($columns as $column) {
            $columnInfo[] = [
                'name' => $column->getVarName(),
                'label' => $column->getLabel(),
                'type' => $column->getType()
            ];
        }

        // Count records without consuming the iterator
        $recordCount = 0;
        foreach ($exportData as $record) {
            $recordCount++;
        }

        return [
            'export_date' => date('c'), // ISO 8601 format
            'record_count' => $recordCount,
            'columns' => $columnInfo,
            'format' => 'json',
            'structure' => $this->outputStructure
        ];
    }

    /**
     * Get JSON encoding options
     *
     * @param array $options Export options
     * @return int JSON encoding flags
     */
    private function getJsonOptions(array $options): int
    {
        $jsonOptions = 0;

        // Pretty print by default
        $prettyPrint = $options['pretty_print'] ??
                      $this->configuration['pretty_print'] ??
                      true;

        if ($prettyPrint) {
            $jsonOptions |= JSON_PRETTY_PRINT;
        }

        // Unescaped unicode
        $unescapedUnicode = $options['unescaped_unicode'] ??
                           $this->configuration['unescaped_unicode'] ??
                           true;

        if ($unescapedUnicode) {
            $jsonOptions |= JSON_UNESCAPED_UNICODE;
        }

        // Unescaped slashes
        $unescapedSlashes = $options['unescaped_slashes'] ??
                           $this->configuration['unescaped_slashes'] ??
                           true;

        if ($unescapedSlashes) {
            $jsonOptions |= JSON_UNESCAPED_SLASHES;
        }

        // Preserve zero fraction
        $preserveZeroFraction = $options['preserve_zero_fraction'] ??
                               $this->configuration['preserve_zero_fraction'] ??
                               false;

        if ($preserveZeroFraction && defined('JSON_PRESERVE_ZERO_FRACTION')) {
            $jsonOptions |= JSON_PRESERVE_ZERO_FRACTION;
        }

        // Numeric check
        $numericCheck = $options['numeric_check'] ??
                       $this->configuration['numeric_check'] ??
                       false;

        if ($numericCheck) {
            $jsonOptions |= JSON_NUMERIC_CHECK;
        }

        return $jsonOptions;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSupportedExtensions(): array
    {
        return ['json'];
    }

    /**
     * {@inheritdoc}
     */
    public function getMimeType(string $format = null): string
    {
        // JSON only supports one format, so format parameter is ignored
        return 'application/json';
    }

    /**
     * {@inheritdoc}
     */
    public function getDefaultExtension(): string
    {
        return 'json';
    }

    /**
     * {@inheritdoc}
     */
    public static function supportsFormat(string $format): bool
    {
        return in_array(strtolower($format), static::getSupportedExtensions());
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatName(): string
    {
        return 'json';
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatOptions(): array
    {
        return [
            'output_structure' => [
                'type' => 'select',
                'label' => 'Output Structure',
                'options' => [
                    'array' => 'Array of Objects (default)',
                    'object' => 'Single Object with Data Array',
                    'nested' => 'Nested by Columns'
                ],
                'default' => 'array'
            ],
            'pretty_print' => [
                'type' => 'checkbox',
                'label' => 'Pretty Print',
                'default' => true,
                'description' => 'Format JSON with indentation for readability'
            ],
            'include_metadata' => [
                'type' => 'checkbox',
                'label' => 'Include Metadata',
                'default' => false,
                'description' => 'Include export metadata (columns, date, etc.)'
            ],
            'unescaped_unicode' => [
                'type' => 'checkbox',
                'label' => 'Unescaped Unicode',
                'default' => true,
                'description' => 'Do not escape Unicode characters'
            ],
            'unescaped_slashes' => [
                'type' => 'checkbox',
                'label' => 'Unescaped Slashes',
                'default' => true,
                'description' => 'Do not escape forward slashes'
            ],
            'numeric_check' => [
                'type' => 'checkbox',
                'label' => 'Numeric Check',
                'default' => false,
                'description' => 'Encode numeric strings as numbers'
            ]
        ];
    }
}
