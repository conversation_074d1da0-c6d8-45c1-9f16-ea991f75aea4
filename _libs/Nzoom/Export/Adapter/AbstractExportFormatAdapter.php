<?php

namespace Nzoom\Export\Adapter;

use Nzoom\I18n\I18nAwareTrait;

/**
 * Abstract base class for export format adapters
 *
 * Provides common functionality shared across different export format adapters,
 * including configuration management, error handling, and file validation.
 */
abstract class AbstractExportFormatAdapter implements ExportFormatAdapterInterface
{
    use I18nAwareTrait;

    /**
     * Application registry
     */
    protected \Registry $registry;

    /**
     * Module name for export context
     */
    protected string $module;

    /**
     * Controller name for export context
     */
    protected string $controller;

    /**
     * Adapter configuration options
     */
    protected array $configuration = [];

    /**
     * Create a new export format adapter
     *
     * @param \Registry $registry Application registry
     * @param string $module Module name for export context
     * @param string $controller Controller name for export context
     */
    public function __construct(\Registry $registry, string $module, string $controller)
    {
        $this->registry = $registry;
        $this->module = $module;
        $this->controller = $controller;

        if (isset($registry['translater'])) {
            $this->setTranslator($registry['translater']);
        }
    }

    /**
     * Set adapter configuration options
     *
     * @param array $config Configuration options to merge with existing
     * @return ExportFormatAdapterInterface For method chaining
     */
    public function setConfiguration(array $config): ExportFormatAdapterInterface
    {
        $this->configuration = array_merge($this->configuration, $config);
        return $this;
    }

    /**
     * Get export filename with proper extension
     *
     * @param string|array|null $prefix Filename prefix
     * @param string|null $extension File extension without dot (uses default if null)
     * @return string Filename
     */
    protected function getExportFilename($prefix, string $extension = null): string
    {
        if ($extension === null) {
            $extension = $this->getDefaultExtension();
        }

        // Handle array or null cases
        if (is_array($prefix)) {
            $prefix = $prefix[0] ?? '';
        }

        if (empty($prefix)) {
            $prefix = strtolower($this->module . '_' . $this->controller . '_export_' . date('Y-m-d_H-i-s'));
        }

        // Add extension if not present
        if (substr($prefix, -(strlen($extension) + 1)) !== '.' . $extension) {
            $prefix .= '.' . $extension;
        }

        return $prefix;
    }

    /**
     * Send HTTP headers for file download
     *
     * @param string $filename Filename
     * @param string|null $contentType Content type (uses default if null)
     * @return void
     */
    protected function sendHeaders(string $filename, string $contentType = null): void
    {
        if ($contentType === null) {
            $contentType = $this->getMimeType();
        }

        // Clean any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Send headers
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Type: ' . $contentType);
        header('Content-Transfer-Encoding: binary');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Expires: 0');
    }

    /**
     * Handle export error
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code to use (default: 400)
     * @return void
     */
    protected function handleExportError(string $message, int $statusCode = 400): void
    {
        // Log the error using General::log
        \General::log($this->registry, 'export_error', 'Export error (' . $this->getFormatName() . '): ' . $message);

        // Set error message in registry for AJAX response
        $this->registry->set('ajax_result', json_encode([
            'error' => $message,
            'status' => 'error',
            'format' => $this->getFormatName(),
            'timestamp' => date('Y-m-d H:i:s')
        ]), true);

        // If this is an AJAX request, send appropriate headers
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

            http_response_code($statusCode);
            header('Content-Type: application/json');
            echo $this->registry->get('ajax_result');
            exit;
        }

        // For non-AJAX requests, we'll let the controller handle the response
    }

    /**
     * Get record headers
     *
     * @param mixed $record Record to extract headers from
     * @return array Array of headers
     */
    protected function getRecordHeaders($record): array
    {
        $headers = [];

        if (method_exists($record, 'getAll')) {
            $data = $record->getAll();
            $headers = array_keys($data);
        } elseif (is_object($record) && method_exists($record, 'getVars')) {
            $vars = $record->getVars();
            $headers = array_keys($vars);
        } elseif (is_array($record)) {
            $headers = array_keys($record);
        }

        return $headers;
    }

    /**
     * {@inheritdoc}
     */
    public function getFormatOptions(): array
    {
        return [];
    }

    /**
     * Validate file parameter and prepare save target
     *
     * @param string|resource $file The file path, PHP stream wrapper, or file pointer
     * @return string|resource The validated save target
     * @throws \Exception If the file parameter is invalid
     */
    protected function validateAndPrepareSaveTarget($file)
    {
        if (is_string($file)) {
            // Handle string input (file path or PHP stream wrapper)
            return $this->validateStringTarget($file);
        } elseif (is_resource($file)) {
            // Handle file pointer
            return $this->validateFilePointer($file);
        } else {
            throw new \Exception('File parameter must be either a string (file path/stream wrapper) or a resource (file pointer)');
        }
    }

    /**
     * Validate string target (file path or PHP stream wrapper)
     *
     * @param string $target The file path or stream wrapper to validate
     * @return string The validated target
     * @throws \Exception If the target is invalid
     */
    private function validateStringTarget(string $target): string
    {
        // Check if it's a PHP stream wrapper
        if (strpos($target, '://') !== false) {
            return $this->validatePhpStreamWrapper($target);
        } else {
            // Handle as regular file path
            return $this->validateFilePath($target);
        }
    }

    /**
     * Validate PHP stream wrapper
     *
     * @param string $wrapper The stream wrapper to validate
     * @return string The validated wrapper
     * @throws \Exception If the wrapper is invalid or not supported
     */
    private function validatePhpStreamWrapper(string $wrapper): string
    {
        $wrapper = strtolower($wrapper);

        // List of supported PHP stream wrappers for export
        $supportedWrappers = [
            'php://output',
            'php://memory',
            'php://temp',
            'php://stdout',
            'php://stderr'
        ];

        // Check for php://temp with parameters (e.g., php://temp/maxmemory:1048576)
        if (strpos($wrapper, 'php://temp') === 0) {
            return $wrapper; // Allow php://temp with parameters
        }

        // Check for exact matches
        if (in_array($wrapper, $supportedWrappers)) {
            return $wrapper;
        }

        // Check for other registered stream wrappers
        $registeredWrappers = stream_get_wrappers();
        $scheme = parse_url($wrapper, PHP_URL_SCHEME);

        if ($scheme && in_array($scheme, $registeredWrappers)) {
            // It's a registered wrapper, allow it but warn about potential issues
            return $wrapper;
        }

        throw new \Exception("Unsupported stream wrapper: {$wrapper}. Supported wrappers: " . implode(', ', $supportedWrappers));
    }

    /**
     * Validate regular file path
     *
     * @param string $filePath The file path to validate
     * @return string The validated file path
     * @throws \Exception If the file path is invalid
     */
    private function validateFilePath(string $filePath): string
    {
        $directory = dirname($filePath);

        if (!is_dir($directory)) {
            throw new \Exception("Directory does not exist: {$directory}");
        }

        if (!is_writable($directory)) {
            throw new \Exception("Directory is not writable: {$directory}");
        }

        return $filePath;
    }

    /**
     * Validate file pointer
     *
     * @param resource $filePointer The file pointer to validate
     * @return resource The validated file pointer
     * @throws \Exception If the file pointer is invalid
     */
    private function validateFilePointer($filePointer)
    {
        if (!is_resource($filePointer)) {
            throw new \Exception('Invalid file pointer provided');
        }

        $resourceType = get_resource_type($filePointer);
        if ($resourceType !== 'stream') {
            throw new \Exception("Invalid resource type: {$resourceType}. Expected 'stream'");
        }

        // Check if the stream is writable
        $meta = stream_get_meta_data($filePointer);
        $mode = $meta['mode'] ?? '';

        if (!preg_match('/[wa+]/', $mode)) {
            throw new \Exception("File pointer is not writable. Mode: {$mode}");
        }

        return $filePointer;
    }
}
