###################################################################################
### SQL nZoom Specific Updates - Оптикс ООД (https://optexim.n-zoom.com/) ###
###################################################################################

######################################################################################
# 2021-06-11 - Added production_intended_quantity to the production diary

# Added production_intended_quantity to the production diary
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES 
(7399, 'Document', 4, 'production_intended_quantity', 'text', 'text', 1, 1, 'text_align := right', 'js_filter := insertOnlyPositiveIntegers', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1048, 30, '', '', '');
 INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES 
 (7399, 'label', 'Отчет за', 'bg'),
 (7399, 'back_label', 'бр.', 'bg');

########################################################################
# 2021-09-02 - Added power roles settings for 'hr_employees_free_days_left' report

# Added power roles settings for 'hr_employees_free_days_left' report
UPDATE reports SET settings = REPLACE(`settings`, 'powerroles :=\r\n', 'powerroles := 2,3\r\n')
WHERE type = 'hr_employees_free_days_left' AND settings LIKE '%powerroles :=\r\n%';

########################################################################
# 2021-04-01 - Deleted enormous useless data of prices changes (actually no changes recorded)
#            - Deleted enormous useless history records of Automatic System (-1) with no audit
#            - Added prices as auditable fields for nomenclatures

# Deleted enormous useless data of prices changes (actually no changes recorded)
DELETE FROM nom_cstm WHERE var_id IN (SELECT id FROM _fields_meta WHERE model='Nomenclature' AND `name` IN ('planned_cost_date', 'planned_cost'));

# Deleted enormous useless history records of Automatic System (-1) with no audit
DELETE nh.*
FROM nom_history nh
         JOIN nom n
              ON nh.model_id=n.id AND nh.user_id=-1 AND n.TYPE IN (5,6)
         LEFT JOIN nom_audit na
                   ON nh.h_id=na.parent_id
WHERE na.a_id IS NULL;

# Added prices as auditable fields for nomenclatures
UPDATE `settings`
SET `value`='code, name, tag, sell_price, sell_price_currency, last_delivery_price, last_delivery_price_currency, average_weighted_delivery_price, average_weighted_delivery_price_currency'
WHERE  section='nomenclatures' and name='audit';

########################################################################
# 2022-06-21 - Added setting for batches sort into production dashlet

# Added setting for batches sort into production dashlet
UPDATE dashlets_plugins
SET settings = REPLACE(
        settings,
        '#batch_sort := alphabetical',
        'batch_sort := alphabetical')
WHERE `type`='production'
  AND settings LIKE '%#batch_sort%';

########################################################################
# 2022-12-16 - Added new automation to create incoming handover for unpacking

# Added new automation to create incoming handover for unpacking
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Издаване на ППП за разкомплектоване', 0, NULL, 1, 'documents', NULL, 'action', '51', 'document_type_production_protocol := 5\r\ndocument_type_schedule := 4\r\nfir_system_expedition := 101', 'condition := \'[prev_b_substatus]\' != \'56\'\r\ncondition := \'[b_substatus]\' == \'56\'', 'plugin := createUnpackingProtocol\r\nmethod := createUnpackingProtocol', NULL, 0, 1, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 51 AND automation_type = 'action' AND method LIKE '%createUnpackingProtocol%');
