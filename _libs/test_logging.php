<?php

// Test script to verify the logging functionality works

// Include mock General class
require_once 'tests/Nzoom/TestHelpers/MockGeneral.php';

// Enable test mode
General::enableTestMode();

// Create a mock registry
class MockRegistry {
    private $data = [];
    
    public function set($key, $value) {
        $this->data[$key] = $value;
    }
    
    public function get($key) {
        return $this->data[$key] ?? null;
    }
}

$registry = new MockRegistry();

// Test logging
echo "Testing General::log() functionality...\n";

General::log($registry, 'test_event', 'This is a test log entry');
General::log($registry, 'another_event', 'Another test entry');

$logs = General::getTestLogs();
echo "Number of log entries: " . count($logs) . "\n";

foreach ($logs as $log) {
    echo "Event: {$log['event']}, Extra: {$log['extra']}\n";
}

// Test event checking
if (General::hasTestEvent('test_event')) {
    echo "✓ test_event was logged correctly\n";
} else {
    echo "✗ test_event was not found\n";
}

if (General::hasTestEvent('nonexistent_event')) {
    echo "✗ nonexistent_event should not exist\n";
} else {
    echo "✓ nonexistent_event correctly not found\n";
}

echo "Test completed successfully!\n";
