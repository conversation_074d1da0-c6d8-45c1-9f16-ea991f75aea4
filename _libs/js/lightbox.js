// -----------------------------------------------------------------------------------
//
//    Lightbox v2.05
//    by <PERSON><PERSON> - http://www.lokeshdhakar.com
//    Last Modification: 3/18/11
//
//    For more information, visit:
//    http://lokeshdhakar.com/projects/lightbox2/
//
//    Licensed under the Creative Commons Attribution 2.5 License - http://creativecommons.org/licenses/by/2.5/
//      - Free for use in both personal and commercial projects
//        - Attribution requires leaving author name, author link, and the license info intact.
//
//  Thanks: <PERSON>(uptonic.com), <PERSON><PERSON><PERSON>(quirksmode.com), and <PERSON>(mir.aculo.us) for ideas, libs, and snippets.
//          <PERSON><PERSON> Treguben<PERSON> (arty.name) for cleanup and help in updating to latest ver of proto-aculous.
//
// -----------------------------------------------------------------------------------
/*

    Table of Contents
    -----------------
    Configuration

    Lightbox Class Declaration
    - initialize()
    - updateImageList()
    - start()
    - changeImage()
    - resizeDialogContainer()
    - showImage()
    - updateImageDetails()
    - updateNav()
    - enableKeyboardNav()
    - disableKeyboardNav()
    - keyboardAction()
    - preloadNeighborImages()
    - end()
    - getPageSize

    nZoom functions
    - setMarkupAsProperties
    - addMarkup
    - defineLightboxContentsHeight
    - setWidth
    - setHeight
    - showDialog
    - updateDialog
    - updateCaption
    - activate
    - actions
    - deactivate
    - wrapContent
    - update
    - toggleSelects
    - createRandomId
    - createIframe
    - loadContent
    - validateContent
    - scaleIframe

    Function Calls
    - document.observe()

*/
// -----------------------------------------------------------------------------------

//
//  Configuration
//
LightboxOptions = Object.extend({
    fileLoadingImage:        'images/loading.gif', // image to display while loading

    //fileBottomNavCloseImage: 'images/closelabel.gif', // not used

    icon: 'spacer.gif',    // default icon = no visible image before window caption

    overlayOpacity: 0.6,   // controls transparency of shadow overlay

    animate: true,         // toggles resizing animations

    resizeSpeed: 10,        // controls the speed of the image resizing animations (1=slowest and 10=fastest)

    borderSize: 10,         //if you adjust the padding in the CSS, you will need to update this variable

    // When grouping images this is used to write: Image # of #.
    // Change it for non-English localization
    labelImage: "Image",
    labelOf: "of",

    errorMessage: 'Technical error occurred! Please, contact our support team!'
}, window.LightboxOptions || {});

// -----------------------------------------------------------------------------------

var lightbox = Class.create();
var lb = {};

lightbox.prototype = {
    imageArray: [],
    activeImage: undefined,

    // flag whether lightbox is on or off
    active: false,

    // flag whether any resizing action is due in the future or currently running
    timeoutId: undefined,

    // store params passed to initialize
    params: {},

    // store params stack when multiple lightbox content windows are open in the same lightbox container
    lb_stack: [],

    // we will call an external function, if available, on activation of lightbox
    // in order not to call it every time when lightbox is used
    /** @function ajaxLoadJS */
    ajaxLoadJS: typeof ajaxLoadJS == 'function' ? ajaxLoadJS : function() { return true; },

    // we will call an external function, if available, when loading content with
    // AJAX request from within lightbox, in order to validate response
    /** @function checkAjaxResponse */
    checkAjaxResponse: typeof checkAjaxResponse == 'function' ? checkAjaxResponse : function() { return true; },

    // initialize()
    // Constructor runs on completion of the DOM loading. Calls updateImageList and then
    // the function inserts html at the bottom of the page which is used to display the shadow
    // overlay and the image container.
    //
    initialize: function(opt) {

        if (typeof opt == 'object' && (opt.content || opt.source)) {
            // lightbox is called with an object parameter containing properties to build it from
            // content or source are mandatory parameter
            // if only source is specified, just set a truthy flag for content in order to pass checks
            if (!opt.content) {
                opt.content = '1';
            }
            // set the uniqid of the container at init time
            opt.uniqid = this.createRandomId((opt.content == 'iframe' ? 'pop' : 'div'));

            // assign property to a new object
            this.params = opt;

            // keep stack from global variable when available
            if (lb && lb.lb_stack) {
                this.lb_stack = lb.lb_stack;
            }
        } else {
            // by default lightbox is created without parameters and is bound
            // to respond to certain trigger elements and load images
            this.updateImageList();
        }

        // global flag whether any resizing action is due in the future or currently running
        // when set, value is an identifier of previously set resizing timeout
        this.timeoutId = lb && lb.timeoutId ? lb.timeoutId : undefined;

        // keep same object from property of existing lightbox object (global variable)
        this.keyboardAction = lb && lb.keyboardAction ? lb.keyboardAction : this.keyboardAction.bindAsEventListener(this);

        if (LightboxOptions.resizeSpeed > 10) LightboxOptions.resizeSpeed = 10;
        if (LightboxOptions.resizeSpeed < 1)  LightboxOptions.resizeSpeed = 1;

        // use input parameter with priority, general setting - by default
        this.animate = this.params && typeof this.params.animate != 'undefined' ? this.params.animate : LightboxOptions.animate;

        this.resizeDuration = this.animate ? ((11 - LightboxOptions.resizeSpeed) * 0.15) : 0;
        this.overlayDuration = this.animate ? 0.2 : 0;  // shadow fade in/out duration

        this.addMarkup();

        // make sure we have all the important markup elements as properties of current object
        // this is done asynchronously with a 10 ms delay (defer method)
        var th = this;
        (this.setMarkupAsProperties.bind(th)).defer();
    },

    setMarkupAsProperties: function() {
        var ids =
            'overlay lightbox outerDialogContainer dialogContainer lightboxContents lightboxImage hoverNav prevLink nextLink loadingContainer loadingLink ' +
            'imageDataContainer imageData imageDetails caption numberDisplay bottomNav bottomNavClose ' +
            'dialogHeader dialogIcon dialogCaption dialogClose';
        $w(ids).each((function(id) { this[id] = $(id); }).bind(this));
    },

    addMarkup: function(opt) {

        // When Lightbox starts it will resize itself from 250 by 250 to the current image dimension.
        // If animations are turned off, it will be hidden as to prevent a flicker of a
        // white 250 by 250 box.
        var size = (this.animate ? 250 : 1) + 'px';

        // add markup once in page
        if ($('lightbox')) {

            // just adjust the size
            $('outerDialogContainer').setStyle({ width: size, height: size });

            return;
        }

        // Code inserts html at the bottom of the page that looks similar to this:
        //
        //  <div id="overlay"></div>
        //  <div id="lightbox">
        //      <div id="outerDialogContainer">
        //          <div id="dialogHeader">
        //              <a class="focus-dummy" href="#"></a>
        //              <img id="dialogIcon" src="/nzoom/1.7.0/_libs/themes/Default/images/spacer.gif">
        //              <div id="dialogCaption" class=""></div>
        //              <a id="dialogClose" href="#"></a>
        //          </div>
        //          <div id="dialogContainer">
        //              <div id="lightboxCotents">
        //              </div>
        //              <div style="" id="hoverNav">
        //                  <a href="#" id="prevLink"></a>
        //                  <a href="#" id="nextLink"></a>
        //              </div>
        //              <div id="loadingContainer">
        //                  <a href="#" id="loadingLink">
        //                      /*<img src="images/loading.gif">*/
        //                  </a>
        //              </div>
        //          </div>
        //      </div>
        //      <div id="imageDataContainer">
        //          <div id="imageData">
        //              <div id="imageDetails">
        //                  <span id="caption"></span>
        //                  <span id="numberDisplay"></span>
        //              </div>
        //              /*<div id="bottomNav">
        //                  <a href="#" id="bottomNavClose">
        //                      <img src="images/close.gif">
        //                  </a>
        //              </div>*/
        //          </div>
        //      </div>
        //  </div>


        var objBody = $$('body')[0];

        objBody.appendChild(Builder.node('div',{id:'overlay'}));

        objBody.appendChild(Builder.node('div',{id:'lightbox'}, [
            Builder.node('div',{id:'outerDialogContainer'}, [
                Builder.node('div',{id:'dialogHeader'}, [
                    Builder.node('a',{'class': 'focus-dummy',href:'#'}),
                    Builder.node('img',{id:'dialogIcon', src: env.themeUrl + 'images/' + LightboxOptions.icon}),
                    Builder.node('div',{id:'dialogCaption'}),
                    Builder.node('a',{id:'dialogClose','href':'#'})
                ]),
                Builder.node('div',{id:'dialogContainer'}, [
                    //Builder.node('img',{id:'lightboxImage'}),
                    Builder.node('div',{id:'lightboxContents'}),
                    Builder.node('div',{id:'hoverNav'}, [
                        Builder.node('a',{id:'prevLink', href: '#'}, [ Builder.node('span',{'class':'arrow'}, ['<']) ]),
                        Builder.node('a',{id:'nextLink', href: '#'}, [ Builder.node('span',{'class':'arrow'}, ['>']) ])
                    ]),
                    Builder.node('div',{id:'loadingContainer'},
                        Builder.node('a',{id:'loadingLink', href: '#' }, '...'/*,
                            Builder.node('img', {src: LightboxOptions.fileLoadingImage})*/
                        )
                    )
                ])
            ]),
            Builder.node('div', {id:'imageDataContainer'},
                Builder.node('div',{id:'imageData'}, [
                    Builder.node('div',{id:'imageDetails'}, [
                        Builder.node('span',{id:'caption'}),
                        Builder.node('span',{id:'numberDisplay'})
                    ])/*,
                    Builder.node('div',{id:'bottomNav'},
                        Builder.node('a',{id:'bottomNavClose', href: '#' },
                            Builder.node('img', { src: LightboxOptions.fileBottomNavCloseImage })
                        )
                    )*/
                ])
            )
        ]));

        $('overlay').hide();
        $('lightbox').hide();
        this.setModal(this.params.isModal || false);
        //$('overlay').hide().observe('click', (function() { this.end(); }).bind(this));
        //$('lightbox').hide().observe('click', (function(event) { if (event.element().id == 'lightbox') this.end(); }).bind(this));
        $('outerDialogContainer').setStyle({ width: size, height: size });
        $('prevLink').observe('click', (function(event) { event.stop(); this.changeImage(this.activeImage - 1); }).bindAsEventListener(this));
        $('nextLink').observe('click', (function(event) { event.stop(); this.changeImage(this.activeImage + 1); }).bindAsEventListener(this));
        $('loadingLink').observe('click', (function(event) { event.stop(); this.end(); }).bind(this));
        //$('bottomNavClose').observe('click', (function(event) { event.stop(); this.end(); }).bind(this));

        // header elements
        $('dialogClose').observe('click', (function(event) { event.stop(); this.end(); }).bind(this));
    },

    //
    // updateImageList()
    // Loops through anchor tags looking for 'lightbox' references and applies onclick
    // events to appropriate links. You can rerun after dynamically adding images w/ajax.
    //
    updateImageList: function() {
        // this functionality causes some trouble when closing iframe popup
        // from lightbox in IE with updateParentAutocomplete so we will not run it
        // as it is not used now at all
        if (window !== window.parent) {
            return;
        }

        this.updateImageList = Prototype.emptyFunction;

        document.observe('click', (function(event) {
            var target = event.findElement('a[rel^=lightbox]') || event.findElement('area[rel^=lightbox]');
            if (target) {
                event.stop();
                this.start(target);
            }
        }).bind(this));
    },

    //
    //  start()
    //  Display overlay and lightbox. If image is part of a set, add siblings to imageArray.
    //
    start: function(imageLink) {

        this.toggleSelects('hidden');

        // display overlay immediately
        this.overlay.setStyle({display: '', opacity: LightboxOptions.overlayOpacity});
        // overlay fade in
        //new Effect.Appear(this.overlay, { duration: this.overlayDuration, from: 0.0, to: LightboxOptions.overlayOpacity });
        // we will add an Appear effect that does nothing but makes sure that overlay stays visible in case previous deactivation has queued a Fade effect
        new Effect.Appear(this.overlay, { duration: this.overlayDuration, from: LightboxOptions.overlayOpacity, to: LightboxOptions.overlayOpacity });

        this.imageArray = [];
        var imageNum = 0;

        if (typeof imageLink == 'object' && imageLink.tagName == 'A') {
            // lightbox is opened for image preview
            if ((imageLink.getAttribute("rel") == 'lightbox')) {
                // if image is NOT part of a set, add single image to imageArray
                this.imageArray.push([imageLink.href, imageLink.title]);
            } else {
                // if image is part of a set..
                this.imageArray =
                    $$(imageLink.tagName + '[href][rel="' + imageLink.rel + '"]').
                    collect(function(anchor) { return [anchor.href, anchor.title]; }).
                    uniq();

                while (this.imageArray[imageNum][0] != imageLink.href) { imageNum++; }
            }
        }

        // calculate top and left offset for the lightbox
        this.positionLightbox();

        // overlay and lightbox are visible, set as active
        this.active = true;

        // add condition when to execute
        if (typeof imageLink == 'object' && imageLink.tagName == 'A') {
            this.changeImage(imageNum);
        }
    },

    //
    // calculate top and left offset for the lightbox
    //
    positionLightbox: function() {
        var arrayPageScroll = document.viewport.getScrollOffsets();
        var lightboxTop = arrayPageScroll[1] + (document.viewport.getHeight() / 10);
        // custom y-position
        if (this.params.yPos) {
            lightboxTop = this.params.yPos;
        }
        var lightboxLeft = arrayPageScroll[0];
        this.lightbox.setStyle({ top: lightboxTop + 'px', left: lightboxLeft + 'px' }).show();
        // custom x-position
        if (this.params.xPos) {
            this.outerDialogContainer.setStyle({left: (this.params.xPos-(this.lightbox.getWidth()-this.outerDialogContainer.getWidth())/2)+'px'});
        }
    },

    //
    //  changeImage()
    //  Hide most elements and preload image in preparation for resizing image container.
    //
    changeImage: function(imageNum) {

        this.activeImage = imageNum; // update global var

        // hide elements during transition
        if (this.animate) this.loadingContainer.show();

        this.lightboxContents.hide();
        this.hoverNav.hide();
        this.prevLink.hide();
        this.nextLink.hide();
        // HACK: Opera9 does not currently support scriptaculous opacity and appear fx
        this.imageDataContainer.setStyle({opacity: .0001});
        this.numberDisplay.hide();

        var imgPreloader = new Image();

        // once image is preloaded, resize image container
        imgPreloader.onload = (function() {
            this.lightboxContents.innerHTML = '';
            this.lightboxContents.appendChild(Builder.node('img', {id: 'lightboxImage', src: this.imageArray[this.activeImage][0], width: imgPreloader.width, height: imgPreloader.height}));
            this.lightboxImage = $('lightboxImage');
            //this.lightboxImage.src = this.imageArray[this.activeImage][0];
            /*Bug Fixed by Andy Scott*/
            //this.lightboxImage.width = imgPreloader.width;
            //this.lightboxImage.height = imgPreloader.height;
            /*End of Bug Fix*/
            this.resizeDialogContainer(imgPreloader.width, imgPreloader.height);
        }).bind(this);
        imgPreloader.src = this.imageArray[this.activeImage][0];
    },

    //
    //  resizeDialogContainer()
    // Specified width and height refer to dimensions of the content
    // (lightboxContents and inner content) and do not include header/footer/padding
    // which are added in calculations in the function
    //
    resizeDialogContainer: function(imgWidth, imgHeight) {

        if (imgWidth === '') {
            imgWidth = 'auto';
        }
        if (imgHeight === '') {
            imgHeight = 'auto';
        }

        // clear the height, if present, in order to define it again
        this.lightboxContents.setStyle({height: ''});

        // get current width and height
        var widthCurrent  = this.outerDialogContainer.getWidth();
        var heightCurrent = this.outerDialogContainer.getHeight();

        // get new width and height
        var widthNew  = (imgWidth  + LightboxOptions.borderSize * 2);
        var heightNew = (imgHeight + (LightboxOptions.borderSize * 2 + this.dialogHeader.getHeight()));

        // Resize the lightbox to fit the viewport if necessary
        var arrayPageSize = this.getPageSize();
        var displayRatio;
        if (widthNew > arrayPageSize[0]) {
            displayRatio = widthNew / heightNew;
            widthNew = arrayPageSize[0];
            // if displaying an image we need to keep ratio
            if (typeof this.activeImage == 'number' && this.imageArray.length || typeof this.params.content == 'string' && this.params.content.match(/^<img/)) {
                heightNew = widthNew / displayRatio;
            }
        }

        // scalars based on change from old to new
        var xScale = (widthNew  / widthCurrent)  * 100;
        var yScale = (heightNew / heightCurrent) * 100;

        // calculate size difference between new and old image, and resize if necessary
        var wDiff = widthCurrent - widthNew;
        var hDiff = heightCurrent - heightNew;
        if (isNaN(hDiff)) {
            hDiff = 0;
        }
        if (isNaN(wDiff)) {
            wDiff = 0;
        }

        // change height
        if (hDiff != 0) {
            new Effect.Scale(
                this.outerDialogContainer,
                yScale,
                {
                    scaleX: false,
                    duration: this.resizeDuration,
                    queue: 'front',
                    scaleContent: !this.params.content,
                    afterFinish: function() {
                        this.lightboxContents.setStyle({height: this.defineLightboxContentsHeight()});
                    }.bind(this)
                });
        } else {
            this.lightboxContents.setStyle({height: this.defineLightboxContentsHeight()});
        }
        // change width
        if (wDiff != 0) {
            new Effect.Scale(
                this.outerDialogContainer,
                xScale,
                {
                    scaleY: false,
                    duration: this.resizeDuration,
                    delay: this.resizeDuration,
                    scaleContent: !this.params.content,
                    afterFinish: function() {
                        if (isNaN(imgHeight)) {
                            // remove height, let container expand to content height
                            this.outerDialogContainer.setStyle({height: ''});
                        }
                        if (isNaN(imgWidth)) {
                            // remove width, use default style
                            this.outerDialogContainer.setStyle({width: ''});
                        }
                        this.lightboxContents.setStyle({height: this.defineLightboxContentsHeight()});
                        this.outerDialogContainer.setStyle({overflow: 'visible'});
                        this.loadingContainer.hide();

                    }.bind(this)
                });
        } else {
            if (isNaN(imgHeight)) {
                // remove height, let container expand to content height
                this.outerDialogContainer.setStyle({height: ''});
            }
            if (isNaN(imgWidth)) {
                // remove width, use default style
                this.outerDialogContainer.setStyle({width: ''});
            }
            this.lightboxContents.setStyle({height: this.defineLightboxContentsHeight()});
            this.outerDialogContainer.setStyle({overflow: 'visible'});
            this.loadingContainer.hide();
        }

        this.outerDialogContainer.setStyle({backgroundColor: this.params.backgroundColor || ''});

        // if showing dialog
        if (this.params.content || !this.imageArray.length) {
            return;
        }

        // if new and old image are same size and no scaling transition is necessary,
        // do a quick pause to prevent image flicker.
        var timeout = 0;
        if ((hDiff == 0) && (wDiff == 0)) {
            timeout = 100;
            if (Prototype.Browser.IE) timeout = 250;
        }

        (function() {
            // show image
            this.prevLink.setStyle({ height: imgHeight + 'px' });
            this.nextLink.setStyle({ height: imgHeight + 'px' });
            this.imageDataContainer.show().setStyle({ width: widthNew + 'px' });

            this.showImage();
        }).bind(this).delay(timeout / 1000);
    },

    defineLightboxContentsHeight: function() {
        if (this.outerDialogContainer.style.height) {
            return (this.outerDialogContainer.getHeight() - (LightboxOptions.borderSize * 2 + this.dialogHeader.getHeight())) + 'px';
        } else {
            return '100%';
        }
    },

    //
    // setWidth()
    // Change outer container width directly, without resizing animation
    // @param width - content width
    //
    setWidth: function(width) {
        // if set as number, increase with border size and add unit
        if (typeof width == 'number') {
            width = (width + LightboxOptions.borderSize * 2) + 20 + 'px';
        }
        // set formatted width as-is
        this.outerDialogContainer.setStyle({width: width});
        // get calculated width as number and update into object properties
        this.params.width = this.outerDialogContainer.getWidth() - LightboxOptions.borderSize * 2;
    },

    //
    // setHeight()
    // Change outer container height directly, without resizing animation
    // @param height - content height
    //
    setHeight: function(height) {
        // if set as number, increase with border size and add unit
        if (typeof height == 'number') {
            height = (height + LightboxOptions.borderSize * 2 + this.dialogHeader.getHeight()) + 25 + 'px';
        }
        // set formatted height as-is
        this.outerDialogContainer.setStyle({height: height});
        // update content container height
        this.lightboxContents.setStyle({height: this.defineLightboxContentsHeight()});
        // get calculated height as number and update into object properties

        this.params.height = (!height || height == 'auto') ? 'auto' : this.outerDialogContainer.getHeight() - (LightboxOptions.borderSize * 2 + this.dialogHeader.getHeight());
    },

    //
    //  showImage()
    //  Display image and begin preloading neighbors.
    //
    showImage: function() {
        this.loadingContainer.hide();
        new Effect.Appear(this.lightboxContents, {
            duration: this.resizeDuration,
            queue: 'end',
            afterFinish: (function() { this.updateImageDetails(); }).bind(this)
        });
        this.preloadNeighborImages();
    },

    //
    //  updateImageDetails()
    //  Display caption, image number, and bottom nav.
    //
    updateImageDetails: function() {

        this.caption.update(this.imageArray[this.activeImage][1]).show();

        // if image is part of set display 'Image x of x'
        if (this.imageArray.length > 1) {
            this.numberDisplay.update( LightboxOptions.labelImage + ' ' + (this.activeImage + 1) + ' ' + LightboxOptions.labelOf + '  ' + this.imageArray.length).show();
        }

        new Effect.Parallel(
            [
                new Effect.SlideDown(this.imageDataContainer, { sync: true, duration: this.resizeDuration, from: 0.0, to: 1.0 }),
                new Effect.Appear(this.imageDataContainer, { sync: true, duration: this.resizeDuration })
            ],
            {
                duration: this.resizeDuration,
                afterFinish: (function() {
                    // update nav
                    this.updateNav();
                }).bind(this)
            }
        );
    },

    //
    //  showDialog()
    //  Display modal dialog window.
    //
    showDialog: function() {
        new Effect.Appear(this.lightboxContents, {
            duration: this.resizeDuration,
            queue: 'front',
            afterFinish: (function() {
                this.updateDialog();
            }).bind(this)
        });
    },

    //
    //  updateDialog()
    //  Display caption, update overlay size
    //
    updateDialog: function() {
        this.resizeDialogContainer(this.params.width || 500, this.params.height || '');
        // header elements
        this.updateCaption();
        // enable keyboard navigation
        this.enableKeyboardNav();
    },

    updateCaption: function() {
        // update lightbox caption and show it, if hidden
        this.dialogCaption.update(this.params.title).show();
        this.dialogIcon.src = env.themeUrl + 'images/' + (this.params.icon ? this.params.icon : LightboxOptions.icon);
        // adjust caption padding according to icon
        if (!this.params.icon || this.params.icon == LightboxOptions.icon) {
            this.dialogCaption.removeClassName('hasIcon');
        } else {
            this.dialogCaption.addClassName('hasIcon');
        }
    },

    activate: function() {

        this.setMarkupAsProperties();
        // if lightbox is already opened, hide current content
        if (this.lightbox.visible()) {
            // nested lightboxes - one lightbox opens the next one
            if (this.lightboxContents.lastChild) {
                // we cannot move iframe around the DOM because that will cause it being reloaded (i.e. entered data will be lost)
                this.lightboxContents.lastChild.hide();
            }
        } else {
            // clear contents
            this.lightboxContents.innerHTML = '';
        }
        // remove absolute elements
        this.removeAbsoluteContent();
        this.start(false);
        // set focus
        this.lightbox.down('.focus-dummy').focus();
        // show loading image
        if (this.animate) {
            this.loadingContainer.show();
        }

        this.hoverNav.hide();
        this.prevLink.hide();
        this.nextLink.hide();
        this.imageDataContainer.hide();
        this.numberDisplay.hide();

        // if content is present as a string or as a DOM object
        if (typeof this.params.content == 'string') {
            if (this.params.content == 'iframe') {
                // create blank iframe
                this.lightboxContents.appendChild(this.createIframe(this.params.uniqid, this.params.source));
            } else {
                // wrap content in a DIV with random id
                this.lightboxContents.appendChild(this.wrapContent((!this.params.source ? this.params.content : ''), this.params.uniqid));
            }
        } else if (typeof this.params.content == 'object') {
            this.lightboxContents.appendChild(this.params.content);
        }

        // missing parameters or something went wrong, close lightbox
        if (!this.lightboxContents.lastChild) {
            this.deactivate();
            this.displayError();
            return;
        }

        // if content should be fetched from somewhere
        if (this.params.source) {
            if (this.params.source == 'inline') {
                // fetch from existing DOM element
                this.lightboxContents.lastChild.innerHTML = $(this.params.content).innerHTML;
            } else {
                // fetch from source URL and load into specified container
                this.loadContent(this.params.source, this.lightboxContents.lastChild);
            }
        }

        if (this.params.width) {
            if (typeof this.params.width == 'string') {
                this.params.width = parseInt(this.params.width.replace(/[^\d]*$/, ''));
            }
        }
        if (this.params.height) {
            if (typeof this.params.height == 'string') {
                this.params.height = parseInt(this.params.height.replace(/[^\d]*$/, ''));
            }
        }
        if (!this.params.height || isNaN(this.params.height)) {
            this.params.height = 'auto';
        }
        // store original dimensions lightbox was activated with
        this.params.width_init = this.params.width || '';
        this.params.height_init = this.params.height || '';

        // push CURRENT content at the top of the stack so we know where we are on deactivate
        // then pop from stack and decide what to do depending on whether there is anything left
        this.lb_stack.push(this.params);

        // if content is present, otherwise call function after content is received
        if (!(this.params.source && this.params.source != 'inline')) {
            this.showDialog();
        }

        // missing parameters or something went wrong, close lightbox
        if (!this.lightboxContents.lastChild) {
            this.deactivate();
            this.displayError();
            return;
        }

        if (this.lightboxContents.lastChild.innerHTML) {
            // eval javascript
            this.actions();
        }

        // id of current active container
        return this.lightboxContents.lastChild.id;
    },

    /**
     * Actions to perform after current content is loaded into lightbox
     */
    actions: function() {
        try {
            // load scripts
            $(this.lightboxContents.lastChild).select('script').each(this.ajaxLoadJS);

            // perform any other actions, if specified
            if (this.params && this.params.onActivate) {
                if (typeof this.params.onActivate == 'function') {
                    this.params.onActivate.call(this);
                } else if (typeof this.params.onActivate == 'string') {
                    eval(this.params.onActivate);
                }
            }
        } catch (e) {
            //alert(e);
        }
    },

    deactivate: function() {
        this.end();
    },

    displayError: function() {
        alert(LightboxOptions.errorMessage);
    },

    //
    // wrapContent()
    // content is loaded in the same window, add wrapper DIV
    //
    wrapContent: function(content, div_id) {
        var wrapper = document.createElement('DIV');
        wrapper.setAttribute('id', div_id || this.createRandomId('div'));
        $(wrapper).addClassName('lb_content');
        wrapper.innerHTML = content;
        return wrapper;
    },

    //
    // update()
    // Reload content in the same container without reloading lightbox
    //
    update: function(opt) {
        if (this.params && this.params.uniqid && this.lightboxContents && this.lightboxContents.lastChild && this.lightboxContents.lastChild.id == this.params.uniqid) {
            // update params with any passed new values
            if (typeof opt == 'object') {
                for (var p in opt) {
                    this.params[p] = opt[p];
                }
            }
            if (this.lb_stack && this.lb_stack.length && this.lb_stack[this.lb_stack.length - 1].uniqid == this.params.uniqid) {
                // update element at the end of stack
                this.lb_stack[this.lb_stack.length - 1] = this.params;
            } else {
                // something in not right, update the whole stack to just current params
                this.lb_stack = [this.params];
            }

            this.updateCaption();
            this.lightboxContents.lastChild.innerHTML = this.params.content;
            this.actions();

            return this.lightboxContents.lastChild.id;
        } else {
            // lightbox cannot be updated, close it
            this.deactivate();
            this.displayError();
            return;
        }
    },

    //
    //  updateNav()
    //  Display appropriate previous and next hover navigation.
    //
    updateNav: function() {

        this.hoverNav.show();

        // if not first image in set, display prev image button
        if (this.activeImage > 0) this.prevLink.show();

        // if not last image in set, display next image button
        if (this.activeImage < (this.imageArray.length - 1)) this.nextLink.show();

        this.enableKeyboardNav();
    },

    //
    //  enableKeyboardNav()
    //
    enableKeyboardNav: function() {
        document.observe('keydown', this.keyboardAction);
    },

    //
    //  disableKeyboardNav()
    //
    disableKeyboardNav: function() {
        document.stopObserving('keydown', this.keyboardAction);
    },

    //
    //  keyboardAction()
    //
    keyboardAction: function(event) {
        var keycode = event.keyCode;

        var escapeKey;
        if (event.DOM_VK_ESCAPE) {  // mozilla
            escapeKey = event.DOM_VK_ESCAPE;
        } else { // ie
            escapeKey = 27;
        }

        var key = String.fromCharCode(keycode).toLowerCase();

        if (keycode == escapeKey) {
            // close lightbox
            this.end();
            // do not pass event further
            event.stop();
        } else if (this.activeImage != undefined) {
            // shortcuts for image (gallery) lightbox only
            if (key.match(/x|o|c/)) { // close lightbox
                this.end();
            } else if ((key == 'p') || (keycode == 37)) { // display previous image
                if (this.activeImage != 0) {
                    this.disableKeyboardNav();
                    this.changeImage(this.activeImage - 1);
                }
            } else if ((key == 'n') || (keycode == 39)) { // display next image
                if (this.activeImage != (this.imageArray.length - 1)) {
                    this.disableKeyboardNav();
                    this.changeImage(this.activeImage + 1);
                }
            }
        }
    },

    //
    //  preloadNeighborImages()
    //  Preload previous and next images.
    //
    preloadNeighborImages: function() {
        var preloadNextImage, preloadPrevImage;
        if (this.imageArray.length > this.activeImage + 1) {
            preloadNextImage = new Image();
            preloadNextImage.src = this.imageArray[this.activeImage + 1][0];
        }
        if (this.activeImage > 0) {
            preloadPrevImage = new Image();
            preloadPrevImage.src = this.imageArray[this.activeImage - 1][0];
        }
    },

    //
    //  end()
    //
    end: function() {

        if (this.lb_stack && this.lb_stack.length) {
            // remove current content from lightbox and stack
            this.params = this.lb_stack.pop();

            // if there is a custom close handler
            if (this.params.closeHandler) {
                if (typeof this.params.closeHandler == 'function') {
                    this.params.closeHandler.call(this);
                } else if (typeof this.params.closeHandler == 'string') {
                    eval(this.params.closeHandler);
                }
            }

            var current_content_id = this.params.uniqid,
                current_container = this.lightboxContents.down('#' + current_content_id);

            if (current_container) {
                // if closed content is in an iframe, remove keydown observer
                if (current_container.tagName == 'IFRAME') {
                    // technically current_container.contentWindow.parent.lb.keyboardAction and this.keyboardAction reference the same object
                    try {
                        Event.stopObserving(current_container.contentWindow, 'keydown', this.keyboardAction);
                    } catch (e) {
                        //
                    }
                }
                // remove absolute elements
                this.removeAbsoluteContent();
                // remove current content container from lightbox contents div
                $(current_container).remove();
            }

            // display next content from stack and stay in lightbox
            if (this.lb_stack.length && this.lightboxContents.childNodes.length) {
                // set params from end of stack into current object (this), also set into global lb object
                lb.params = this.params = this.lb_stack[this.lb_stack.length - 1];
                current_content_id = this.params.uniqid;
                current_container = this.lightboxContents.down('#' + current_content_id);
                if (current_container) {
                    // display next content from stack
                    current_container.show();

                    if (current_container.tagName == 'IFRAME') {
                        current_container = current_container.contentWindow.document.body;
                    } else {
                        // show all elements in current lightbox content that were hidden by upper lightbox
                        this.toggleSelects('hidden', current_container);
                    }

                    var title_element = current_container.down('h1');
                    if (title_element) {
                        // define lightbox title if not present as parameter
                        if (!this.params.title) {
                            this.params.title = title_element.innerText;
                        }
                        if (!this.params.icon && title_element.down('img')) {
                            this.params.icon = title_element.down('img').src.replace(/.*\//, '');
                        }
                    }

                    // adjust lightbox according to params of currently active content
                    this.loadingContainer.show();
                    this.outerDialogContainer.setStyle({ overflow: 'hidden' });
                    this.updateDialog();

                    // stay in the lightbox
                    return;
                }
            }
        }

        // clear properties in case lightbox gets reused directly
        // removing properties will remove them in global "lb" object as well
        // (assigning a new object to params will not do that)
        for (var p in this.params) {
            delete this.params[p];
        }

        // clean up html
        this.lightboxContents.innerHTML = this.dialogCaption.innerHTML = '';

        // restore outer container size to initial
        var size = (this.animate ? 250 : 1) + 'px';
        $('outerDialogContainer').setStyle({ width: size, height: size, backgroundColor: '', overflow: '', left: '' });
        $('lightboxContents').setStyle({ height: '' });

        this.disableKeyboardNav();
        this.lightbox.hide();
        new Effect.Fade(this.overlay, { duration: this.overlayDuration });
        this.toggleSelects('visible');

        // overlay and lightbox are not visible, set as inactive
        this.active = false;
        // this and the global lb object are not the same if method is called
        // from the registered bound observers and not directly on the lb object
        // so update flag there as well
        if (lb.active == true) {
            lb.active = false;
        }
    },

    /**
     * Remove absolutely-positioned content which is not located in lightbox when
     * current lightbox content is opened/closed/hidden.
     */
    removeAbsoluteContent: function() {
        // close Zapatec.Calendar if opened
        if (typeof Zapatec == 'function' && Zapatec.Calendar && window.calendar &&
        typeof window.calendar.params == 'object') {
            Zapatec.Calendar.prototype.destroy.call(window.calendar);
        }
        // remove overlib popup if visible
        if (typeof cClick == 'function' && typeof over == 'object' && over != null) {
            cClick();
        }
    },

    /**
     * Hide Selects from the page because of IE.
     */
    toggleSelects: function(visibility, container) {
        if (!navigator.userAgent.toLowerCase().match('msie')) {
            return;
        }

        Element.select(container || document, 'select', 'object', 'embed').each(function(node) { node.style.visibility = visibility; });

        if (visibility == 'hidden' && !container) {
            var arrayPageSize = this.getPageSize();
            $('overlay').setStyle({ position: 'absolute', width: arrayPageSize[0] + 'px', height: arrayPageSize[1] + 'px' });
        }
    },

    //
    //  getPageSize()
    //
    getPageSize: function() {

         var xScroll, yScroll;

        if (window.innerHeight && window.scrollMaxY) {
            xScroll = window.innerWidth + window.scrollMaxX;
            yScroll = window.innerHeight + window.scrollMaxY;
        } else if (document.body.scrollHeight > document.body.offsetHeight) { // all but Explorer Mac
            xScroll = document.body.scrollWidth;
            yScroll = document.body.scrollHeight;
        } else { // Explorer Mac...would also work in Explorer 6 Strict, Mozilla and Safari
            xScroll = document.body.offsetWidth;
            yScroll = document.body.offsetHeight;
        }

        var windowWidth, windowHeight;

        if (self.innerHeight) {    // all except Explorer
            if (document.documentElement.clientWidth) {
                windowWidth = document.documentElement.clientWidth;
            } else {
                windowWidth = self.innerWidth;
            }
            windowHeight = self.innerHeight;
        } else if (document.documentElement && document.documentElement.clientHeight) { // Explorer 6 Strict Mode
            windowWidth = document.documentElement.clientWidth;
            windowHeight = document.documentElement.clientHeight;
        } else if (document.body) { // other Explorers
            windowWidth = document.body.clientWidth;
            windowHeight = document.body.clientHeight;
        }

        // add the horizontal scroll offset, if any
        var arrayPageScroll = document.viewport.getScrollOffsets();
        if (arrayPageScroll[0]) {
            windowWidth += arrayPageScroll[0];
        }

        // for small pages with total height less then height of the viewport
        if (yScroll < windowHeight) {
            pageHeight = windowHeight;
        } else {
            pageHeight = yScroll;
        }

        // for small pages with total width less then width of the viewport
        if (xScroll < windowWidth) {
            pageWidth = xScroll;
        } else {
            pageWidth = windowWidth;
        }

        return [pageWidth, pageHeight];
    },

    createRandomId: function(prefix) {
        var random_id;

        do {
            random_id = (prefix || 'pop').toString() + Math.round(100*Math.random());
        } while (this.lightboxContents && this.lightboxContents.down('#' + random_id))

        return random_id;
    },

    createIframe: function(iframe_id, src) {
        if (!iframe_id) {
            iframe_id = this.createRandomId('pop');
        }
        // iframe already present in document
        if ($(iframe_id)) {
            return $(iframe_id);
        }
        // creates an element
        var iframe;
        try {
            iframe = document.createElement('<iframe name="' + iframe_id + '">');
        } catch (ex) {
            iframe = document.createElement('iframe');
            iframe.name = iframe_id;
        }

        // applies all the required properties of the iframe
        iframe.id = iframe_id;
        iframe.src = "about:blank";
        iframe.style.width = '100%';
        iframe.style.height = '99%';
        iframe.style.borderWidth = '0px';

        return iframe;
    },

    /**
     * If lightbox receives URL as parameter, perform AJAX request to fetch
     * content, then load it into specified target container
     */
    loadContent: function(source, target) {

        new Ajax.Request(
            source,
            {
                method: 'get',
                parameters: '',
                onSuccess: function(t) {
                    // validate response
                    if (!this.validateContent(t.responseText)) {
                        if (t.responseText === '' && t.status === 0) {
                            this.displayError();
                        }
                        return;
                    }

                    // load response text into target
                    if (target.tagName == 'IFRAME') {
                        // validate that content can be loaded into iframe
                        if ((t.getHeader('X-Frame-Options') || '').toLowerCase() == 'deny') {
                            this.handleIframeError(true);
                            return;
                        }

                        var doc = target.contentWindow.document || target.contentDocument;
                        doc.open();
                        doc.write(t.responseText);
                        doc.close();

                        // this will be fired every time the window inside the iframe is loaded from redirects, submits etc.
                        Event.observe(target, 'load', function() {
                            /* this = current iframe */
                            if (!this.parentNode || this.parentNode.id != 'lightboxContents') {
                                return;
                            }

                            var wnd = this.contentWindow,
                                parent_lb = wnd.parent.lb,
                                title_element;

                            try {
                                var open_popup = true;
                                if (wnd.document && wnd.document.body) {
                                    if (wnd.document.body.innerHTML != '') {
                                        open_popup = false;
                                    }
                                    // window has content but it is not what is expected, do not switch to popup mode
                                    if (!wnd.document.body.down) {
                                        throw "window not loaded correctly";
                                    };
                                }
                            } catch (e) {
                                if (parent_lb) {
                                    parent_lb.handleIframeError(open_popup);
                                }
                                return;
                            }

                            title_element = wnd.document.body.down ? wnd.document.body.down('h1') : null;

                            // if "Reload iframe" is selected in browser
                            if (!wnd.document.body.down) {
                                this.src = parent_lb.params.source;
                                return;
                            }

                            // validate loaded HTML
                            if (!parent_lb.validateContent(wnd.document.documentElement.innerHTML)) {
                                if (wnd.document.documentElement.innerHTML === '') {
                                    parent_lb.displayError();
                                }
                                return;
                            }

                            parent_lb.params.title = parent_lb.params.icon = '';

                            if (title_element) {
                                title_element.hide();
                                parent_lb.params.title = title_element.innerText;
                                parent_lb.params.icon = title_element.down('img') ? title_element.down('img').src.replace(/.*\//, '') : '';
                            }

                            // update lightbox caption from params
                            parent_lb.updateCaption();

                            // set focus on the iframe window
                            //wnd.focus();
                            parent_lb.lightbox.down('.focus-dummy').focus();

                            // bind iframe window to lightbox keyboard shortcuts (which are in parent window)
                            Event.observe(wnd, 'keydown', parent_lb.keyboardAction);

                            // adjust lightbox dimensions
                            // add delay (even when animations are off) so that is done after resizing and page scripts have been executed
                            parent_lb.scaleIframe.bind(parent_lb).delay((parent_lb.resizeDuration || 0.1) * 3);
                        });

                    } else {
                        target.innerHTML = t.responseText;

                        // eval javascript
                        this.actions();
                    }

                    // adjust lightbox according to params of currently active content
                    this.outerDialogContainer.setStyle({ overflow: 'hidden' });
                    this.showDialog();
                }.bind(this),
                onFailure: function(t) {
                    // handles response status other than 2XX
                    this.deactivate();
                    this.displayError();
                    //alert('Error ' + t.status + ' -- ' + t.statusText);
                }.bind(this),
                onException: function(t) {
                    this.deactivate();
                    this.displayError();
                    //alert('Error ' + arguments[1]);
                }.bind(this)
            });
    },

    /**
     * Validates loaded content and deactivates lightbox on failure
     *
     * @param {String} responseText - loaded page content
     * @return {Boolean} - true on success, false on failure
     */
    validateContent: function(responseText) {
        if (responseText === '' || !this.checkAjaxResponse(responseText)) {
            this.deactivate();
            return false;
        }
        return true;
    },

    handleIframeError: function(open_popup) {
        var window_params = [this.params.source, this.params.width_init, this.params.height_init];
        this.deactivate();
        if (typeof windowOpen == 'function' && open_popup) {
            // cannot load response because of restrictions => switch to popup mode
            windowOpen.apply(window, window_params);
        } else {
            // response is loaded but it is not what is expected => display error
            this.displayError();
        }
    },

    /**
     * Additional adjustment of dimensions of lightbox containing iframe window
     */
    scaleIframe: function() {
        // get the inner iframe window because function is executed on lightbox object in parent window
        var wnd = window.$$('iframe#' + this.params.uniqid)[0];
        if (wnd) {
            wnd = wnd.contentWindow;
        }
        if (!wnd) {
            return;
        }

        // check if there are any pending effects, ignoring if there is just the fade effect as we don't have to wait for it
        if (wnd.Effect && wnd.Effect.Queue && wnd.Effect.Queue.effects.length && !(wnd.Effect.Queue.effects.length == 1 && wnd.Effect.Queue.effects[0].options.to === 0.0)) {
            if (this.timeoutId) {
                window.clearTimeout(this.timeoutId);
            }
            this.timeoutId = this.scaleIframe.bind(this).delay(this.resizeDuration || 0.1);
            return;
        }

        var doc = wnd.document.documentElement;
        // shrink height if content is less than it, otherwise set to initial value
        // important: params hold the inner content container height (of lightboxContents)
        var container_height = parseInt(this.params.height_init ? this.params.height_init : this.params.height);
        if (!isNaN(container_height)) {
            // temporarily clear container height in order to get scrollHeight of iframe body
            this.setHeight('');

            if (doc.scrollHeight < container_height) {
                container_height = doc.scrollHeight;
            }

            this.params.height = container_height;
            this.setHeight(container_height);
        }
        // increase width if content is wider, otherwise set to initial value
        // important: params hold the inner content container width (of lightboxContents)
        var container_width = parseInt(this.params.width_init ? this.params.width_init : this.params.width);
        if (!isNaN(container_width)) {
            // temporarily clear container width in order to get scrollHeight of iframe body
            this.setWidth('');
            // new width = scroll width + scrollbar, if visible
            var new_width = doc.scrollWidth + ($(this.params.uniqid).offsetWidth - doc.offsetWidth) * (doc.scrollHeight > doc.clientHeight);
            if (container_width < new_width) {
                container_width = new_width;
            }
            this.params.width = container_width;
            this.setWidth(container_width);
        }

        // clear the timeout flag after everything is finished
        this.timeoutId = undefined;
    },

    /**
     * Makes lightbox modal
     *
     * @param {Boolean} isModal - true - modal, false - not modal
     */
    setModal: function(isModal) {
        if (isModal) {
            $('overlay').stopObserving('click');
            $('lightbox').stopObserving('click');
            this.disableKeyboardNav();
        } else {
            $('overlay').observe('click', (function() { this.end(); }).bind(this));
            $('lightbox').observe('click', (function(event) { if (event.element().id == 'lightbox') this.end(); }).bind(this));
        }
    }
}

document.observe('dom:loaded', function () { lb = new lightbox(); });
