/* $Id: fancyblue.css 6262 2007-02-13 00:49:01Z smaxim $ */
@import url("layouts/layout-3d.css");

.calendar {
  border-color: #585858;
  border-width: 1px;
  color: #000;
}

.calendar table {
  border-color: #585858;
  background: url("fancyblue/bg_body.gif") 100% 100%;
}

.calendar .nav {
  background-color: #FFCC66;
}

/* Header part -- contains navigation buttons and day names. */

.calendar .button { /* "<<", "<", ">", ">>" buttons have this class */
  border-color: #696969 #CFAB1E #000000 #2E2E2E;
  color: #000000;
  background: #d9d2c9 url("fan1cyblue/bg_button.gif") 100% 100%;
  border-top:2px solid #f2c823;
}

.calendar thead .title { /* This holds the current "month, year" */
  border-color: #737373 #400000 #000000 #2E2E2E;
  background: url("fancyblue/bg_wn.gif") 100% 100%;
  color: #fff;
}

.calendar thead .headrow { /* Row <TR> containing navigation buttons */

}

.calendar thead .daynames { /* Row <TR> containing the day names */

}

.calendar thead .name { /* Cells <TD> containing the day names */
  border-color: #000;
  background-color: #6A92A8;
  color: #ffffff;
}

.calendar thead .weekend { /* How a weekend day name shows in header */
  color: #f00;
}

.calendar thead .hilite { /* How do the buttons in header appear when hover */
  border-color: #696969 #CFAB1E #000000 #2E2E2E;
  background-color: #B1783E;
  color: #000000;
  background: url("fancyblue/bg_button_hover.gif") 100% 100%;
}

.calendar thead .active { /* Active (pressed) buttons in header */
  padding: 2px 0px 0px 2px;
  border-color: #404040 #d4d0c8 #d4d0c8 #404040;
  background-color: #FF0303;
}

/* The body part -- contains all the days in month. */

.calendar tbody .day { /* Cells <TD> containing month days dates */

}

.calendar tbody .day.othermonth {
  color: #888;
}

.calendar tbody .day.othermonth.oweekend {
  color: #e88;
}

.calendar table .wn {
  border-color: #000000;
  background-color: #6A92A8;
  color: #ffffff
}

.calendar tbody .rowhilite td {
  background-color: #A5C4D5;
}

.calendar tbody .rowhilite td.wn {
  background: url("fancyblue/bg_wn.gif") 100% 100%;
}

.calendar tbody td.hilite { /* Hovered cells <TD> */
  border-color: #F9EAD5 #675B4D #9C8A74 #F7E2C6;
  background-color: #A1B3BC;
}

.calendar tbody td.active { /* Active (pressed) cells <TD> */
  border-color: #404040 #d4d0c8 #d4d0c8 #404040;
}

.calendar tbody td.selected { /* Cell showing selected date */
  border-color: #404040 #d4d0c8 #d4d0c8 #404040;
  background-color: #FFFBEF;
  color: #000000;
}

.calendar tbody td.weekend { /* Cells showing weekend days */
  color: #f00;
}

.calendar tbody td.today { /* Cell showing today date */
  color: #000000;
}

.calendar tbody .disabled { 
  color: #999; 
}

.calendar tbody .emptycell { /* Empty cells (the best is to hide them) */
}

.calendar tbody .emptyrow { /* Empty row (some months need less than 6 rows) */
}

/* The footer part -- status bar and "Close" button */

.calendar tfoot .footrow { /* The <TR> in footer (only one right now) */
}

.calendar tfoot .ttip { /* Tooltip (status bar) cell <TD> */
  border-color:  #585858 #585858 #585858 #585858;
  background: url("fancyblue/bg_wn.gif") 100% 100%;
  color: #fff;
}

.calendar tfoot .hilite { /* Hover style for buttons in footer */
  border-color: #404040 #d4d0c8 #d4d0c8 #404040;
  background-color: #e4e0d8;
}

.calendar tfoot .active { /* Active (pressed) style for buttons in footer */
  border-color: #000 #fff #fff #000;
}

/* Combo boxes (menus that display months/years for direct selection) */

.calendar .combo {
  border-color: #FFC46C #6B4000 #A26100 #FFAE35;
  background-color: #D9D2C9;
  color: #000000;
}

.calendar .combo .active {
  background-color: #FFFBEF;
  border-color: #404040 #d4d0c8 #d4d0c8 #404040;
  color: #000000;
}

.calendar .combo .hilite {
  border-color: #E4BF8F #573B1B #855A29 #DAA266;
  background-color: #F5DAB7;
  background: url("fancyblue/bg_button_hover.gif") 100% 100%;
  color: #000000;
}

.calendar .month-left-border { /* Divider line between two monthes */
  border-left:1px solid #585858;
}


/* time */

.calendar tfoot tr.time td { border-color: #8f8b7e; }

.calendar tfoot tr.time td.hour,
.calendar tfoot tr.time td.minute { border-color: #cdcabc; }

.calendar tfoot tr.time td.hilite { background-color: #e4e0d8; }
.calendar tfoot tr.time td.active { background-color: #b4b0a8; }
.calendar-time-scroller { border-color: #adaa9c; }
