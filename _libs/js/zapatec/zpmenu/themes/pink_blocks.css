/* $Id: pink_blocks.css 4322 2006-09-04 08:49:33Z shacka $ */
@import url("layout/basic.css");

.zpMenuPink_blocks .zpMenu-vertical-mode .zpMenu-level-1 {
	width:100px;
	border: 1px solid gray;
	background-color: rgb(236,229,232);
}

/* ALL Anchors have NO decorations, all menu item font color */
.zpMenuPink_blocks .zpMenu-top a, .zpMenu-top .zpMenu-label {
	text-decoration:none;
	color:rgb(161,73,90);
}

/* Top menu LABEL */
.zpMenuPink_blocks .zpMenuContainer .zpMenu-label {
	font-weight: bold;
}

/* SUB menu LABEL */
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-label {
	font-weight: normal;
}

/* All menu items in the tree */
.zpMenuPink_blocks .zpMenuContainer .zpMenu-item {
	color:rgb(161,73,90);
	font-family: Verdana, Geneva, Arial, Helvetica, sans-serif;
	font-size: 1em;
	padding-left:0.5em;
	padding-right:2em;
	margin-left: 5px;
	height:20px;
	width:100px;
}


.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item, 
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer {
	/* override top menu padding and margin */
	width: 200px;
}

/* properties a drop down menu */
.zpMenuPink_blocks .zpMenu-horizontal-mode .zpMenu-level-1 {
	float: left;
	border: 1px solid gray;
	padding-top: 2px;
	background: url("pink_blocks/arrow_right.gif") rgb(236,229,232) no-repeat right center;
	margin-left:5px;
}

/* General items that's not a top menu */
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item {
  background: rgb(236,229,232);
	padding-top: 2px;
	margin-left:0px;
	margin-right: 0px;
	margin-top:0px;
	margin-bottom:1px;
	border: 1px solid gray;
}

/* Current selected items in top horizontal menu*/
.zpMenuPink_blocks .zpMenuContainer .zpMenu-item-selected {
	border: 1px solid gray;
	background: url("pink_blocks/arrow_down.gif") rgb(220,195,202)  no-repeat right center;
}


/* SUB menu selected items, labels (no anchor), anchor */
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-selected,
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-selected .zpMenu-label, 
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-selected a {
	color: #000000;
	background-color:rgb(220,195,202);
}

/* The arrow that shows up when there's a sub-menu */
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-collapsed .zpMenu-label {
	background: url("pink_blocks/arrow_right.gif")  no-repeat right center;
}

/* The arrow that shows up when there's a sub-menu and the item is hovered*/
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-expanded .zpMenu-label {
	background: url("pink_blocks/arrow_right.gif")  no-repeat right center;
}

.zpMenuPink_blocks .zpMenuContainer .zpMenu-item-hr,
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
	background:pink;
	height:3px;
}
.zpMenuPink_blocks .zpMenuContainer .zpMenu-item-hr {
	width: 100px;
}
.zpMenuPink_blocks .zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
	width: 200px;
}
